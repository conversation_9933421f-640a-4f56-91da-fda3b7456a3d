import random
import string

# 假设 W 最大为 50，实际使用可根据需求调整，这里先固定用于推导范围
W = 50  
# 每个仓库对应地区数量是 W*10 里的 10（因为 W 是仓库数，每个仓库对应 10 个地区 ）
D_PER_W = 10  
# 每个地区对应客户数量是 3000（customer 表是 W*10*3000，即每个仓库 10 地区、每个地区 3000 客户 ）
C_PER_D = 3000  
# 每个订单对应的 order_line 数量是 5 - 15 条，这里保持原逻辑
OL_PER_ORDER_MIN = 5
OL_PER_ORDER_MAX = 15  


# 生成随机字符串，用于名称等字段
def random_string(length):
    return ''.join(random.choice(string.ascii_letters + string.digits) for _ in range(length))


# 生成随机整数，用于 ID 等字段
def random_int(min_val, max_val):
    return random.randint(min_val, max_val)


# 生成 new_order 事务 SQL
def generate_new_order_sql():
    # warehouse 表最多 W 条记录，所以 w_id 范围 1 - W
    w_id = random_int(1, W)  
    # district 表每个仓库对应 10 个地区，所以 d_id 范围 1 - D_PER_W（即 1 - 10  ）
    d_id = random_int(1, D_PER_W)  
    # customer 表每个地区有 3000 客户，所以 c_id 范围 1 - C_PER_D（即 1 - 3000  ）
    c_id = random_int(1, C_PER_D)  
    # orders 表 o_id 这里简单用随机，只要不冲突逻辑上都行，先保持较宽泛范围
    o_id = random_int(1000, 999999)  
    o_entry_d = "2025-08-06 12:00:00"  
    o_carrier_id = random_int(1, 5)
    # 每个订单随机生成 5 - 15 条 order_line 记录，所以 ol 数量在这范围
    o_ol_cnt = random_int(OL_PER_ORDER_MIN, OL_PER_ORDER_MAX)  
    o_all_local = random.choice([True, False])
    # item 表有 10 万条记录，i_id 范围合理即可，这里假设 1 - 100000
    ol_i_id = random_int(1, 100000)  
    # 仓库最多 W 个，所以供应仓库 ol_supply_w_id 范围 1 - W
    ol_supply_w_id = random_int(1, W)  
    ol_delivery_d = "2025-08-10 12:00:00"
    ol_quantity = random_int(1, 50)
    ol_amount = round(random.uniform(1.0, 100.0), 2)
    ol_dist_info = random_string(20)

    sql = f"""
select c_discount, c_last, c_credit, w_tax from customer, warehouse where w_id={w_id}
and c_w_id={w_id} and c_d_id={d_id} and c_id={c_id};
select d_next_o_id, d_tax from district where d_id={d_id} and d_w_id={w_id};
update district set d_next_o_id={o_id} where d_id={d_id} and d_w_id={w_id};
insert into orders values({o_id}, {d_id}, {w_id}, {c_id}, '{o_entry_d}', {o_carrier_id}, {o_ol_cnt}, {o_all_local});
insert into new_orders values({o_id}, {d_id}, {w_id});
select i_price, i_name, i_data from item where i_id={ol_i_id};
select s_quantity, s_data, s_dist_01, s_dist_02, s_dist_03, s_dist_04, s_dist_05,
s_dist_06, s_dist_07, s_dist_08, s_dist_09, s_dist_10 from stock where s_i_id={ol_i_id}
and s_w_id = {ol_supply_w_id};
UPDATE stock SET s_quantity={ol_quantity} where s_i_id = {ol_i_id} and s_w_id = {ol_supply_w_id};
insert into order_line values ({o_id}, {d_id}, {w_id}, {ol_quantity}, {ol_i_id}, {ol_supply_w_id}, '{ol_delivery_d}', {ol_quantity}, {ol_amount}, '{ol_dist_info}');
    """
    return sql.strip()


# 生成 payment 事务 SQL
def generate_payment_sql():
    # 仓库 w_id 范围 1 - W
    w_id = random_int(1, W)  
    h_amount = round(random.uniform(1.0, 100.0), 2)
    # 地区 d_id 范围 1 - D_PER_W（1 - 10  ）
    d_id = random_int(1, D_PER_W)  
    # 每个地区客户 c_id 范围 1 - C_PER_D（1 - 3000  ）
    c_id = random_int(1, C_PER_D)  
    h_date = "2025-08-06 14:00:00"
    h_data = random_string(50)

    sql = f"""
update warehouse set w_ytd=w_ytd+{h_amount} where w_id={w_id};
select w_street_1, w_street_2, w_city, w_state, w_zip, w_name from warehouse where w_id={w_id};
update district set d_ytd=d_ytd+{h_amount} where d_w_id={w_id} and d_id={d_id};
select d_street_1, d_street_2, d_city, d_state, d_zip, d_name from district where d_w_id={w_id} and d_id={d_id};
select c_first, c_middle, c_last, c_street_1, c_street_2, c_city, c_state, c_zip,
c_phone, c_credit, c_credit_lim, c_discount, c_balance, c_since from customer where c_w_id={w_id} and c_d_id={d_id} and c_id={c_id};
update customer set c_balance={h_amount} where c_w_id={w_id} and c_d_id={d_id} and c_id={c_id};
insert into history values({c_id}, {d_id}, {w_id}, {d_id}, {w_id}, '{h_date}', {h_amount}, '{h_data}');
    """
    return sql.strip()


# 生成 delivery 事务 SQL
def generate_delivery_sql():
    # 地区 d_id 范围 1 - D_PER_W（1 - 10  ）
    d_id = random_int(1, D_PER_W)  
    # 仓库 w_id 范围 1 - W
    w_id = random_int(1, W)  
    carrier_id = random_int(1, 5)
    datetime = "2025-08-07 10:00:00"

    # 新订单来自 orders 表每个地区最后 900 条，这里简单用合理范围随机，比如假设 orders 表 o_id 足够大
    min_o_id_subquery = f"select min(no_o_id) as min_o_id from new_orders where no_d_id={d_id} and no_w_id={w_id}"
    # 删除新订单，o_id 范围也基于合理情况，这里先简单随机，实际可结合业务逻辑
    delete_o_id = random_int(1000, 999999)  
    # 查询订单客户、更新订单等用的 o_id 同样合理随机
    select_o_id = random_int(1000, 999999)  
    update_order_o_id = random_int(1000, 999999)  
    update_ol_o_id = random_int(1000, 999999)  
    sum_ol_o_id = random_int(1000, 999999)  
    # 客户 c_id 范围 1 - C_PER_D（1 - 3000  ）
    update_c_id = random_int(1, C_PER_D)  

    sql = f"""
{min_o_id_subquery};
delete from new_orders where no_o_id={delete_o_id} and no_d_id={d_id} and no_w_id={w_id};
select o_c_id from orders where o_id={select_o_id} and o_d_id={d_id} and o_w_id={w_id};
update orders set o_carrier_id={carrier_id} where o_id={update_order_o_id} and o_d_id={d_id} and o_w_id={w_id};
update order_line set ol_delivery_d='{datetime}' where ol_o_id={update_ol_o_id} and ol_d_id={d_id} and ol_w_id={w_id};
select sum(ol_amount) as sum_amount from order_line where ol_o_id={sum_ol_o_id} and ol_d_id={d_id};
update customer set c_balance={round(random.uniform(1.0, 100.0), 2)}, c_delivery_cnt=c_delivery_cnt+1 where c_id={update_c_id} and c_d_id={d_id} and c_w_id={w_id};
    """
    return sql.strip()


# 生成 order_status 事务 SQL
def generate_order_status_sql():
    # 仓库 c_w_id 范围 1 - W
    c_w_id = random_int(1, W)  
    # 地区 c_d_id 范围 1 - D_PER_W（1 - 10  ）
    c_d_id = random_int(1, D_PER_W)  
    c_last = random_string(10)
    # 每个地区客户 c_id 范围 1 - C_PER_D（1 - 3000  ）
    c_id = random_int(1, C_PER_D)  
    # 订单 o_id 合理随机，假设范围较大
    o_id = random_int(1000, 999999)  

    # 60% 概率执行前两条，40% 概率执行后一条（简单模拟概率，实际可更精确）
    if random.random() < 0.6:
        sql = f"""
select count(c_id) as count_c_id from customer where c_w_id={c_w_id} and c_d_id={c_d_id} and c_last='{c_last}';
select c_balance, c_first, c_middle, c_last from customer where c_w_id={c_w_id} and c_d_id={c_d_id} and c_last='{c_last}' order by c_first;
        """
    else:
        sql = f"""
select c_balance, c_first, c_middle, c_last from customer where c_w_id={c_w_id} and c_d_id={c_d_id} and c_id={c_id};
select o_id, o_entry_d, o_carrier_id from orders where o_w_id={c_w_id} and o_d_id={c_d_id} and o_c_id={c_id} and o_id={o_id};
select ol_i_id, ol_supply_w_id, ol_quantity, ol_amount, ol_delivery_d from order_line where ol_w_id={c_w_id} and ol_d_id={c_d_id} and ol_o_id={o_id};
        """
    return sql.strip()


# 生成 stock_level 事务 SQL
def generate_stock_level_sql():
    # 地区 d_id 范围 1 - D_PER_W（1 - 10  ）
    d_id = random_int(1, D_PER_W)  
    # 仓库 w_id 范围 1 - W
    w_id = random_int(1, W)  
    level = random_int(10, 30)

    # 订单 o_id 合理随机
    random_o_id_upper = random_int(1000, 999999)  
    random_o_id_lower = random_o_id_upper - 20 if random_o_id_upper > 20 else 1  
    # 商品 i_id 合理随机，比如 1 - 100000
    random_i_id = random_int(1, 100000)  

    sql = f"""
select d_next_o_id from district where d_id={d_id} and d_w_id={w_id};
select ol_i_id from order_line where ol_w_id={w_id} and ol_d_id={d_id} and ol_o_id<{random_o_id_upper} and ol_o_id>={random_o_id_lower};
select count(*) as count_stock from stock where s_w_id={w_id} and s_i_id={random_i_id} and s_quantity<{level};
    """
    return sql.strip()


# 生成各类事务 SQL 示例
print("--- new_order SQL ---")
print(generate_new_order_sql())
print("\n--- payment SQL ---")
print(generate_payment_sql())
print("\n--- delivery SQL ---")
print(generate_delivery_sql())
print("\n--- order_status SQL ---")
print(generate_order_status_sql())
print("\n--- stock_level SQL ---")
print(generate_stock_level_sql())