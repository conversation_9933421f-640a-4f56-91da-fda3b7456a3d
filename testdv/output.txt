Read from client 5: create table district (d_id int, d_w_id int, d_name char(10), d_street_1 char(20), d_street_2 char(20), d_city char(20), d_state char(2), d_zip char(9), d_tax float, d_ytd float, d_next_o_id int);
begints 0
Read from client 5: create index district(d_w_id, d_id);
begints 1
Read from client 5: select d_next_o_id from district where d_w_id = 1 and d_id = 2;
begints 2
| d_next_o_id |
Read from client 5: show tables;
begints 3
| Tables |
| district |
Read from client 5: show index from district;
begints 4
| district | unique | (d_w_id,d_id) |
Read from client 5: select d_next_o_id from district where d_w_id = 1 and d_id = 2;
begints 5
| d_next_o_id |
Read from client 5: select d_next_o_id from district where d_w_id = -1 and d_id = 2;
begints 6
| d_next_o_id |
Read from client 5: select d_next_o_id from district where d_w_id > 1 and d_id = 2;
begints 7
| d_next_o_id |
Read from client 5: create table a(b int,c int);
begints 8
Read from client 5: create index a(b, c);
begints 9
Read from client 5: select * from a where b > 1 and c = 2;
begints 10
| b | c |
Read from client 5: select * from a where b > 1 and c < 2;
begints 11
| b | c |
Read from client 5: select * from a where b = 1 and c < 2;
begints 12
| b | c |
Read from client 5: insert into a values(1,2);
begints 13
Read from client 5: select * from a where b = 1 and c = 2;
begints 14
| b | c |
| 1 | 2 |
Read from client 5: select * from a where b = 1 and c = 3;
begints 15
| b | c |
Read from client 5: select * from a where b = 1 and c = 4;
begints 16
| b | c |
Read from client 5: select * from a where b > 1 and c > 2;
begints 17
| b | c |
Read from client 5: select * from a where b > 1 and c >= 2;
begints 18
| b | c |
