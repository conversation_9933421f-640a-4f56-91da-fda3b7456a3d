from collections import defaultdict


def count_and_sort_numbers(file_path, output_file):
    # 用于存储数字及其出现次数的字典
    num_counts = defaultdict(int)

    try:
        # 读取文件内容并统计次数
        with open(file_path, 'r') as file:
            for line in file:
                line = line.strip()
                if not line:
                    continue  # 跳过空行

                # 提取"disk page"后的数字
                parts = line.split()
                if len(parts) == 3 and parts[0] == 'disk' and parts[1] == 'page':
                    try:
                        num = int(parts[2])
                        num_counts[num] += 1
                    except ValueError:
                        print(f"警告：无法将 '{parts[2]}' 转换为数字，已跳过该行：{line}")
                else:
                    print(f"警告：行格式不符合要求，已跳过该行：{line}")

        if not num_counts:
            print("文件中未找到有效的数字。")
            return

        # 按出现次数倒序排序（次数相同则按数字本身升序排序）
        sorted_items = sorted(num_counts.items(), key=lambda x: (-x[1], x[0]))

        # 输出到文件
        with open(output_file, 'w') as f:
            f.write("数字\t出现次数\n")
            f.write("-" * 20 + "\n")
            for num, count in sorted_items:
                f.write(f"{num}\t{count}\n")

        print(f"统计完成，结果已输出到 {output_file}")

    except FileNotFoundError:
        print(f"错误：未找到文件 '{file_path}'")
    except Exception as e:
        print(f"处理文件时发生错误：{str(e)}")


# 调用函数，处理lostpage.txt并输出到num.txt
if __name__ == "__main__":
    count_and_sort_numbers("lostpage.txt", "num.txt")