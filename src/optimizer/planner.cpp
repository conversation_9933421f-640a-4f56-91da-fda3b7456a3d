/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#include "planner.h"

#include <memory>

#include "execution/executor_delete.h"
#include "execution/executor_index_scan.h"
#include "execution/executor_insert.h"
#include "execution/executor_nestedloop_join.h"
#include "execution/executor_projection.h"
#include "execution/executor_seq_scan.h"
#include "execution/executor_update.h"
#include "index/ix.h"
#include "record_printer.h"

bool Planner::get_index_cols(std::string tab_name,
                             std::vector<Condition> curr_conds,
                             std::vector<std::string> &index_col_names)
{
    TabMeta &tab = sm_manager_->db_.get_table(tab_name);

    if (curr_conds.empty() || tab.indexes.empty())
    {
        return false;
    }
    // std::cout << "come into get_index_cols\n";

    // TODO 优化：减少索引文件名长度，提高匹配效率
    // conds重复去重
    std::unordered_set<std::string> index_set;      // 快速查找
    std::unordered_map<std::string, int> conds_map; // 列名 -> Cond
    std::unordered_map<std::string, int> repelicate_conds_map;

    index_set.reserve(curr_conds.size());
    conds_map.reserve(curr_conds.size());
    repelicate_conds_map.reserve(2);
    index_col_names.reserve(tab.indexes.begin()->cols.size());

    for (std::size_t i = 0; i < curr_conds.size(); ++i)
    {
        auto &col_name = curr_conds[i].lhs_col.col_name;
        if (index_set.count(col_name) == 0)
        {
            index_set.emplace(col_name);
            conds_map.emplace(col_name, i);
        }
        else
        {
            repelicate_conds_map.emplace(col_name, i);
        }
    }

    size_t max_len = 0, max_equals = 0, cur_len = 0, cur_equals = 0;
    for (auto &index : tab.indexes)
    {
        cur_len = cur_equals = 0;
        for (auto &col : index.cols)
        {
            if (index_set.count(col.name) == 0)
            {
                break;
            }
            if (curr_conds[conds_map[col.name]].op == OP_EQ)
            {
                ++cur_equals;
            }
            ++cur_len;
        }
        // 如果有 where a = 1, b = 1, c > 1;
        // index(a, b, c), index(a, b, c, d);
        // 应该匹配最合适的，避免索引查询中带来的额外拷贝开销
        if (cur_len > max_len && cur_len < curr_conds.size())
        {
            // 匹配最长的
            max_len = cur_len;
            index_col_names.clear();
            for (size_t i = 0; i < cur_len; ++i)
            {
                index_col_names.emplace_back(index.cols[i].name);
            }
        }
        else if (cur_len == curr_conds.size())
        {
            max_len = cur_len;
            // 最长前缀相等选择等号多的
            if (index_col_names.empty())
            {
                // for (size_t i = 0; i < index.cols.size(); ++i) {
                //     index_col_names.emplace_back(index.cols[i].name);
                // }
                for (int i = 0; i < cur_len; ++i)
                {
                    index_col_names.emplace_back(index.cols[i].name);
                }
                // = = >  等号优先   = > =     = =
                // = > =           = = > _   = = _
                // } else if(index_col_names.size() > index.cols.size()) {
                //     // 选择最合适的，正好满足，这样减少索引查找的 memcpy
                //     index_col_names.clear();
                //     for (int i = 0; i < index.cols.size(); ++i) {
                //         index_col_names.emplace_back(index.cols[i].name);
                //     }
                // = = >  等号优先   = > =     = =
                // = > =           = = > _   = = _
                // 谁等号多选谁，不管是否合适
            }
            else if (cur_equals > max_equals)
            {
                max_equals = cur_equals;
                // cur_len >= cur_equals;
                index_col_names.clear();
                // for (size_t i = 0; i < index.cols.size(); ++i) {
                //     index_col_names.emplace_back(index.cols[i].name);
                // }
                for (int i = 0; i < cur_len; ++i)
                {
                    index_col_names.emplace_back(index.cols[i].name);
                }
            }
        }
    }

    // 没有索引
    if (index_col_names.empty())
    {
        return false;
    }

    std::vector<Condition> fed_conds; // 理想谓词
    fed_conds.reserve(index_col_names.size());

    // 连接剩下的非索引列
    // 先清除已经在set中的
    for (auto &index_name : index_col_names)
    {
        if (index_set.count(index_name))
        {
            index_set.erase(index_name);
            fed_conds.emplace_back(std::move(curr_conds[conds_map[index_name]]));
        }
    }

    // 连接 set 中剩下的
    for (auto &index_name : index_set)
    {
        fed_conds.emplace_back(std::move(curr_conds[conds_map[index_name]]));
    }

    // 连接重复的，如果有
    for (auto &[index_name, idx] : repelicate_conds_map)
    {
        std::ignore = idx;
        fed_conds.emplace_back(std::move(curr_conds[repelicate_conds_map[index_name]]));
    }

    curr_conds = std::move(fed_conds);

    // 检查正确与否
    for (auto &index_name : index_col_names)
    {
        // std::cout << index_name << ",";
    }
    // std::cout << "\n";

    if (tab.is_index(index_col_names))
        return true;
    return true;
}

/**
 * @brief 表算子条件谓词生成
 *
 * @param conds 条件
 * @param tab_names 表名
 * @return std::vector<Condition>
 */
std::vector<Condition> pop_conds(std::vector<Condition> &conds, std::string tab_names)
{
    // auto has_tab = [&](const std::string &tab_name) {
    //     return std::find(tab_names.begin(), tab_names.end(), tab_name) != tab_names.end();
    // };
    std::vector<Condition> solved_conds;
    auto it = conds.begin();
    while (it != conds.end())
    {
        if ((tab_names.compare(it->lhs_col.tab_name) == 0 && it->is_rhs_val) || (it->lhs_col.tab_name.compare(it->rhs_col.tab_name) == 0))
        {
            solved_conds.emplace_back(std::move(*it));
            it = conds.erase(it);
        }
        else
        {
            it++;
        }
    }
    return solved_conds;
}

int push_conds(Condition *cond, std::shared_ptr<Plan> plan)
{
    if (auto x = std::dynamic_pointer_cast<ScanPlan>(plan))
    {
        if (x->tab_name_.compare(cond->lhs_col.tab_name) == 0)
        {
            return 1;
        }
        else if (x->tab_name_.compare(cond->rhs_col.tab_name) == 0)
        {
            return 2;
        }
        else
        {
            return 0;
        }
    }
    else if (auto x = std::dynamic_pointer_cast<JoinPlan>(plan))
    {
        int left_res = push_conds(cond, x->left_);
        // 条件已经下推到左子节点
        if (left_res == 3)
        {
            return 3;
        }
        int right_res = push_conds(cond, x->right_);
        // 条件已经下推到右子节点
        if (right_res == 3)
        {
            return 3;
        }
        // 左子节点或右子节点有一个没有匹配到条件的列
        if (left_res == 0 || right_res == 0)
        {
            return left_res + right_res;
        }
        // 左子节点匹配到条件的右边
        if (left_res == 2)
        {
            // 需要将左右两边的条件变换位置
            std::map<CompOp, CompOp> swap_op = {
                {OP_EQ, OP_EQ},
                {OP_NE, OP_NE},
                {OP_LT, OP_GT},
                {OP_GT, OP_LT},
                {OP_LE, OP_GE},
                {OP_GE, OP_LE},
            };
            std::swap(cond->lhs_col, cond->rhs_col);
            cond->op = swap_op.at(cond->op);
        }
        x->conds_.emplace_back(std::move(*cond));
        return 3;
    }
    return false;
}

std::shared_ptr<Plan> pop_scan(int *scantbl, std::string table, std::vector<std::string> &joined_tables,
                               std::vector<std::shared_ptr<Plan>> plans)
{
    /*取出表table的表扫描执行器*/
    for (size_t i = 0; i < plans.size(); i++)
    {
        auto x = std::dynamic_pointer_cast<ScanPlan>(plans[i]);
        if (x->tab_name_.compare(table) == 0)
        {
            scantbl[i] = 1;
            joined_tables.emplace_back(x->tab_name_);
            return plans[i];
        }
    }
    return nullptr;
}

std::shared_ptr<Query> Planner::logical_optimization(std::shared_ptr<Query> query, Context *context)
{

    // TODO 实现逻辑优化规则
    /*连接顺序优化*/
    /*1.首先找出行数最小的表*/
    if (!query->jointree.empty() && !query->is_semi_join && !query->is_anti_join)
    {
        std::vector<JoinRef> joins;
        std::vector<std::string> joined_tables;
        std::vector<std::pair<std::string, int>> num_record;
        for (const auto &table : query->tables)
        {
            auto rm = sm_manager_->fhs_.at(table).get();
            int table_record = rm->get_num_records();
            auto it = num_record.begin();
            while (it != num_record.end() && it->second < table_record)
            {
                it++;
            }
            num_record.insert(it, {table, table_record});
        }

        /*2.取出所有连接条件*/
        bool first = true;
        while (!query->jointree.empty())
        {
            auto it = query->jointree.begin();
            while (it != query->jointree.end() && (it->left_table != num_record.front().first) &&
                   (it->right_table != num_record.front().first))
            {
                it++;
            }
            if (first)
            {
                /*找到该表的右表*/
                auto table_it = num_record.begin();
                table_it++;
                while (table_it != num_record.end() && table_it->first != it->right_table && table_it->first != it->left_table)
                {
                    table_it++;
                }
                /*放到排序后的连接条件*/
                JoinRef jr{num_record.begin()->first, table_it->first, it->join_conds, it->type};
                joins.emplace_back(jr);
                joined_tables.push_back(num_record.begin()->first);
                joined_tables.push_back(table_it->first);
                num_record.erase(table_it);
                num_record.erase(num_record.begin());
                query->jointree.erase(it);
                first = false;
            }
            else
            {
                while (!query->jointree.empty())
                {
                    /*根据joined_tables和剩余的表选择下一个条件*/
                    auto table_it = num_record.begin();
                    while (table_it != num_record.end())
                    {
                        bool is_joined = false;
                        auto join_it = query->jointree.begin();
                        while (join_it != query->jointree.end())
                        {
                            if (join_it->right_table == table_it->first || join_it->left_table == table_it->first)
                            {
                                if ((std::find(joined_tables.begin(), joined_tables.end(), join_it->left_table) != joined_tables.end()) || (std::find(joined_tables.begin(), joined_tables.end(), join_it->right_table) != joined_tables.end()))
                                {
                                    /*找到了*/
                                    joins.emplace_back(*join_it);
                                    query->jointree.erase(join_it);
                                    joined_tables.push_back(table_it->first);
                                    num_record.erase(table_it);
                                    is_joined = true;
                                    break;
                                }
                            }
                            join_it++;
                        }
                        if (is_joined)
                        {
                            break;
                        }
                        else
                        {
                            /*没找到，跳过该表*/
                            table_it++;
                        }
                    }
                }
            }
        }
        query->jointree = joins;
    }

    return query;
}

std::shared_ptr<Plan> Planner::physical_optimization(std::shared_ptr<Query> query, Context *context)
{
    std::shared_ptr<Plan> plan = make_one_rel(query);

    // 其他物理优化

    // 处理orderby
    plan = generate_sort_plan(query, std::move(plan));

    return plan;
}

std::shared_ptr<Plan> Planner::make_one_rel(std::shared_ptr<Query> query)
{
    auto x = std::dynamic_pointer_cast<ast::SelectStmt>(query->parse);
    std::vector<std::string> tables = query->tables;
    // // Scan table , 生成表算子列表tab_nodes
    std::vector<std::shared_ptr<Plan>> table_scan_executors(tables.size());
    /*常量传播优化*/
    if(query->tables.size() > 1 && query->jointree.empty())
    {
        /*只有隐式连接进行优化*/
        /*遍历找出cond中两边都是不同表的cond*/
        for(auto &cond : query->conds)
        {
            if(!cond.is_rhs_val)
            {
                /*右值为列*/
                std::string left_table_name = cond.lhs_col.tab_name;
                std::string right_table_name = cond.rhs_col.tab_name;
                if(left_table_name != right_table_name)
                {
                    /*来自不同表*/
                    /*将右值换成常数*/
                    int constVlaue = -1;
                    for(auto &cd : query->conds)
                    {
                        if(cd.lhs_col.col_name == cond.rhs_col.col_name && cd.is_rhs_val)
                        {
                            cond.rhs_val = cd.rhs_val;
                            cond.is_rhs_val = true;
                            break;
                        }
                    }
                }
            }
        }
    }
    /*orderline的sum优化*/
    if(query->agg_exprs.size() == 1 && query->agg_exprs.begin()->type == AGG_SUM && query->tables.size() == 1 &&
    (*(query->tables.begin()) == "order_line") && query->conds.size() == 2)
    {
        // std::cout << "DEBUG: IndexSumPlan triggered! conds.size()=" << query->conds.size() << std::endl;
        std::shared_ptr<Plan> index_sum = std::make_shared<IndexSumPlan>(T_IndexSum, sm_manager_, *(query->tables.begin()), std::move(query->conds), 50);
        return index_sum;
    }
    for (size_t i = 0; i < tables.size(); i++)
    {
        auto curr_conds = pop_conds(query->conds, tables[i]);
        // int index_no = get_indexNo(tables[i], curr_conds);
        std::vector<std::string> index_col_names;
        bool index_exist = get_index_cols(tables[i], curr_conds, index_col_names);
        if (index_exist == false)
        { // 该表没有索引
            index_col_names.clear();
            table_scan_executors[i] =
                std::make_shared<ScanPlan>(T_SeqScan, sm_manager_, tables[i], curr_conds, index_col_names);
        }
        else
        { // 存在索引
            table_scan_executors[i] =
                std::make_shared<ScanPlan>(T_IndexScan, sm_manager_, tables[i], curr_conds, index_col_names);
        }
    }

    std::shared_ptr<Plan> table_join_executors;
    int scantbl[tables.size()];
    std::vector<std::string> joined_tables;
    for (size_t i = 0; i < tables.size(); i++)
    {
        scantbl[i] = -1;
    }
    if (query->is_semi_join)
    {
        // 取出连接条件
        auto join_ref = query->jointree.begin();
        const auto &join_conds = join_ref->join_conds;

        // 取出左右表名
        const std::string &left_table = join_ref->left_table;
        const std::string &right_table = join_ref->right_table;

        // 拿出这两个表对应的表扫描计划
        std::shared_ptr<Plan> left = pop_scan(scantbl, left_table, joined_tables, table_scan_executors);
        std::shared_ptr<Plan> right = pop_scan(scantbl, right_table, joined_tables, table_scan_executors);
        /*构造一个嵌套循环执行算子*/
        std::shared_ptr<Plan> new_join = std::make_shared<JoinPlan>(
            T_NestLoop, std::move(left), std::move(right), join_conds);
        auto join_plan = std::dynamic_pointer_cast<JoinPlan>(new_join);
        join_plan->type = SEMI_JOIN;
        table_join_executors = std::move(new_join);
        return table_join_executors;
    }

    if(query->is_anti_join)
    {
        // 取出连接条件
        auto join_ref = query->jointree.begin();
        const auto &join_conds = join_ref->join_conds;

        // 取出左右表名
        const std::string &left_table = join_ref->left_table;
        const std::string &right_table = join_ref->right_table;

        // 拿出这两个表对应的表扫描计划
        std::shared_ptr<Plan> left = pop_scan(scantbl, left_table, joined_tables, table_scan_executors);
        std::shared_ptr<Plan> right = pop_scan(scantbl, right_table, joined_tables, table_scan_executors);
        /*构造一个嵌套循环执行算子*/
        std::shared_ptr<Plan> new_join = std::make_shared<JoinPlan>(
            T_NestLoop, std::move(left), std::move(right), join_conds);
        auto join_plan = std::dynamic_pointer_cast<JoinPlan>(new_join);
        join_plan->type = ANTI_JOIN;
        table_join_executors = std::move(new_join);
        return table_join_executors;
    }

    // 只有一个表，不需要join。
    if (tables.size() == 1)
    {
        return table_scan_executors[0];
    }
    // 获取where条件
    auto conds = std::move(query->conds);
    // std::shared_ptr<Plan> table_join_executors;

    // 假设在ast中已经添加了jointree，这里需要修改的逻辑是，先处理jointree，然后再考虑剩下的部分
    /*处理连接条件query->jointree，这一部分是把所有的表连接起来*/
    /*1.将每个joinref处理成一个嵌套循环连接算子*/
    for (const auto &join_ref : query->jointree)
    {
        // 取出连接条件
        const auto &join_conds = join_ref.join_conds;
        // 取出左右表名
        const std::string &left_table = join_ref.left_table;
        const std::string &right_table = join_ref.right_table;
        if (!table_join_executors)
        {
            // 拿出这两个表对应的表扫描计划
            std::shared_ptr<Plan> left = pop_scan(scantbl, left_table, joined_tables, table_scan_executors);
            std::shared_ptr<Plan> right = pop_scan(scantbl, right_table, joined_tables, table_scan_executors);
            std::shared_ptr<Plan> left_pro_scan = nullptr;
            std::shared_ptr<Plan> right_pro_scan = nullptr;
            if (!left || !right)
            {
                throw InternalError("Invalid jointree: failed to find left/right scan plan.");
            }
            if (!query->is_select_all_cols)
            {
                /*投影下推*/
                left_pro_scan = add_scan_pro(query->cols, query->jointree, left);
                right_pro_scan = add_scan_pro(query->cols, query->jointree, right);
                std::shared_ptr<Plan> new_join = std::make_shared<JoinPlan>(
                    T_NestLoop, std::move(left_pro_scan), std::move(right_pro_scan), join_conds);
                table_join_executors = std::move(new_join);
            }
            else
            {
                /*构造一个嵌套循环执行算子*/
                std::shared_ptr<Plan> new_join = std::make_shared<JoinPlan>(
                    T_NestLoop, std::move(left), std::move(right), join_conds);
                table_join_executors = std::move(new_join);
            }
        }
        else
        {
            std::shared_ptr<Plan> left_need_to_join_executors = nullptr;
            std::shared_ptr<Plan> right_need_to_join_executors = nullptr;
            std::shared_ptr<Plan> left_pro_scan = nullptr;
            std::shared_ptr<Plan> right_pro_scan = nullptr;
            bool isneedreverse = false;
            if (std::find(joined_tables.begin(), joined_tables.end(), left_table) == joined_tables.end())
            {
                left_need_to_join_executors = pop_scan(scantbl, left_table, joined_tables, table_scan_executors);
            }
            if (std::find(joined_tables.begin(), joined_tables.end(), right_table) == joined_tables.end())
            {
                right_need_to_join_executors = pop_scan(scantbl, right_table, joined_tables, table_scan_executors);
                isneedreverse = true;
            }

            if (!query->is_select_all_cols)
            {
                /*一般情况下两个执行器只会有一个不为空,不为空说明之前没连接是个新表需要新的表扫描执行器*/
                if (left_need_to_join_executors && !right_need_to_join_executors)
                {
                    left_pro_scan = add_scan_pro(query->cols, query->jointree, left_need_to_join_executors);
                    table_join_executors = std::make_shared<JoinPlan>(
                        T_NestLoop, std::move(left_pro_scan), std::move(table_join_executors), join_conds);
                }
                else if (right_need_to_join_executors && !left_need_to_join_executors)
                {
                    right_pro_scan = add_scan_pro(query->cols, query->jointree, right_need_to_join_executors);
                    table_join_executors = std::make_shared<JoinPlan>(
                        T_NestLoop, std::move(right_pro_scan), std::move(table_join_executors), join_conds);
                }
                else
                {
                    throw RMDBError("Invalid join!");
                }
            }
            else
            {
                /*一般情况下两个执行器只会有一个不为空,不为空说明之前没连接是个新表需要新的表扫描执行器*/
                if (left_need_to_join_executors && !right_need_to_join_executors)
                {
                    table_join_executors = std::make_shared<JoinPlan>(
                        T_NestLoop, std::move(left_need_to_join_executors), std::move(table_join_executors), join_conds);
                }
                else if (right_need_to_join_executors && !left_need_to_join_executors)
                {
                    table_join_executors = std::make_shared<JoinPlan>(
                        T_NestLoop, std::move(right_need_to_join_executors), std::move(table_join_executors), join_conds);
                }
                else
                {
                    throw RMDBError("Invalid join!");
                }
            }
        }
    }

    if (query->jointree.empty())
    {
        if (conds.size() >= 1)
        {
            /*这里应该处理where条件中包含两个表的连接，比如a.id > b.id
            这里应该加一个filter算子，加在连接完成之后*/

            // 有连接条件
            // 根据连接条件，生成第一层join
            std::vector<std::string> joined_tables(tables.size());
            auto it = conds.begin();
            while (it != conds.end())
            {
                std::shared_ptr<Plan> left, right;
                left = pop_scan(scantbl, it->lhs_col.tab_name, joined_tables, table_scan_executors);
                right = pop_scan(scantbl, it->rhs_col.tab_name, joined_tables, table_scan_executors);
                std::vector<Condition> join_conds{*it};
                
                /*进行投影下推*/
                std::vector<JoinRef> temp_joincond;
                JoinRef temp_joinref;
                temp_joinref.join_conds.push_back(*it);
                temp_joincond.push_back(temp_joinref);
                if (!query->is_select_all_cols)
                {
                    left = add_scan_pro(query->cols, temp_joincond, left);
                    right = add_scan_pro(query->cols, temp_joincond, right);
                }

                // 建立join
                //  判断使用哪种join方式
                if (enable_nestedloop_join && enable_sortmerge_join)
                {
                    // 默认nested loop join
                    table_join_executors = std::make_shared<JoinPlan>(T_NestLoop, std::move(left), std::move(right), join_conds);
                }
                else if (enable_nestedloop_join)
                {
                    table_join_executors = std::make_shared<JoinPlan>(T_NestLoop, std::move(left), std::move(right), join_conds);
                }
                else if (enable_sortmerge_join)
                {
                    table_join_executors = std::make_shared<JoinPlan>(T_SortMerge, std::move(left), std::move(right), join_conds);
                }
                else
                {
                    // error
                    throw RMDBError("No join executor selected!");
                }

                // table_join_executors = std::make_shared<JoinPlan>(T_NestLoop, std::move(left), std::move(right), join_conds);
                it = conds.erase(it);
                break;
            }
            // 根据连接条件，生成第2-n层join
            it = conds.begin();
            while (it != conds.end())
            {
                std::shared_ptr<Plan> left_need_to_join_executors = nullptr;
                std::shared_ptr<Plan> right_need_to_join_executors = nullptr;
                bool isneedreverse = false;
                if (std::find(joined_tables.begin(), joined_tables.end(), it->lhs_col.tab_name) == joined_tables.end())
                {
                    left_need_to_join_executors = pop_scan(scantbl, it->lhs_col.tab_name, joined_tables, table_scan_executors);
                    /*进行投影下推*/
                    std::vector<JoinRef> temp_joincond;
                    JoinRef temp_joinref;
                    temp_joinref.join_conds.push_back(*it);
                    temp_joincond.push_back(temp_joinref);
                    if (!query->is_select_all_cols)
                    {
                        left_need_to_join_executors = add_scan_pro(query->cols, temp_joincond, left_need_to_join_executors);
                    }
                }
                if (std::find(joined_tables.begin(), joined_tables.end(), it->rhs_col.tab_name) == joined_tables.end())
                {
                    right_need_to_join_executors = pop_scan(scantbl, it->rhs_col.tab_name, joined_tables, table_scan_executors);
                    isneedreverse = true;
                    /*进行投影下推*/
                    std::vector<JoinRef> temp_joincond;
                    JoinRef temp_joinref;
                    temp_joinref.join_conds.push_back(*it);
                    temp_joincond.push_back(temp_joinref);
                    if (!query->is_select_all_cols)
                    {
                        right_need_to_join_executors = add_scan_pro(query->cols, temp_joincond, right_need_to_join_executors);
                    }
                }

                if (left_need_to_join_executors != nullptr && right_need_to_join_executors != nullptr)
                {
                    std::vector<Condition> join_conds{*it};
                    std::shared_ptr<Plan> temp_join_executors = std::make_shared<JoinPlan>(T_NestLoop,
                                                                                           std::move(left_need_to_join_executors),
                                                                                           std::move(right_need_to_join_executors),
                                                                                           join_conds);
                    table_join_executors = std::make_shared<JoinPlan>(T_NestLoop, std::move(temp_join_executors),
                                                                      std::move(table_join_executors),
                                                                      std::vector<Condition>());
                }
                else if (left_need_to_join_executors != nullptr || right_need_to_join_executors != nullptr)
                {
                    if (isneedreverse)
                    {
                        std::map<CompOp, CompOp> swap_op = {
                            {OP_EQ, OP_EQ},
                            {OP_NE, OP_NE},
                            {OP_LT, OP_GT},
                            {OP_GT, OP_LT},
                            {OP_LE, OP_GE},
                            {OP_GE, OP_LE},
                        };
                        std::swap(it->lhs_col, it->rhs_col);
                        it->op = swap_op.at(it->op);
                        left_need_to_join_executors = std::move(right_need_to_join_executors);
                    }
                    std::vector<Condition> join_conds{*it};
                    table_join_executors = std::make_shared<JoinPlan>(T_NestLoop, std::move(left_need_to_join_executors),
                                                                      std::move(table_join_executors), join_conds);
                }
                else
                {
                    push_conds(std::move(&(*it)), table_join_executors);
                }
                it = conds.erase(it);
            }
        }
        else
        {
            table_join_executors = table_scan_executors[0];
            scantbl[0] = 1;
        }

        /*连接出现在where条件之外的表*/
        // 连接剩余表
        for (size_t i = 0; i < tables.size(); i++)
        {
            if (scantbl[i] == -1)
            {
                table_join_executors = std::make_shared<JoinPlan>(T_NestLoop, std::move(table_join_executors), std::move(table_scan_executors[i]),
                                                                  std::vector<Condition>());
            }
        }
    }

    return table_join_executors;
}

std::shared_ptr<Plan> Planner::generate_sort_plan(std::shared_ptr<Query> query, std::shared_ptr<Plan> plan)
{
    // 检查是否是SelectStmt
    auto select_stmt = std::dynamic_pointer_cast<ast::SelectStmt>(query->parse);
    if (select_stmt) {
        if (!select_stmt->has_sort) {
            return plan;
        }
    } else {
        // 检查是否是GroupByStmt
        auto group_stmt = std::dynamic_pointer_cast<ast::GroupByStmt>(query->parse);
        if (group_stmt) {
            if (!group_stmt->has_sort) {
                return plan;
            }
        } else {
            // 既不是SelectStmt也不是GroupByStmt，直接返回
            return plan;
        }
    }
    std::vector<std::string> tables = query->tables;
    std::vector<ColMeta> all_cols;
    for (auto &sel_tab_name : tables)
    {
        // 这里db_不能写成get_db(), 注意要传指针
        const auto &sel_tab_cols = sm_manager_->db_.get_table(sel_tab_name).cols;
        all_cols.insert(all_cols.end(), sel_tab_cols.begin(), sel_tab_cols.end());
    }
    std::vector<TabCol> sel_cols;
    std::vector<bool> is_desc;
    // for (auto &col : all_cols) {
    //     if(col.name.compare(x->order->cols->col_name) == 0 )
    //     sel_cols.push_back({.tab_name = col.tab_name, .col_name = col.name});
    // }
    // 获取排序信息
    std::vector<std::shared_ptr<ast::OrderBy>> order_list;
    if (select_stmt) {
        order_list = select_stmt->order;
    } else {
        auto group_stmt = std::dynamic_pointer_cast<ast::GroupByStmt>(query->parse);
        if (group_stmt) {
            order_list = group_stmt->order;
        }
    }

    for (const auto &od : order_list)
    {
        for (auto &col : all_cols)
        {
            if (col.name.compare(od->cols->col_name) == 0)
                sel_cols.push_back({.tab_name = col.tab_name, .col_name = col.name});
        }
        is_desc.push_back(od->orderby_dir == ast::OrderBy_DESC);
    }

    // 获取LIMIT信息
    int limit = query->limit;

    return std::make_shared<SortPlan>(T_Sort, std::move(plan), sel_cols, is_desc, limit);
}

/**
 * @brief select plan 生成
 *
 * @param sel_cols select plan 选取的列
 * @param tab_names select plan 目标的表
 * @param conds select plan 选取条件
 */
std::shared_ptr<Plan> Planner::generate_select_plan(std::shared_ptr<Query> query, Context *context)
{
    // 逻辑优化
    query = logical_optimization(std::move(query), context);

    // 物理优化
    auto sel_cols = query->cols;
    std::shared_ptr<Plan> plannerRoot = physical_optimization(query, context);
    plannerRoot = std::make_shared<ProjectionPlan>(T_Projection, std::move(plannerRoot),
                                                   std::move(sel_cols));

    return plannerRoot;
}

// 生成DDL语句和DML语句的查询执行计划
std::shared_ptr<Plan> Planner::do_planner(std::shared_ptr<Query> query, Context *context)
{
    std::shared_ptr<Plan> plannerRoot;
    if (auto x = std::dynamic_pointer_cast<ast::CreateTable>(query->parse))
    {
        // create table;
        std::vector<ColDef> col_defs;
        for (auto &field : x->fields)
        {
            if (auto sv_col_def = std::dynamic_pointer_cast<ast::ColDef>(field))
            {
                ColDef col_def = {.name = sv_col_def->col_name,
                                  .type = interp_sv_type(sv_col_def->type_len->type),
                                  .len = sv_col_def->type_len->len};
                col_defs.push_back(col_def);
            }
            else
            {
                throw InternalError("Unexpected field type");
            }
        }
        plannerRoot = std::make_shared<DDLPlan>(T_CreateTable, x->tab_name, std::vector<std::string>(), col_defs);
    }
    else if (auto x = std::dynamic_pointer_cast<ast::DropTable>(query->parse))
    {
        // drop table;
        plannerRoot = std::make_shared<DDLPlan>(T_DropTable, x->tab_name, std::vector<std::string>(), std::vector<ColDef>());
    }
    else if (auto x = std::dynamic_pointer_cast<ast::CreateIndex>(query->parse))
    {
        // create index;
        plannerRoot = std::make_shared<DDLPlan>(T_CreateIndex, x->tab_name, x->col_names, std::vector<ColDef>());
    } else if (auto x = std::dynamic_pointer_cast<ast::CreateStaticCheckpoint>(query->parse)) {
        // create static checkpoint
        plannerRoot = std::make_shared<DDLPlan>(T_CreateStaticCheckpoint, std::string(), std::vector<std::string>(), std::vector<ColDef>());
    } else if (auto x = std::dynamic_pointer_cast<ast::DropIndex>(query->parse))
    {
        // drop index
        plannerRoot = std::make_shared<DDLPlan>(T_DropIndex, x->tab_name, x->col_names, std::vector<ColDef>());
    }
    else if (auto x = std::dynamic_pointer_cast<ast::InsertStmt>(query->parse))
    {
        // insert;
        plannerRoot = std::make_shared<DMLPlan>(T_Insert, std::shared_ptr<Plan>(), x->tab_name,
                                                query->values, std::vector<Condition>(), std::vector<SetClause>());
    }
    else if (auto x = std::dynamic_pointer_cast<ast::DeleteStmt>(query->parse))
    {
        // delete;
        // 生成表扫描方式
        std::shared_ptr<Plan> table_scan_executors;
        // 只有一张表，不需要进行物理优化了
        // int index_no = get_indexNo(x->tab_name, query->conds);
        std::vector<std::string> index_col_names;
        bool index_exist = get_index_cols(x->tab_name, query->conds, index_col_names);

        if (index_exist == false)
        { // 该表没有索引
            index_col_names.clear();
            table_scan_executors =
                std::make_shared<ScanPlan>(T_SeqScan, sm_manager_, x->tab_name, query->conds, index_col_names);
        }
        else
        { // 存在索引
            table_scan_executors =
                std::make_shared<ScanPlan>(T_IndexScan, sm_manager_, x->tab_name, query->conds, index_col_names);
        }

        plannerRoot = std::make_shared<DMLPlan>(T_Delete, table_scan_executors, x->tab_name,
                                                std::vector<Value>(), query->conds, std::vector<SetClause>());
    }
    else if (auto x = std::dynamic_pointer_cast<ast::UpdateStmt>(query->parse))
    {
        // update;
        // 生成表扫描方式
        std::shared_ptr<Plan> table_scan_executors;
        // 只有一张表，不需要进行物理优化了
        // int index_no = get_indexNo(x->tab_name, query->conds);
        std::vector<std::string> index_col_names;
        bool index_exist = get_index_cols(x->tab_name, query->conds, index_col_names);

        if (index_exist == false)
        { // 该表没有索引
            index_col_names.clear();
            table_scan_executors =
                std::make_shared<ScanPlan>(T_SeqScan, sm_manager_, x->tab_name, query->conds, index_col_names);
        }
        else
        { // 存在索引
            table_scan_executors =
                std::make_shared<ScanPlan>(T_IndexScan, sm_manager_, x->tab_name, query->conds, index_col_names);
        }
        plannerRoot = std::make_shared<DMLPlan>(T_Update, table_scan_executors, x->tab_name,
                                                std::vector<Value>(), query->conds,
                                                query->set_clauses);
    }
    else if (auto x = std::dynamic_pointer_cast<ast::SelectStmt>(query->parse))
    {

        std::shared_ptr<plannerInfo> root = std::make_shared<plannerInfo>(x);
        // 生成select语句的查询执行计划
        bool is_select_all_cols = query->is_select_all_cols;
        std::shared_ptr<Plan> projection = generate_select_plan(std::move(query), context);
        projection->is_select_all_cols = is_select_all_cols;
        plannerRoot = std::make_shared<DMLPlan>(T_select, projection, std::string(), std::vector<Value>(),
                                                std::vector<Condition>(), std::vector<SetClause>());
        plannerRoot->is_select_all_cols = is_select_all_cols;
    } else if (auto x = std::dynamic_pointer_cast<ast::GroupByStmt>(query->parse)) {
        // 生成聚合查询的执行计划
        std::shared_ptr<Plan> aggregation_plan = generate_aggregation_plan(std::move(query), context);
        plannerRoot = std::make_shared<DMLPlan>(T_select, aggregation_plan, std::string(), std::vector<Value>(),
                                                    std::vector<Condition>(), std::vector<SetClause>());
    }
    else if (auto x = std::dynamic_pointer_cast<ast::ExplainStmt>(query->parse))
    {
        std::shared_ptr<Plan> sub_plan = do_planner(query->explain_query, context);
        plannerRoot = std::make_shared<DMLPlan>(T_Explain, sub_plan, std::string(), std::vector<Value>(),
                                                std::vector<Condition>(), std::vector<SetClause>());
    }
    else if (auto x = std::dynamic_pointer_cast<ast::SemijoinStmt>(query->parse))
    {
        // 生成select语句的查询执行计划
        bool is_select_all_cols = query->is_select_all_cols;
        std::shared_ptr<Plan> projection = generate_select_plan(std::move(query), context);
        projection->is_select_all_cols = is_select_all_cols;
        plannerRoot = std::make_shared<DMLPlan>(T_SEMI_join, projection, std::string(), std::vector<Value>(),
                                                std::vector<Condition>(), std::vector<SetClause>());
        plannerRoot->is_select_all_cols = is_select_all_cols;
    } else if (auto x = std::dynamic_pointer_cast<ast::AntijoinStmt>(query->parse))
    {
        // 生成select语句的查询执行计划
        bool is_select_all_cols = query->is_select_all_cols;
        std::shared_ptr<Plan> projection = generate_select_plan(std::move(query), context);
        projection->is_select_all_cols = is_select_all_cols;
        plannerRoot = std::make_shared<DMLPlan>(T_ANTI_join, projection, std::string(), std::vector<Value>(),
                                                std::vector<Condition>(), std::vector<SetClause>());
        plannerRoot->is_select_all_cols = is_select_all_cols;
    } else if (auto x = std::dynamic_pointer_cast<ast::LoadStmt>(query->parse)){
        plannerRoot = std::make_shared<DMLPlan>(T_Load, std::shared_ptr<Plan>(),  x->tab_name,  
                                                    std::vector<Value>(), std::vector<Condition>(), std::vector<SetClause>());
        std::shared_ptr<DMLPlan> dmlPlan = std::dynamic_pointer_cast<DMLPlan>(plannerRoot);
        dmlPlan->file_name_ = query->loadFileName;
    } else {
        throw InternalError("Unexpected AST root");
    }

    return plannerRoot;
}

std::shared_ptr<Plan> Planner::add_scan_pro(std::vector<TabCol> cols, std::vector<JoinRef> join_conds, std::shared_ptr<Plan> left_scan)
{
    std::string table_name = std::dynamic_pointer_cast<ScanPlan>(left_scan)->tab_name_;
    /*投影列是属于本表的投影列 + 与本表有关的连接条件中的列*/
    std::vector<TabCol> pro_cols;
    for (const auto &col : cols)
    {
        if (col.tab_name == table_name)
        {
            pro_cols.push_back(col);
        }
    }
    for (const auto &join_ref : join_conds)
    {
        for (const auto &join_cond : join_ref.join_conds)
        {
            if (join_cond.lhs_col.tab_name == table_name)
            {
                pro_cols.push_back(join_cond.lhs_col);
            }
            else if (join_cond.rhs_col.tab_name == table_name)
            {
                pro_cols.push_back(join_cond.rhs_col);
            }
        }
    }

    /* 增加去重逻辑：移除重复的投影列 */
    if (!pro_cols.empty())
    {
        // 1. 按表名和列名排序，使相同列相邻
        std::sort(pro_cols.begin(), pro_cols.end(), 
            [](const TabCol& a, const TabCol& b) {
                if (a.tab_name != b.tab_name) {
                    return a.tab_name < b.tab_name;
                }
                return a.col_name < b.col_name;
            });
        
        // 2. 移除连续重复的元素
        auto last = std::unique(pro_cols.begin(), pro_cols.end(),
            [](const TabCol& a, const TabCol& b) {
                return (a.tab_name == b.tab_name) && (a.col_name == b.col_name);
            });
        
        // 3. 截断向量保留不重复元素
        pro_cols.erase(last, pro_cols.end());
    }

    /*创建投影算子*/
    std::shared_ptr<Plan> projection = std::make_shared<ProjectionPlan>(T_Projection, left_scan, pro_cols);
    return projection;
}


/**
 * @brief 生成聚合查询的执行计划
 *
 * @param query 包含聚合信息的查询对象
 * @param context 上下文
 * @return std::shared_ptr<Plan> 聚合执行计划
 */
std::shared_ptr<Plan> Planner::generate_aggregation_plan(std::shared_ptr<Query> query, Context *context) {
    // 逻辑优化
    query = logical_optimization(std::move(query), context);

    // 生成基础的扫描和连接计划
    std::shared_ptr<Plan> base_plan = physical_optimization(query, context);



    // 创建聚合计划
    std::shared_ptr<Plan> aggregation_plan = std::make_shared<AggregationPlan>(
        std::move(base_plan),
        query->group_cols,
        query->agg_exprs,
        query->having_conds,
        query->cols
    );

    // 处理排序（如果有ORDER BY）
    aggregation_plan = generate_sort_plan(query, std::move(aggregation_plan));

    // 添加投影层
    aggregation_plan = std::make_shared<ProjectionPlan>(T_Projection, std::move(aggregation_plan),
                                                       std::move(query->cols));

    return aggregation_plan;
}