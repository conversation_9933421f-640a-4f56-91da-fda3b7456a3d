/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once

#include "execution_defs.h"
#include "execution_manager.h"
#include "executor_abstract.h"
#include "index/ix.h"
#include "system/sm.h"
#include "execution_common.h"
#include <climits>

class IndexSumExecutor : public AbstractExecutor {
   private:
    std::string tab_name_;              // 表的名称
    std::vector<Condition> conds_;      // scan的条件
    RmFileHandle *fh_;                  // 表的数据文件句柄
    std::vector<ColMeta> cols_;         // scan后生成的记录的字段
    size_t len_;                        // scan后生成的每条记录的长度
    std::vector<Condition> fed_conds_;  // 同conds_，两个字段相同

    Rid rid_;
    std::unique_ptr<RecScan> scan_;     // table_iterator
    
    SmManager *sm_manager_;
    int w_id_{-1};
    int w_id_it;
    float index_sum{0.0};
    bool has_returned{false};  // 添加标志来跟踪是否已经返回了结果


    // bool is_end{false};
    // bool is_find{false};

   public:
    IndexSumExecutor(SmManager *sm_manager, std::string tab_name, std::vector<Condition> conds, int w_id, Context *context) {
        sm_manager_ = sm_manager;
        tab_name_ = std::move(tab_name);
        conds_ = std::move(conds);
        fh_ = sm_manager_->fhs_.at(tab_name_).get();
        ColMeta col;
        col.len = sizeof(float);
        col.tab_name = "order_line";
        col.offset = 0;
        col.name = "ol_amount";  // 使用查询中的别名
        col.type = TYPE_FLOAT;  // 明确告诉系统这是浮点数据
        cols_.push_back(col);
        len_ = sizeof(float);
        context_ = context;
        fed_conds_ = conds_;
        w_id_ = w_id;
        TabCol w_id_col;
        w_id_col.tab_name = tab_name_;
        w_id_col.col_name = "ol_w_id";
        Condition w_id_cd;
        w_id_cd.lhs_col = w_id_col;
        Value rhs_value;
        w_id_cd.is_rhs_val = true;
        w_id_cd.rhs_val = rhs_value;
        fed_conds_.insert(fed_conds_.begin(), w_id_cd);
    }

    void beginTuple() override {
        w_id_it = 1;
        index_sum = 0.0;
        has_returned = false;  // 重置标志
        
        // 从查询条件中获取 ol_o_id 和 ol_d_id
        int target_o_id = 0;
        int target_d_id = 0;
        for (const auto &cond : conds_) {
            if (cond.lhs_col.col_name == "ol_o_id" && cond.is_rhs_val) {
                target_o_id = cond.rhs_val.int_val;
            }
            if (cond.lhs_col.col_name == "ol_d_id" && cond.is_rhs_val) {
                target_d_id = cond.rhs_val.int_val;
            }
        }
        
        // 为每个可能的 w_id (1-50) 累加数据
        for (w_id_it = 1; w_id_it <= w_id_; w_id_it++) {
            /* 构造条件 */
            fed_conds_.begin()->rhs_val.set_int(w_id_it);
            
            /* 使用 fed_conds_ 构造 scan_ */
            // 获取表元数据
            TabMeta &tab = sm_manager_->db_.get_table(tab_name_);
            
            // 按顺序构造索引列名 (ol_w_id, ol_d_id, ol_o_id, ol_number)
            std::vector<std::string> index_col_names = {"ol_w_id", "ol_d_id", "ol_o_id", "ol_number"};
            
            // 获取索引元数据
            auto index_meta = tab.get_index_meta(index_col_names);
            if (index_meta == tab.indexes.end()) {
                throw InternalError("Index not found for columns: ol_w_id, ol_d_id, ol_o_id, ol_number");
            }
            
            // 获取索引句柄
            std::string index_name = sm_manager_->get_ix_manager()->get_index_name(tab_name_, index_col_names, sm_manager_->db_);
            auto ih = sm_manager_->ihs_.at(index_name).get();
            
            // 构造索引键边界
            int key_len = 0;
            std::vector<ColType> col_types;
            std::vector<int> col_lens;
            
            // 计算索引键的总长度和类型信息
            for (const auto &col_name : index_col_names) {
                auto col_meta = tab.get_col(col_name);
                key_len += col_meta->len;
                col_types.push_back(col_meta->type);
                col_lens.push_back(col_meta->len);
            }
            
            // 构造下界键 (w_id_it, target_d_id, target_o_id, 0)
            std::vector<char> lower_key(key_len);
            int offset = 0;
            
            // 设置 ol_w_id = w_id_it
            memcpy(lower_key.data() + offset, &w_id_it, sizeof(int));
            offset += sizeof(int);
            
            // 设置 ol_d_id = target_d_id
            memcpy(lower_key.data() + offset, &target_d_id, sizeof(int));
            offset += sizeof(int);
            
            // 设置 ol_o_id = target_o_id
            memcpy(lower_key.data() + offset, &target_o_id, sizeof(int));
            offset += sizeof(int);
            
            // 设置 ol_number = 0 (最小值)
            int ol_number = 0;
            memcpy(lower_key.data() + offset, &ol_number, sizeof(int));
            
            // 构造上界键 (w_id_it, target_d_id, target_o_id, INT_MAX)
            std::vector<char> upper_key(key_len);
            offset = 0;
            
            memcpy(upper_key.data() + offset, &w_id_it, sizeof(int));
            offset += sizeof(int);
            memcpy(upper_key.data() + offset, &target_d_id, sizeof(int));
            offset += sizeof(int);
            memcpy(upper_key.data() + offset, &target_o_id, sizeof(int));
            offset += sizeof(int);
            
            int max_ol_number = INT_MAX;
            memcpy(upper_key.data() + offset, &max_ol_number, sizeof(int));
            
            // 获取索引扫描边界
            Iid lower = ih->lower_bound(lower_key.data());
            Iid upper = ih->upper_bound(upper_key.data());
            
            // 构造 IxScan 执行器
            BufferPoolManager *bpm = sm_manager_->get_bpm();
            scan_ = std::make_unique<IxScan>(ih, lower, upper, bpm);
            
            // 扫描索引并累加 ol_amount
            int record_count = 0;  // 添加计数器
            for (; !scan_->is_end(); scan_->next()) {
                Rid rid = scan_->rid();
                auto targetRecord = fh_->get_record(rid, context_);
                
                // 获取 ol_amount 列的值
                auto colmeta = tab.get_col("ol_amount");
                Value recordColValue;
                std::string str(targetRecord->data + colmeta->offset, colmeta->len);
                
                switch(colmeta->type) {
                    case TYPE_INT:
                        recordColValue.set_int(*reinterpret_cast<const int*>(targetRecord->data + colmeta->offset));
                        break;
                    case TYPE_FLOAT:
                        recordColValue.set_float(*reinterpret_cast<const float*>(targetRecord->data + colmeta->offset));
                        break;
                    case TYPE_STRING:
                        recordColValue.set_str(str.c_str());
                        break;
                    default:
                        throw InternalError("invalid type\n");
                }
                recordColValue.init_raw(colmeta->len);
                index_sum += recordColValue.float_val;
                record_count++;  // 增加计数器
            }
            // 添加调试信息
            if (record_count > 0) {
                // std::cout << "DEBUG: w_id=" << w_id_it << " found " << record_count << " records, current sum=" << index_sum << std::endl;
            }
        }
        
        // 调试：显示最终总和
        // std::cout << "DEBUG: beginTuple completed, final index_sum=" << index_sum << std::endl;
    }

    void nextTuple() override {
        // 什么都不做，因为beginTuple已经处理了所有数据
    }

    std::unique_ptr<RmRecord> Next() override {
        // 直接返回累加的结果
        // std::cout << "DEBUG: Next() called, final index_sum=" << index_sum << ", len_=" << len_ << std::endl;
        char* temp_data = new char[len_];
        memcpy(temp_data, &index_sum, len_);  // 将float的字节复制到缓冲区
        // std::cout << "DEBUG: temp_data created, first 4 bytes: ";
        // for(int i = 0; i < 4; i++) {
        //     printf("%02x ", (unsigned char)temp_data[i]);
        // }
        // printf("\n");
        
        // 设置标志，表示已经返回了结果
        has_returned = true;
        
        return std::make_unique<RmRecord>(len_, temp_data);
    }
    
    const std::vector<ColMeta> &cols() const override
    {
        return cols_;
    }
    size_t tupleLen() const override{ return len_; };
    bool is_end() const override{
        // 如果已经返回了结果，就结束
        return has_returned;
    }

    ColMeta get_col_offset(const TabCol &target) override{
        return *(get_col(cols_,target));
    };

    std::string getType() override {
        return "IndexSumExecutor";
    }

    std::string get_tab_name() override {
        return tab_name_;
    }

    // 获取条件列表，用于判断是否有WHERE条件
    const std::vector<Condition>& get_conditions() const {
        return conds_;
    }

    // 获取文件句柄，用于优化COUNT(*)操作
    RmFileHandle* get_file_handle() const {
        return fh_;
    }

    Rid &rid() override { return rid_; }
};

