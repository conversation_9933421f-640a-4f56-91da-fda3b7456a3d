/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once
#include "execution_defs.h"
#include "execution_manager.h"
#include "executor_abstract.h"
#include "index/ix.h"
#include "system/sm.h"
#include "execution_common.h"

class InsertExecutor : public AbstractExecutor {
private:
    TabMeta tab_;                   // 表的元数据
    std::vector<Value> values_;     // 需要插入的数据
    RmFileHandle* fh_;              // 表的数据文件句柄
    std::string tab_name_;          // 表名称
    Rid rid_;                       // 插入的位置，插入后赋值
    SmManager* sm_manager_;

public:
    InsertExecutor(SmManager* sm_manager, const std::string& tab_name, std::vector<Value> values, Context* context)
        : sm_manager_(sm_manager), tab_name_(tab_name), values_(std::move(values)) {
        tab_ = sm_manager_->db_.get_table(tab_name);
        if (values_.size() != tab_.cols.size()) {
            throw InvalidValueCountError();
        }
        fh_ = sm_manager_->fhs_.at(tab_name).get();
        context_ = context;
    }

    std::unique_ptr<RmRecord> Next() override {
        // Make record buffer
        Transaction *txn = context_->txn_;
        // timestamp_t read_ts = txn->get_read_ts();
        timestamp_t temp_ts = txn->get_transaction_id() + TXN_START_ID;
        // TransactionManager *tmr = context_->txn_mgr_;
        TabMeta tabmeta = sm_manager_->db_.get_table(tab_name_);
        
        /*根据value生成record*/
        RmRecord rec(fh_->get_file_hdr().record_size);
        for (size_t i = 0; i < values_.size(); i++) {
            auto &col = tab_.cols[i];
            auto &val = values_[i];
            if (col.type != val.type) {
                if(col.type == TYPE_FLOAT && val.type == TYPE_INT)
                {
                    val.type = TYPE_FLOAT;
                    val.set_float(float(val.int_val));
                }
                else
                {
                    throw IncompatibleTypeError(coltype2str(col.type), coltype2str(val.type));
                }
            }
            val.init_raw(col.len);
            memcpy(rec.data + col.offset, val.raw->data, col.len);
        }

        // if(tmr->get_concurrency_mode() == ConcurrencyMode::MVCC)
        // {
            // bool hasInsertConflict = false;
            // /*考虑等值插入冲突问题*/
            // auto& insertedRowRidSet = fh_->get_raw_inserted_rid();
            // for(auto &rid : insertedRowRidSet)
            // {
            //     auto insertedRecord = fh_->get_record(rid, nullptr);
            //     auto inserttuplemeta = fh_->get_tuple_meta(rid);
            //     if(inserttuplemeta.is_deleted_ && inserttuplemeta.ts_ < TXN_START_ID)
            //     {
            //         continue;
            //     }
            //     std::vector<bool> modified_bools;
            //     std::vector<Value> modified_values;
            //     ExtractDifferentValues(tab_, rec, *insertedRecord, modified_values, modified_bools);
            //     bool isSame = modified_values.empty();

            //     bool isInActiveTxn = true;
            //     auto insertedRidSets = fh_->get_inserted_rid();
            //     auto it = std::find(insertedRidSets.begin(), insertedRidSets.end(),rid);
            //     isInActiveTxn = it != insertedRidSets.end();

            //     if(isSame)
            //     {
            //         if(IsWriteWriteConflict(inserttuplemeta.ts_, txn))
            //         {
            //             hasInsertConflict = true;
            //         }
            //         // hasInsertConflict = true;
            //         break;
            //     }

            //     // if((isInActiveTxn && modified_values.empty() && (inserttuplemeta.is_deleted_ == false))
            //     // || (!isInActiveTxn && inserttuplemeta.ts_ >= TXN_START_ID))
            //     // if(IsWriteWriteConflict(inserttuplemeta.ts_, txn))
            //     // {
            //     //     hasInsertConflict = true;
            //     // }
            // }

            // if(hasInsertConflict)
            // {
            //     throw TransactionAbortException(txn->get_transaction_id(), AbortReason::UPGRADE_CONFLICT);
            // }

            /*这里延迟插入，预先分配一个rid，但是不插入记录,把记录保存在私有空间中的变量*/
            rid_ = fh_->allocateRid();

            /*写入私有变量集合*/
            auto keyPair = std::make_pair(fh_->GetFd(), rid_);
            auto &private_inserts = txn->get_private_insert();
            TupleMeta new_meta = {temp_ts, false};  // 更新元数据时间戳
            private_inserts[keyPair] = {rec, OperationType::INSERT};  // 存入私有内存
            /*8.更新元组时间戳  这里要更新成临时时间戳防止可见性不一致*/
            fh_->add_tuple_meta(rid_, new_meta);
            
            // /*更新写集合*/
            // auto* wr = new WriteRecord(WType::INSERT_TUPLE, tab_name_, rid_, rec);
            // txn->append_write_record(wr);
        // }
        // else
        // {
        //     // Insert into record file
        //     rid_ = fh_->insert_record(rec.data, context_);
        // }

        return nullptr;
    }

    Rid& rid() override { return rid_; }
};

