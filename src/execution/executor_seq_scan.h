/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once

#include "execution_defs.h"
#include "execution_manager.h"
#include "executor_abstract.h"
#include "index/ix.h"
#include "system/sm.h"
#include "execution_common.h"

class SeqScanExecutor : public AbstractExecutor {
   private:
    std::string tab_name_;              // 表的名称
    std::vector<Condition> conds_;      // scan的条件
    RmFileHandle *fh_;                  // 表的数据文件句柄
    std::vector<ColMeta> cols_;         // scan后生成的记录的字段
    size_t len_;                        // scan后生成的每条记录的长度
    std::vector<Condition> fed_conds_;  // 同conds_，两个字段相同

    Rid rid_;
    std::unique_ptr<RecScan> scan_;     // table_iterator

    SmManager *sm_manager_;


    // bool is_end{false};
    // bool is_find{false};

   public:
    SeqScanExecutor(SmManager *sm_manager, std::string tab_name, std::vector<Condition> conds, Context *context) {
        sm_manager_ = sm_manager;
        tab_name_ = std::move(tab_name);
        conds_ = std::move(conds);
        TabMeta &tab = sm_manager_->db_.get_table(tab_name_);
        fh_ = sm_manager_->fhs_.at(tab_name_).get();
        cols_ = tab.cols;
        len_ = cols_.back().offset + cols_.back().len;

        context_ = context;

        fed_conds_ = conds_;
    }

    void beginTuple() override {
        /*1.定位到第一个元组*/
        scan_ = std::make_unique<RmScan>(fh_);
        rid_ = scan_->rid();
    }

    void nextTuple() override {
        /*1.找到下一条记录*/
        if(!is_end())
        {
            scan_->next();
            rid_ = scan_->rid();
        }
    }

    std::unique_ptr<RmRecord> Next() override {
        /*1.从当前元组开始直到找到下一条符合条件的记录*/
        // is_find = false;
        Transaction *txn = context_->txn_;
        // timestamp_t read_ts = txn->get_read_ts();
        timestamp_t temp_ts = txn->get_transaction_id() + TXN_START_ID;
        TransactionManager *tmr = context_->txn_mgr_;
        TabMeta tabmeta = sm_manager_->db_.get_table(tab_name_);
        while(!is_end())
        {
            is_conflict = false;
            std::unique_ptr<RmRecord> curr_record;
            TupleMeta tuplemeta;
            bool isSelfModify = false;
            auto keyPair = std::make_pair(fh_->GetFd(), rid_);  // 表FD + Rid唯一标识记录
            auto &private_inserts = txn->get_private_insert();
            auto &private_updates = txn->get_private_update();
            auto &vis_record = txn->get_vis_record();
            if (private_inserts.count(keyPair)) {
                /*代表是本事务新插入的*/
                tuplemeta.ts_ = temp_ts;
                tuplemeta.is_deleted_ = false;
                if(private_updates.count(keyPair))
                {
                    curr_record = std::make_unique<RmRecord>(std::get<0>(private_updates[keyPair]));
                }
                else
                {
                    curr_record = std::make_unique<RmRecord>(std::get<0>(private_inserts[keyPair]));
                }
                // isSelfModify = true;
            } else if(private_updates.count(keyPair))
            {
                /*该记录不是本事务插入的，且本是务修改过，将会过滤本是务删除的*/
                /*代表该记录被更新过且被删除*/
                tuplemeta.ts_ = temp_ts;
                tuplemeta.is_deleted_ = false;
                if(std::get<1>(private_updates[keyPair]) == OperationType::DELETE)
                {
                    tuplemeta.is_deleted_ = true;
                    nextTuple();
                    continue;
                }
                curr_record = std::make_unique<RmRecord>(std::get<0>(private_updates[keyPair]));
                // isSelfModify = true;
            } else {
                /*将会过滤可见版本已删除的元组*/
                // 首次修改，从数据页读取原始版本,并读取时间戳
                curr_record = get_visible_record(&tabmeta, fh_, rid_, txn, &is_conflict, context_);
                if(curr_record == nullptr)
                {
                    nextTuple();
                    continue;
                }
                /*缓存历史版本*/
                vis_record[keyPair] = {*(curr_record), OperationType::NONE};
            }

            RmRecord finalrecord(curr_record->size, curr_record->data);

            /*判断是否被删除，包括自己删除和别的事务的删除*/
            if(tmr->get_concurrency_mode() == ConcurrencyMode::MVCC)
            {
            }

            /*1.1判断是否满足过滤条件*/
            /*1.1.1从元组取出对应列的值*/
            bool flag = true;
            for(size_t i = 0; i < fed_conds_.size(); i++)
            {
                Condition cond = fed_conds_[i];
                TabCol ltc = cond.lhs_col;
                Value &rv = cond.rhs_val;
                Value recordColValue;
                auto col = get_col(cols_,ltc);
                std::string str(finalrecord.data + col->offset,col->len);
                switch(col->type)
                {
                    case TYPE_INT:
                        recordColValue.set_int(*reinterpret_cast<const int*>(finalrecord.data + col->offset));
                        break;
                    case TYPE_FLOAT:
                        recordColValue.set_float(*reinterpret_cast<const float*>(finalrecord.data + col->offset));
                        break;
                    case TYPE_STRING:
                        recordColValue.set_str(str.c_str());
                        break;
                    default:
                        throw InternalError("invalid type\n");
                }
                recordColValue.init_raw(col->len);
                if(!cond.ValueIsEqual(recordColValue,rv,cond.op))
                {
                    flag = false;
                    break;
                }
            }
            if(flag)
            {
                return std::make_unique<RmRecord>(finalrecord);
            }
            nextTuple();
        }
        return nullptr;
    }
    
    const std::vector<ColMeta> &cols() const override
    {
        return cols_;
    }
    size_t tupleLen() const override{ return len_; };
    bool is_end() const override{
        return scan_->is_end();
    }

    ColMeta get_col_offset(const TabCol &target) override{
        return *(get_col(cols_,target));
    };

    std::string getType() override {
        return "SeqScanExecutor";
    }

    std::string get_tab_name() override {
        return tab_name_;
    }

    // 获取条件列表，用于判断是否有WHERE条件
    const std::vector<Condition>& get_conditions() const {
        return conds_;
    }

    // 获取文件句柄，用于优化COUNT(*)操作
    RmFileHandle* get_file_handle() const {
        return fh_;
    }

    Rid &rid() override { return rid_; }
};

