/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once
#include "execution_defs.h"
#include "execution_manager.h"
#include "executor_abstract.h"
#include "index/ix.h"
#include "system/sm.h"

class AntijoinExecutor : public AbstractExecutor {
   private:
    std::unique_ptr<AbstractExecutor> left_;    // 左儿子节点（需要join的表）
    std::unique_ptr<AbstractExecutor> right_;   // 右儿子节点（需要join的表）
    size_t len_;                                // join后获得的每条记录的长度
    std::vector<ColMeta> cols_;                 // join后获得的记录的字段

    std::vector<Condition> fed_conds_;          // join条件
    bool isend;
    std::unique_ptr<RmRecord> left_record;

   public:
    AntijoinExecutor(std::unique_ptr<AbstractExecutor> left, std::unique_ptr<AbstractExecutor> right, 
                            std::vector<Condition> conds) {
        /*左右子执行器*/
        left_ = std::move(left);
        right_ = std::move(right);
        /*连接后的元组长度*/
        len_ = left_->tupleLen();
        cols_ = left_->cols();
        isend = false;
        fed_conds_ = std::move(conds);

    }

    void beginTuple() override {
        /*1.把左、右执行器移到第一个元组*/
        left_->beginTuple();
        right_->beginTuple();
        isend = right_->is_end();
        /*2.把左执行器移到第一个满足条件的元组*/
        left_record = left_->Next();
    }

    void nextTuple() override {
        /*1.把左执行器移到下一个元组*/
        /*1.1若右执行器未结束则不动*/
        right_->nextTuple();
        isend = right_->is_end();
        // if(isend)
        // {
        //     /*左执行器移动到下一个元组*/
        //     left_->nextTuple();
        //     left_record = left_->Next();
        //     /*重启右执行器*/
        //     right_->beginTuple();
        //     isend = right_->is_end();
        // }
    }

    std::unique_ptr<RmRecord> Next() override {
        /*1.一次返回连接好的一个元组*/
        auto record = std::make_unique<RmRecord>(len_);
        if(right_->is_end())
        {
            memcpy(record->data, left_record->data, left_->tupleLen());
            left_->nextTuple();
            left_record = left_->Next();
            right_->beginTuple();
            isend = right_->is_end();
            return record;
        }
        while(!isend && !left_->is_end() && left_record)
        {
            /*1.1右执行器还有元组，那么依次遍历剩下的元组*/
            std::unique_ptr<RmRecord> right_record = right_->Next();
            if(right_record)
            {
                /*1.2判断是否连接成功*/
                bool isjoin = false;
                for(auto& cond : fed_conds_)
                {
                    /*如果相等则不行*/
                    if(cond.RecordIsEqual(*left_record,*right_record,left_->cols(),right_->cols()))
                    {
                        isjoin = true;
                        break;
                    }
                }
                /*1.3如果连接成功则重新开始左元组*/
                if(isjoin)
                {
                    left_->nextTuple();
                    left_record = left_->Next();
                    right_->beginTuple();
                    isend = right_->is_end();
                }
                /*1.4当前元组连接失败，右子执行器移动到下一个元组*/
                else
                {
                    right_->nextTuple();
                    isend = right_->is_end();
                    if(isend)
                    {
                        /*1.5如果右子执行器到了结尾，则返回左元组*/
                        /*说明该左元组和右元组无连接元组，直接返回左元组*/
                        memcpy(record->data, left_record->data, left_->tupleLen());
                        left_->nextTuple();
                        left_record = left_->Next();
                        right_->beginTuple();
                        isend = right_->is_end();
                        return record;
                    }
                }
            }
            /*右执行器到末尾了*/
            else
            {
                /*说明该左元组和右元组无连接元组，直接返回左元组*/
                memcpy(record->data, left_record->data, left_->tupleLen());
                left_->nextTuple();
                left_record = left_->Next();
                right_->beginTuple();
                isend = right_->is_end();
                return record;
            }
        }
        return nullptr;
    }

    const std::vector<ColMeta> &cols() const override{
        // std::vector<ColMeta> *_cols = nullptr;
        return cols_;
    };

    size_t tupleLen() const override{ return len_; };
    bool is_end() const override{
        return left_->is_end();
    }
    Rid &rid() override { return _abstract_rid; }
};