/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once

#include "execution/execution_common.h"

auto ReconstructTuple(const TabMeta *schema, const RmRecord &base_tuple, const TupleMeta &base_meta,
                      const std::vector<UndoLog> &undo_logs) -> std::optional<RmRecord>
{
    /*根据undo_logs来重新构建元组，使用增量表的形式*/
    if(undo_logs.empty())
    {
        if(base_meta.is_deleted_)
        {
            return std::nullopt;
        }
    }

    /*若base_meta中显示当前元组已经被删除，但是undolog中出现了删除标记意味着
    * 此删除可以撤销也就是可见的*/
    RmRecord curr_tuple = base_tuple;
    bool is_deleted = base_meta.is_deleted_;
    /*重做undolog*/
    for(auto& undo_log : undo_logs)
    {
        /*基于base_tuple重做undolog*/
        int changeValueIndex = 0;
        auto &modified_fields = undo_log.modified_fields_;
        auto &changeValue = undo_log.tuple_;
        for(int i = 0; i < modified_fields.size(); i++)
        {
            if(modified_fields[i])
            {
                /*该字段被修改*/
                /*获取该字段的表列*/
                auto &col = schema->cols[i];
                /*将该字段修改后的值写到当前元组*/
                auto val = changeValue[changeValueIndex++];
                val.init_raw(col.len);
                memcpy(curr_tuple.data + col.offset, val.raw.get()->data, col.len);
            }
        }
        if(undo_log.is_deleted_ != is_deleted)
        {
            /*undolog的删除标志位标记该条undolog开始前
            * 元组是否删除*/
            is_deleted = undo_log.is_deleted_;
        }
    }
    /*说明被当前时间戳可见的事务删除，那么该读取失败*/
    if(is_deleted)
    {
        return std::nullopt;
    }
    return curr_tuple;
}


auto IsWriteWriteConflict(timestamp_t tuple_ts, Transaction *txn) -> bool
{
    /*1.该元组被其它未提交的事务修改*/
    if(tuple_ts >= TXN_START_ID && tuple_ts != (TXN_START_ID + txn->get_transaction_id()))
    {
        return true;
    }

    /*2.该元组被其它事务修改且提交时间戳大于txn的读取时间戳*/
    if(tuple_ts < TXN_START_ID && tuple_ts > txn->get_read_ts())
    {
        return true;
    }

    return false;
}

/**
 * 从记录中根据表结构提取值并转换为Value对象
 * @param columns 表结构列定义
 * @param record 数据记录
 * @param modifiedvalue_value 存储提取的Value对象的向量
 * @throw InternalError 当遇到不支持的数据类型时抛出
 */
void ExtractValuesFromRecord(const std::vector<ColMeta> &columns, const RmRecord *record,
                             std::vector<Value> &modifiedvalue_value) {
    modifiedvalue_value.clear();
    modifiedvalue_value.reserve(columns.size());
    for (size_t i = 0; i < columns.size(); ++i) {
        const auto &col = columns[i];
        Value value;
        // 根据列偏移和长度提取原始数据
        const char *data_ptr = record->data + col.offset;
        switch (col.type) {
            case TYPE_INT:
                // 处理整型数据
                value.set_int(*reinterpret_cast<const int*>(data_ptr));
                break;
            case TYPE_FLOAT:
                // 处理浮点型数据
                value.set_float(*reinterpret_cast<const float*>(data_ptr));
                break;
            case TYPE_STRING:
                // 处理字符串数据
                value.set_str(std::string(data_ptr, col.len).c_str());
                break;
            default:
                throw InternalError("Invalid data type in record extraction");
        }
        modifiedvalue_value.push_back(value);
    }
}

/**
 * 从记录中提取指定列的值并生成修改标记
 * @param table 表结构信息
 * @param change_cols 需要提取的列信息列表
 * @param record 数据记录
 * @param modified_values 输出参数：提取的列值
 * @param modified_flags 输出参数：对应列是否被修改的标记
 * @throws InternalError 当列类型不支持或列名不存在时抛出异常
 */
void ExtractModifiedValues(
    TabMeta& table, 
    const std::vector<TabCol> &change_cols, 
    const RmRecord &record,
    std::vector<Value> &modified_values,
    std::vector<bool> &modified_flags
) {
    // 初始化标记向量，默认所有列未被修改
    modified_flags.assign(table.cols.size(), false);
    modified_values.clear();
    
    // 遍历需要修改的列
    for (const auto &col : change_cols) {
        // 查找列元数据
        auto colmeta = table.get_col(col.col_name);
        
        // 从记录中提取值
        Value value;
        std::string str(record.data + colmeta->offset, colmeta->len);
        
        // 根据列类型转换值
        switch (colmeta->type) {
            case TYPE_INT:
                value.set_int(*reinterpret_cast<const int*>(record.data + colmeta->offset));
                break;
            case TYPE_FLOAT:
                value.set_float(*reinterpret_cast<const float*>(record.data + colmeta->offset));
                break;
            case TYPE_STRING:
                value.set_str(str.c_str());
                break;
            default:
                throw InternalError("Unsupported column type: " + std::to_string(colmeta->type));
        }
        
        // 添加值到结果向量
        modified_values.push_back(value);
        
        // 计算列在表结构中的索引位置
        size_t index = std::distance(table.cols.begin(), colmeta);
        modified_flags[index] = true;
    }
}


/**
 * 比较两个记录的对应列，提取不同的列值并生成差异标记
 * @param table 表结构信息
 * @param record1 第一个记录
 * @param record2 第二个记录
 * @param modified_values 输出参数：存储record1中与record2不同的列值
 * @param modified_flags 输出参数：标记对应列是否不同（true表示不同）
 * @throws InternalError 当遇到不支持的数据类型时抛出
 */
void ExtractDifferentValues(
    const TabMeta& table,
    const RmRecord& record1,
    const RmRecord& record2,
    std::vector<Value>& modified_values,
    std::vector<bool>& modified_flags
) {
    // 初始化结果容器
    modified_values.clear();
    modified_flags.assign(table.cols.size(), false);
    
    // 遍历表中所有列
    for (size_t i = 0; i < table.cols.size(); ++i) {
        const auto& col = table.cols[i];
        
        // 从两个记录中提取当前列的值
        Value value1, value2;
        try {
            // 提取record1的值
            const char* data_ptr1 = record1.data + col.offset;
            switch (col.type) {
                case TYPE_INT:
                    value1.set_int(*reinterpret_cast<const int*>(data_ptr1));
                    break;
                case TYPE_FLOAT:
                    value1.set_float(*reinterpret_cast<const float*>(data_ptr1));
                    break;
                case TYPE_STRING:
                    value1.set_str(std::string(data_ptr1, col.len).c_str());
                    break;
                default:
                    throw InternalError("Unsupported data type in record comparison");
            }
            
            // 提取record2的值
            const char* data_ptr2 = record2.data + col.offset;
            switch (col.type) {
                case TYPE_INT:
                    value2.set_int(*reinterpret_cast<const int*>(data_ptr2));
                    break;
                case TYPE_FLOAT:
                    value2.set_float(*reinterpret_cast<const float*>(data_ptr2));
                    break;
                case TYPE_STRING:
                    value2.set_str(std::string(data_ptr2, col.len).c_str());
                    break;
                default:
                    throw InternalError("Unsupported data type in record comparison");
            }
        } catch (const InternalError& e) {
            throw InternalError("Error extracting values for comparison: " + std::string(e.what()));
        }
        
        // 比较两个值是否不同
        Condition cond;
        if (cond.ValueIsEqual(value1, value2, OP_NE)) {
            // 值不同，记录到结果中
            modified_values.push_back(value1);
            modified_flags[i] = true;
        }
    }
}

// 从数据页读取原始版本，并检查可见性（需回溯到可见版本）
std::unique_ptr<RmRecord> get_visible_record(const TabMeta *schema, RmFileHandle *fh, Rid rid, Transaction *txn, bool *isWWconflict, Context *context) {
    // 1. 获取当前版本的元数据
    TupleMeta current_meta = fh->get_tuple_meta(rid);
    // 2. 获取事务的读取时间戳（不同隔离级别可能有差异，这里以可重复读为例）
    timestamp_t read_ts = txn->get_read_ts();

    // 3. 检查当前版本是否可见 
    if (current_meta.ts_ > read_ts) {
        if(current_meta.ts_>=TXN_START_ID && current_meta.ts_ != txn->get_transaction_id() + TXN_START_ID)
        {
            /*活跃事务新插入的，在这里只有可能是其它事务的*/
            return nullptr;
        }
        // 版本的生成时间晚于事务的读取时间，不可见，需回溯
        *isWWconflict = true;
        // std::fstream outfile;
        // outfile.open("output1.txt", std::ios::out | std::ios::app);
        // outfile << "scan conflict " << "fd: " << fh->GetFd() << " rid " << rid.page_no << " "<< rid.slot_no << " " << 
        // " curr_tm " << read_ts << " conflict_tm " << current_meta.ts_ << std::endl;
        // outfile.close();
    } else {
        if(current_meta.is_deleted_)
        {
            return nullptr;
        }
        // 当前版本可见，直接返回
        return fh->get_record(rid, context);
    }

    // 4. 若当前版本不可见，通过 undo log 回溯到可见版本
    auto curr_ts = current_meta.ts_;
    auto undo_link = context->txn_mgr_->GetUndoLink(fh->GetFd(), rid);
    std::vector<UndoLog> undo_logs;/*存放要做的undo*/
    auto undo_log = context->txn_mgr_->GetUndoLogOptional(undo_link.value());
    // bool isfirst = true;
    while(undo_log.has_value() && (curr_ts > read_ts))
    {
        // isfirst = false;
        curr_ts = undo_log.value().ts_;
        undo_logs.push_back(undo_log.value());
        undo_link = undo_log.value().prev_version_;
        undo_log = context->txn_mgr_->GetUndoLogOptional(undo_link.value());
    }
    std::unique_ptr<RmRecord> record = fh->get_record(rid, context);
    if(undo_logs.empty() || undo_logs.back().ts_ > read_ts)
    {
        return nullptr;
    }
    auto prev_old_tuple = ReconstructTuple(schema, *record, current_meta, undo_logs);  // 用 undo log 恢复旧版本
    if(!prev_old_tuple.has_value())
    {
        return nullptr;
    }
    return std::make_unique<RmRecord>(prev_old_tuple.value());
}