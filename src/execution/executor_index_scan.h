#pragma once

#include "execution_defs.h"
#include "execution_manager.h"
#include "executor_abstract.h"
#include "index/ix.h"
#include "system/sm.h"
#include <sstream>
#include <iomanip>  // <— for std::setw, std::setfill
#include <unordered_set>
#include <cmath>     // for std::nextafter
#include <limits>    // for std::numeric_limits


class IndexScanExecutor : public AbstractExecutor {
   private:
    std::string tab_name_;                      // 表名称
    TabMeta tab_;                               // 表的元数据
    std::vector<Condition> conds_;              // 扫描条件
    RmFileHandle *fh_;                          // 表的数据文件句柄
    std::vector<ColMeta> cols_;                 // 需要读取的字段
    size_t len_;                                // 选取出来的一条记录的长度
    std::vector<Condition> fed_conds_;          // 扫描条件，和conds_字段相同
    std::vector<Condition> conds_to_eval_;      // 未参与索引范围构造的剩余条件

    std::vector<std::string> index_col_names_;  // index scan涉及到的索引包含的字段
    IndexMeta index_meta_;                      // index scan涉及到的索引元数据

    Rid rid_;
    std::unique_ptr<IxScan> scan_;

    SmManager *sm_manager_;

    bool isCacheHaveRecord = false;
    bool cacheRecordIndex = 0;
    std::unordered_map<
        std::pair<int, Rid>,
        std::tuple<RmRecord, OperationType>
    > private_updates_;
    std::unordered_map<
        std::pair<int, Rid>,
        std::tuple<RmRecord, OperationType>
    > private_inserts_;
    std::unordered_map<std::pair<int, Rid>, std::tuple<RmRecord, OperationType>> ::iterator current_iter_;
    bool is_traversed_all_ = false;
    bool is_findself = false;
    bool is_scan_first_valid = true;

    std::unique_ptr<RmRecord> fitCondRec;

   public:
    IndexScanExecutor(SmManager *sm_manager, std::string tab_name, std::vector<Condition> conds, std::vector<std::string> index_col_names,
                    Context *context) {
        sm_manager_ = sm_manager;
        context_ = context;
        tab_name_ = std::move(tab_name);
        tab_ = sm_manager_->db_.get_table(tab_name_);
        conds_ = std::move(conds);
        // index_no_ = index_no;
        index_col_names_ = index_col_names; 
        index_meta_ = *(tab_.get_index_meta(index_col_names_));
        fh_ = sm_manager_->fhs_.at(tab_name_).get();
        cols_ = tab_.cols;
        len_ = cols_.back().offset + cols_.back().len;
        std::map<CompOp, CompOp> swap_op = {
            {OP_EQ, OP_EQ}, {OP_NE, OP_NE}, {OP_LT, OP_GT}, {OP_GT, OP_LT}, {OP_LE, OP_GE}, {OP_GE, OP_LE},
        };

        for (auto &cond : conds_) {
            if (cond.lhs_col.tab_name != tab_name_) {
                // lhs is on other table, now rhs must be on this table
                assert(!cond.is_rhs_val && cond.rhs_col.tab_name == tab_name_);
                // swap lhs and rhs
                std::swap(cond.lhs_col, cond.rhs_col);
                cond.op = swap_op.at(cond.op);
            }
        }
        fed_conds_ = conds_;

        private_updates_ = context_->txn_->get_private_update();
        private_inserts_ = context_->txn_->get_private_insert();
    }

    std::string getType() { return "indexScan"; }

void beginTuple() override {
    check_runtime_conds();
    // std::cout << "Entering beginTuple()" << std::endl;

    // 获取索引句柄
    auto ih = sm_manager_->ihs_.at(
        sm_manager_->get_ix_manager()->get_index_name(tab_name_, index_col_names_, sm_manager_->db_)
    ).get();
    BufferPoolManager *bpm = sm_manager_->get_bpm();

    Iid lower = ih->leaf_begin();
    Iid upper = ih->leaf_end();

    
    // 构造联合 key buffer
    int key_len = index_meta_.col_tot_len;
    std::vector<char> lower_key(key_len, 0x00);
    std::vector<char> upper_key(key_len, 0x7F);

    int prefix_len = 0;
    std::vector<Condition> matched_conds;
    bool found_range_cond = false;
    std::string range_col_used;
    bool use_lower_with_upper_bound = false; // for OP_GT
    bool use_upper_with_lower_bound = false; // for OP_LT

    // 第一步：处理等值条件作为前缀
    for (int i = 0; i < index_meta_.col_num; ++i) {
        const auto &index_col = index_meta_.cols[i];

        auto cond_it = std::find_if(fed_conds_.begin(), fed_conds_.end(), [&](const Condition &cond) {
            return cond.lhs_col.tab_name == tab_name_
                && cond.lhs_col.col_name == index_col.name
                && cond.is_rhs_val
                && cond.op == OP_EQ;
        });

        if (cond_it == fed_conds_.end()) {
            break; // 只处理连续前缀
        }

        const char *rhs_key = cond_it->rhs_val.raw->data;
        memcpy(lower_key.data() + prefix_len, rhs_key, index_col.len);
        memcpy(upper_key.data() + prefix_len, rhs_key, index_col.len);
        prefix_len += index_col.len;
        matched_conds.push_back(*cond_it);
    }

    // 第二步：处理第一个范围条件（如果存在）
    if (prefix_len < index_meta_.col_tot_len) {
        // 计算已经处理了多少列
        int processed_cols = 0;
        int temp_offset = 0;
        for (int i = 0; i < index_meta_.col_num && temp_offset < prefix_len; ++i) {
            temp_offset += index_meta_.cols[i].len;
            processed_cols = i + 1;
        }
        
        if (processed_cols < index_meta_.col_num) {
            const auto &index_col = index_meta_.cols[processed_cols];
        
            // 查找该字段的所有范围条件
            std::vector<Condition> range_conds;
            for (const auto &cond : fed_conds_) {
                if (cond.lhs_col.tab_name == tab_name_
                    && cond.lhs_col.col_name == index_col.name
                    && cond.is_rhs_val
                    && (cond.op == OP_LT || cond.op == OP_LE || cond.op == OP_GT || cond.op == OP_GE)) {
                    range_conds.push_back(cond);
                }
            }

            if (!range_conds.empty()) {
                found_range_cond = true;
                range_col_used = index_col.name;
                /* debug print removed */
                
                // 处理下界条件 (>=, >)
                auto lower_cond_it = std::find_if(range_conds.begin(), range_conds.end(), 
                    [](const Condition &cond) { return cond.op == OP_GE || cond.op == OP_GT; });
                
                if (lower_cond_it != range_conds.end()) {
                    const char *rhs_key = lower_cond_it->rhs_val.raw->data;
                    // 统一不做值偏移：
                    // GE: lower = lower_bound(v)
                    // GT: lower = upper_bound(v)
                    memcpy(lower_key.data() + prefix_len, rhs_key, index_col.len);
                    use_lower_with_upper_bound = (lower_cond_it->op == OP_GT);
                }
                
                // 处理上界条件 (<, <=)
                auto upper_cond_it = std::find_if(range_conds.begin(), range_conds.end(), 
                    [](const Condition &cond) { return cond.op == OP_LT || cond.op == OP_LE; });
                
                if (upper_cond_it != range_conds.end()) {
                    const char *rhs_key = upper_cond_it->rhs_val.raw->data;
                    // 统一不做值偏移：
                    // LT: upper = lower_bound(v)
                    // LE: upper = upper_bound(v)
                    memcpy(upper_key.data() + prefix_len, rhs_key, index_col.len);
                    use_upper_with_lower_bound = (upper_cond_it->op == OP_LT);
                }
                
                prefix_len += index_col.len;
                matched_conds.insert(matched_conds.end(), range_conds.begin(), range_conds.end());
            }
        }
    }

    // 第三步：用类型感知方式补齐剩余字段
    if (prefix_len > 0) {
        int offset = prefix_len;
        // 计算已经处理了多少列
        int processed_cols = 0;
        int temp_offset = 0;
        for (int i = 0; i < index_meta_.col_num && temp_offset < prefix_len; ++i) {
            temp_offset += index_meta_.cols[i].len;
            processed_cols = i + 1;
        }
        
        // 为剩余列设置默认值
        for (int i = processed_cols; i < index_meta_.col_num; ++i) {
            const auto &col = index_meta_.cols[i];
            if (col.type == TYPE_INT) {
                int32_t min_val = INT32_MIN;
                int32_t max_val = INT32_MAX;
                memcpy(lower_key.data() + offset, &min_val, col.len);
                memcpy(upper_key.data() + offset, &max_val, col.len);
            } else if (col.type == TYPE_FLOAT) {
                float min_val = -__FLT_MAX__;  // 或使用 std::numeric_limits<float>::lowest()
                float max_val = __FLT_MAX__;
                memcpy(lower_key.data() + offset, &min_val, col.len);
                memcpy(upper_key.data() + offset, &max_val, col.len);
            } else if (col.type == TYPE_STRING) {
                memset(lower_key.data() + offset, 0x00, col.len);
                memset(upper_key.data() + offset, 0x7F, col.len);
            }
            offset += col.len;
        }

        // 只有在存在等值前缀或范围条件时才根据键定位边界
        if (prefix_len > 0 || found_range_cond) {
            if (use_lower_with_upper_bound) {
                lower = ih->upper_bound(lower_key.data());
            } else {
                lower = ih->lower_bound(lower_key.data());
            }
            if (use_upper_with_lower_bound) {
                upper = ih->lower_bound(upper_key.data());
            } else {
                upper = ih->upper_bound(upper_key.data());
            }
        }

    }

    // 构建 IxScan 执行器
    scan_ = std::make_unique<IxScan>(ih, lower, upper, bpm);

    conds_to_eval_.clear();
    conds_to_eval_.reserve(fed_conds_.size());
    // 收集已使用的等值列名
    std::unordered_set<std::string> used_eq_cols;
    for (const auto &mc : matched_conds) {
        if (mc.op == OP_EQ) {
            used_eq_cols.insert(mc.lhs_col.col_name);
        }
    }
    for (const auto &c : fed_conds_) {
        bool skip = false;
        // 跳过等值前缀中已使用的等值条件
        if (c.op == OP_EQ && used_eq_cols.count(c.lhs_col.col_name)) {
            skip = true;
        }
        // 保留范围列上的范围条件，用于严格比较的行级校验
        if (!skip) conds_to_eval_.push_back(c);
    }


    Transaction *txn = context_->txn_;
    timestamp_t read_ts = txn->get_read_ts();
    TransactionManager *tmr = context_->txn_mgr_;
    timestamp_t temp_ts = txn->get_transaction_id() + TXN_START_ID;
    TabMeta tabmeta = sm_manager_->db_.get_table(tab_name_);
    auto &private_insert = txn->get_private_insert();
    current_iter_ = private_insert.begin();

    /*首先检查事务缓存中是否有满足条件的记录，如果有直接返回*/
    if(findNextMatching())
    {
        return;
    }
    auto &vis_record = txn->get_vis_record();
    /*说明新插的元组不满足要求*/
    // 滤除不符合 fed_conds_ 的记录
    while (!scan_->is_end()) {
        is_conflict = false;
        is_scan_first_valid = false;
        rid_ = scan_->rid();  // 如果为空也不会崩，因为 is_end() 已判断
        bool isSelfModify = false;
        auto keyPair = std::make_pair(fh_->GetFd(), rid_);  // 表FD + Rid唯一标识记录
        if(private_updates_.count(keyPair))
        {
            /*该记录被本事务修改过*/
            /*代表该记录被更新过且被删除*/
            if(std::get<1>(private_updates_[keyPair]) == OperationType::DELETE)
            {
                scan_->next();
                continue;
            }
            fitCondRec = std::make_unique<RmRecord>(std::get<0>(private_updates_[keyPair]));
            isSelfModify = true;
        } else {
            // bool isWWconflict = false;
            // 首次修改，从数据页读取原始版本,并读取时间戳
            fitCondRec = get_visible_record(&tabmeta, fh_, rid_, txn, &is_conflict, context_);
            if(fitCondRec == nullptr)
            {
                scan_->next();
                continue;
            }
            vis_record[keyPair] = {*(fitCondRec), OperationType::NONE};
        }

        if (eval_conds(cols_, conds_to_eval_, fitCondRec.get())) {
            break;
        }
        scan_->next();
    }

    // std::cout << "Exiting beginTuple()" << std::endl;
}


    void nextTuple() override {
        is_conflict = false;
        /*首先检查事务缓存中是否有满足条件的记录，如果有直接返回*/
        if(findNextMatching())
        {
            return;
        }
        // std::cout << "Entering nextTuple()" << std::endl;
        Transaction *txn = context_->txn_;
        timestamp_t read_ts = txn->get_read_ts();
        timestamp_t temp_ts = txn->get_transaction_id() + TXN_START_ID;
        TransactionManager *tmr = context_->txn_mgr_;
        TabMeta tabmeta = sm_manager_->db_.get_table(tab_name_);
        check_runtime_conds();
        if(!is_scan_first_valid)
        {
            if(!scan_->is_end())
            {
                scan_->next();
            }
        }
        else
        {
            is_scan_first_valid = false;
        }
        auto &vis_record = txn->get_vis_record();
        while ( !scan_->is_end() ) {
            // scan_->next();
            is_conflict = false;
            rid_ = scan_->rid();  // 如果为空也不会崩，因为 is_end() 已判断
            bool isSelfModify = false;
            auto keyPair = std::make_pair(fh_->GetFd(), rid_);  // 表FD + Rid唯一标识记录
            if(private_updates_.count(keyPair))
            {
                /*该记录被本事务修改过*/
                /*代表该记录被更新过且被删除*/
                if(std::get<1>(private_updates_[keyPair]) == OperationType::DELETE)
                {
                    scan_->next();
                    continue;
                }
                fitCondRec = std::make_unique<RmRecord>(std::get<0>(private_updates_[keyPair]));
                isSelfModify = true;
            } else {
                // bool isWWconflict = false;
                // 首次修改，从数据页读取原始版本,并读取时间戳
                fitCondRec = get_visible_record(&tabmeta, fh_, rid_, txn, &is_conflict, context_);
                if(fitCondRec == nullptr)
                {
                    scan_->next();
                    continue;
                }
                vis_record[keyPair] = {*(fitCondRec), OperationType::NONE};
            }

            if (eval_conds(cols_, conds_to_eval_, fitCondRec.get())) {
                break;
            }
            scan_->next();
        }
    }


    bool is_end() const override { return (!is_findself) && scan_->is_end(); }

    size_t tupleLen() const override { return len_; }

    const std::vector<ColMeta> &cols() const override { return cols_; }

    std::string get_tab_name() override {
        return tab_name_;
    }

    std::unique_ptr<RmRecord> Next() override {
        // assert(!is_end());
        if(is_end())
        {
            return nullptr;
        }
        if(is_findself)
        {
            is_findself = false;
        }
        // return fh_->get_record(rid_, context_);
        return std::move(fitCondRec);
    }

    void feed(const std::map<TabCol, Value> &feed_dict) override {
        fed_conds_ = conds_;
        for (auto &cond : fed_conds_) {
            // lab3 task2 todo
            // 参考seqscan
            // lab3 task2 todo end
			if (!cond.is_rhs_val && cond.rhs_col.tab_name != tab_name_) {
                cond.is_rhs_val = true;
                cond.rhs_val = feed_dict.at(cond.rhs_col);
            }
        }
        check_runtime_conds();
    }

    Rid &rid() override { return rid_; }

    void check_runtime_conds() {
        for (auto &cond : fed_conds_) {
            assert(cond.lhs_col.tab_name == tab_name_);
            if (!cond.is_rhs_val) {
                assert(cond.rhs_col.tab_name == tab_name_);
            }
        }
    }

    bool eval_cond(const std::vector<ColMeta> &rec_cols, const Condition &cond, const RmRecord *rec) {
        auto lhs_col = get_col(rec_cols, cond.lhs_col);
        char *lhs = rec->data + lhs_col->offset;
        char *rhs;
        ColType rhs_type;
        if (cond.is_rhs_val) {
            rhs_type = cond.rhs_val.type;
            rhs = cond.rhs_val.raw->data;
        } else {
            // rhs is a column
            auto rhs_col = get_col(rec_cols, cond.rhs_col);
            rhs_type = rhs_col->type;
            rhs = rec->data + rhs_col->offset;
        }
        assert(rhs_type == lhs_col->type);  // TODO convert to common type
        int cmp = ix_compare(lhs, rhs, rhs_type, lhs_col->len);
        if (cond.op == OP_EQ) {
            return cmp == 0;
        } else if (cond.op == OP_NE) {
            return cmp != 0;
        } else if (cond.op == OP_LT) {
            return cmp < 0;
        } else if (cond.op == OP_GT) {
            return cmp > 0;
        } else if (cond.op == OP_LE) {
            return cmp <= 0;
        } else if (cond.op == OP_GE) {
            return cmp >= 0;
        } else {
            throw InternalError("Unexpected op type");
        }
    }

    bool eval_conds(const std::vector<ColMeta> &rec_cols, const std::vector<Condition> &conds, const RmRecord *rec) {
        return std::all_of(conds.begin(), conds.end(),
                           [&](const Condition &cond) { return eval_cond(rec_cols, cond, rec); });
    }

    ColMeta get_col_offset(const TabCol &target) override{
        return *(get_col(cols_,target));
    };

    bool findNextMatching() {
        // 若已遍历完所有元素，直接返回false
        if (is_traversed_all_) {
            return false;
        }

        // 从当前迭代器开始遍历
        auto it = current_iter_;
        for (; it != private_inserts_.end(); ++it) {
            auto& val = it->second;
            RmRecord& rec1 = std::get<0>(val);

            if(private_updates_.count(it->first))
            {
                /*如果被更新则用更新后的结果*/
                rec1 = std::get<0>(private_updates_[it->first]);
            }

            if (eval_conds(cols_, fed_conds_, &rec1)) {
                current_iter_ = std::next(it);
                // 若下一个位置是结尾，标记为已遍历完
                if (current_iter_ == private_inserts_.end()) {
                    is_traversed_all_ = true;
                }
                is_findself = true;
                fitCondRec = std::make_unique<RmRecord>(rec1);
                rid_ = it->first.second;
                return true;
            }
        }

        // 遍历完未找到符合条件的记录，标记为已遍历完并返回false
        is_traversed_all_ = true;
        return false;
    }

};