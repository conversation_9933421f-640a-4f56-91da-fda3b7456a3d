/* A Bison parser, made by GNU Bison 3.8.2.  */

/* Bison implementation for Yacc-like parsers in C

   Copyright (C) 1984, 1989-1990, 2000-2015, 2018-2021 Free Software Foundation,
   Inc.

   This program is free software: you can redistribute it and/or modify
   it under the terms of the GNU General Public License as published by
   the Free Software Foundation, either version 3 of the License, or
   (at your option) any later version.

   This program is distributed in the hope that it will be useful,
   but WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
   GNU General Public License for more details.

   You should have received a copy of the GNU General Public License
   along with this program.  If not, see <https://www.gnu.org/licenses/>.  */

/* As a special exception, you may create a larger work that contains
   part or all of the Bison parser skeleton and distribute that work
   under terms of your choice, so long as that work isn't itself a
   parser generator using the skeleton or a modified version thereof
   as a parser skeleton.  Alternatively, if you modify or redistribute
   the parser skeleton itself, you may (at your option) remove this
   special exception, which will cause the skeleton and the resulting
   Bison output files to be licensed under the GNU General Public
   License without this special exception.

   This special exception was added by the Free Software Foundation in
   version 2.2 of Bison.  */

/* C LALR(1) parser skeleton written by <PERSON>, by
   simplifying the original so-called "semantic" parser.  */

/* DO NOT RELY ON FEATURES THAT ARE NOT DOCUMENTED in the manual,
   especially those whose name start with YY_ or yy_.  They are
   private implementation details that can be changed or removed.  */

/* All symbols defined below should begin with yy or YY, to avoid
   infringing on user name space.  This should be done even for local
   variables, as they might otherwise be expanded by user macros.
   There are some unavoidable exceptions within include files to
   define necessary library symbols; they are noted "INFRINGES ON
   USER NAME SPACE" below.  */

/* Identify Bison output, and Bison version.  */
#define YYBISON 30802

/* Bison version string.  */
#define YYBISON_VERSION "3.8.2"

/* Skeleton name.  */
#define YYSKELETON_NAME "yacc.c"

/* Pure parsers.  */
#define YYPURE 2

/* Push parsers.  */
#define YYPUSH 0

/* Pull parsers.  */
#define YYPULL 1




/* First part of user prologue.  */
#line 1 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"

#include "ast.h"
#include "yacc.tab.h"
#include <iostream>
#include <memory>

int yylex(YYSTYPE *yylval, YYLTYPE *yylloc);

void yyerror(YYLTYPE *locp, const char* s) {
    std::cerr << "Parser Error at line " << locp->first_line << " column " << locp->first_column << ": " << s << std::endl;
}

using namespace ast;

#line 86 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"

# ifndef YY_CAST
#  ifdef __cplusplus
#   define YY_CAST(Type, Val) static_cast<Type> (Val)
#   define YY_REINTERPRET_CAST(Type, Val) reinterpret_cast<Type> (Val)
#  else
#   define YY_CAST(Type, Val) ((Type) (Val))
#   define YY_REINTERPRET_CAST(Type, Val) ((Type) (Val))
#  endif
# endif
# ifndef YY_NULLPTR
#  if defined __cplusplus
#   if 201103L <= __cplusplus
#    define YY_NULLPTR nullptr
#   else
#    define YY_NULLPTR 0
#   endif
#  else
#   define YY_NULLPTR ((void*)0)
#  endif
# endif

#include "yacc.tab.h"
/* Symbol kind.  */
enum yysymbol_kind_t
{
  YYSYMBOL_YYEMPTY = -2,
  YYSYMBOL_YYEOF = 0,                      /* "end of file"  */
  YYSYMBOL_YYerror = 1,                    /* error  */
  YYSYMBOL_YYUNDEF = 2,                    /* "invalid token"  */
  YYSYMBOL_SHOW = 3,                       /* SHOW  */
  YYSYMBOL_TABLES = 4,                     /* TABLES  */
  YYSYMBOL_CREATE = 5,                     /* CREATE  */
  YYSYMBOL_TABLE = 6,                      /* TABLE  */
  YYSYMBOL_DROP = 7,                       /* DROP  */
  YYSYMBOL_DESC = 8,                       /* DESC  */
  YYSYMBOL_INSERT = 9,                     /* INSERT  */
  YYSYMBOL_INTO = 10,                      /* INTO  */
  YYSYMBOL_VALUES = 11,                    /* VALUES  */
  YYSYMBOL_DELETE = 12,                    /* DELETE  */
  YYSYMBOL_FROM = 13,                      /* FROM  */
  YYSYMBOL_ASC = 14,                       /* ASC  */
  YYSYMBOL_EXPLAIN = 15,                   /* EXPLAIN  */
  YYSYMBOL_ORDER = 16,                     /* ORDER  */
  YYSYMBOL_BY = 17,                        /* BY  */
  YYSYMBOL_ON = 18,                        /* ON  */
  YYSYMBOL_SEMI = 19,                      /* SEMI  */
  YYSYMBOL_COUNT = 20,                     /* COUNT  */
  YYSYMBOL_MIN = 21,                       /* MIN  */
  YYSYMBOL_MAX = 22,                       /* MAX  */
  YYSYMBOL_AVG = 23,                       /* AVG  */
  YYSYMBOL_SUM = 24,                       /* SUM  */
  YYSYMBOL_GROUP = 25,                     /* GROUP  */
  YYSYMBOL_HAVING = 26,                    /* HAVING  */
  YYSYMBOL_AS = 27,                        /* AS  */
  YYSYMBOL_LOAD = 28,                      /* LOAD  */
  YYSYMBOL_ANTI = 29,                      /* ANTI  */
  YYSYMBOL_WHERE = 30,                     /* WHERE  */
  YYSYMBOL_UPDATE = 31,                    /* UPDATE  */
  YYSYMBOL_SET = 32,                       /* SET  */
  YYSYMBOL_SELECT = 33,                    /* SELECT  */
  YYSYMBOL_INT = 34,                       /* INT  */
  YYSYMBOL_CHAR = 35,                      /* CHAR  */
  YYSYMBOL_FLOAT = 36,                     /* FLOAT  */
  YYSYMBOL_INDEX = 37,                     /* INDEX  */
  YYSYMBOL_AND = 38,                       /* AND  */
  YYSYMBOL_JOIN = 39,                      /* JOIN  */
  YYSYMBOL_EXIT = 40,                      /* EXIT  */
  YYSYMBOL_HELP = 41,                      /* HELP  */
  YYSYMBOL_TXN_BEGIN = 42,                 /* TXN_BEGIN  */
  YYSYMBOL_TXN_COMMIT = 43,                /* TXN_COMMIT  */
  YYSYMBOL_TXN_ABORT = 44,                 /* TXN_ABORT  */
  YYSYMBOL_TXN_ROLLBACK = 45,              /* TXN_ROLLBACK  */
  YYSYMBOL_ORDER_BY = 46,                  /* ORDER_BY  */
  YYSYMBOL_ENABLE_NESTLOOP = 47,           /* ENABLE_NESTLOOP  */
  YYSYMBOL_ENABLE_SORTMERGE = 48,          /* ENABLE_SORTMERGE  */
  YYSYMBOL_STATIC_CHECKPOINT = 49,         /* STATIC_CHECKPOINT  */
  YYSYMBOL_LIMIT = 50,                     /* LIMIT  */
  YYSYMBOL_LEQ = 51,                       /* LEQ  */
  YYSYMBOL_NEQ = 52,                       /* NEQ  */
  YYSYMBOL_GEQ = 53,                       /* GEQ  */
  YYSYMBOL_T_EOF = 54,                     /* T_EOF  */
  YYSYMBOL_IDENTIFIER = 55,                /* IDENTIFIER  */
  YYSYMBOL_VALUE_STRING = 56,              /* VALUE_STRING  */
  YYSYMBOL_PATH_TOKEN = 57,                /* PATH_TOKEN  */
  YYSYMBOL_VALUE_INT = 58,                 /* VALUE_INT  */
  YYSYMBOL_VALUE_FLOAT = 59,               /* VALUE_FLOAT  */
  YYSYMBOL_VALUE_BOOL = 60,                /* VALUE_BOOL  */
  YYSYMBOL_61_ = 61,                       /* ';'  */
  YYSYMBOL_62_ = 62,                       /* '='  */
  YYSYMBOL_63_ = 63,                       /* '('  */
  YYSYMBOL_64_ = 64,                       /* ')'  */
  YYSYMBOL_65_ = 65,                       /* ','  */
  YYSYMBOL_66_ = 66,                       /* '*'  */
  YYSYMBOL_67_ = 67,                       /* '.'  */
  YYSYMBOL_68_ = 68,                       /* '<'  */
  YYSYMBOL_69_ = 69,                       /* '>'  */
  YYSYMBOL_70_ = 70,                       /* '+'  */
  YYSYMBOL_71_ = 71,                       /* '-'  */
  YYSYMBOL_72_ = 72,                       /* '/'  */
  YYSYMBOL_YYACCEPT = 73,                  /* $accept  */
  YYSYMBOL_start = 74,                     /* start  */
  YYSYMBOL_stmt = 75,                      /* stmt  */
  YYSYMBOL_txnStmt = 76,                   /* txnStmt  */
  YYSYMBOL_dbStmt = 77,                    /* dbStmt  */
  YYSYMBOL_setStmt = 78,                   /* setStmt  */
  YYSYMBOL_ddl = 79,                       /* ddl  */
  YYSYMBOL_dml = 80,                       /* dml  */
  YYSYMBOL_explainStmt = 81,               /* explainStmt  */
  YYSYMBOL_loadStmt = 82,                  /* loadStmt  */
  YYSYMBOL_fieldList = 83,                 /* fieldList  */
  YYSYMBOL_colNameList = 84,               /* colNameList  */
  YYSYMBOL_field = 85,                     /* field  */
  YYSYMBOL_type = 86,                      /* type  */
  YYSYMBOL_valueList = 87,                 /* valueList  */
  YYSYMBOL_value = 88,                     /* value  */
  YYSYMBOL_condition = 89,                 /* condition  */
  YYSYMBOL_optWhereClause = 90,            /* optWhereClause  */
  YYSYMBOL_whereClause = 91,               /* whereClause  */
  YYSYMBOL_opt_group_by = 92,              /* opt_group_by  */
  YYSYMBOL_opt_having = 93,                /* opt_having  */
  YYSYMBOL_havingClause = 94,              /* havingClause  */
  YYSYMBOL_havingCondition = 95,           /* havingCondition  */
  YYSYMBOL_aggExpr = 96,                   /* aggExpr  */
  YYSYMBOL_col = 97,                       /* col  */
  YYSYMBOL_colList = 98,                   /* colList  */
  YYSYMBOL_op = 99,                        /* op  */
  YYSYMBOL_expr = 100,                     /* expr  */
  YYSYMBOL_setClauses = 101,               /* setClauses  */
  YYSYMBOL_setClause = 102,                /* setClause  */
  YYSYMBOL_valuexpr = 103,                 /* valuexpr  */
  YYSYMBOL_selector = 104,                 /* selector  */
  YYSYMBOL_select_item = 105,              /* select_item  */
  YYSYMBOL_tableExpression = 106,          /* tableExpression  */
  YYSYMBOL_tableRef = 107,                 /* tableRef  */
  YYSYMBOL_optAlias = 108,                 /* optAlias  */
  YYSYMBOL_opt_order_clause = 109,         /* opt_order_clause  */
  YYSYMBOL_order_clause = 110,             /* order_clause  */
  YYSYMBOL_opt_asc_desc = 111,             /* opt_asc_desc  */
  YYSYMBOL_opt_limit = 112,                /* opt_limit  */
  YYSYMBOL_set_knob_type = 113,            /* set_knob_type  */
  YYSYMBOL_tbName = 114,                   /* tbName  */
  YYSYMBOL_filename = 115,                 /* filename  */
  YYSYMBOL_colName = 116                   /* colName  */
};
typedef enum yysymbol_kind_t yysymbol_kind_t;




#ifdef short
# undef short
#endif

/* On compilers that do not define __PTRDIFF_MAX__ etc., make sure
   <limits.h> and (if available) <stdint.h> are included
   so that the code can choose integer types of a good width.  */

#ifndef __PTRDIFF_MAX__
# include <limits.h> /* INFRINGES ON USER NAME SPACE */
# if defined __STDC_VERSION__ && 199901 <= __STDC_VERSION__
#  include <stdint.h> /* INFRINGES ON USER NAME SPACE */
#  define YY_STDINT_H
# endif
#endif

/* Narrow types that promote to a signed type and that can represent a
   signed or unsigned integer of at least N bits.  In tables they can
   save space and decrease cache pressure.  Promoting to a signed type
   helps avoid bugs in integer arithmetic.  */

#ifdef __INT_LEAST8_MAX__
typedef __INT_LEAST8_TYPE__ yytype_int8;
#elif defined YY_STDINT_H
typedef int_least8_t yytype_int8;
#else
typedef signed char yytype_int8;
#endif

#ifdef __INT_LEAST16_MAX__
typedef __INT_LEAST16_TYPE__ yytype_int16;
#elif defined YY_STDINT_H
typedef int_least16_t yytype_int16;
#else
typedef short yytype_int16;
#endif

/* Work around bug in HP-UX 11.23, which defines these macros
   incorrectly for preprocessor constants.  This workaround can likely
   be removed in 2023, as HPE has promised support for HP-UX 11.23
   (aka HP-UX 11i v2) only through the end of 2022; see Table 2 of
   <https://h20195.www2.hpe.com/V2/getpdf.aspx/4AA4-7673ENW.pdf>.  */
#ifdef __hpux
# undef UINT_LEAST8_MAX
# undef UINT_LEAST16_MAX
# define UINT_LEAST8_MAX 255
# define UINT_LEAST16_MAX 65535
#endif

#if defined __UINT_LEAST8_MAX__ && __UINT_LEAST8_MAX__ <= __INT_MAX__
typedef __UINT_LEAST8_TYPE__ yytype_uint8;
#elif (!defined __UINT_LEAST8_MAX__ && defined YY_STDINT_H \
       && UINT_LEAST8_MAX <= INT_MAX)
typedef uint_least8_t yytype_uint8;
#elif !defined __UINT_LEAST8_MAX__ && UCHAR_MAX <= INT_MAX
typedef unsigned char yytype_uint8;
#else
typedef short yytype_uint8;
#endif

#if defined __UINT_LEAST16_MAX__ && __UINT_LEAST16_MAX__ <= __INT_MAX__
typedef __UINT_LEAST16_TYPE__ yytype_uint16;
#elif (!defined __UINT_LEAST16_MAX__ && defined YY_STDINT_H \
       && UINT_LEAST16_MAX <= INT_MAX)
typedef uint_least16_t yytype_uint16;
#elif !defined __UINT_LEAST16_MAX__ && USHRT_MAX <= INT_MAX
typedef unsigned short yytype_uint16;
#else
typedef int yytype_uint16;
#endif

#ifndef YYPTRDIFF_T
# if defined __PTRDIFF_TYPE__ && defined __PTRDIFF_MAX__
#  define YYPTRDIFF_T __PTRDIFF_TYPE__
#  define YYPTRDIFF_MAXIMUM __PTRDIFF_MAX__
# elif defined PTRDIFF_MAX
#  ifndef ptrdiff_t
#   include <stddef.h> /* INFRINGES ON USER NAME SPACE */
#  endif
#  define YYPTRDIFF_T ptrdiff_t
#  define YYPTRDIFF_MAXIMUM PTRDIFF_MAX
# else
#  define YYPTRDIFF_T long
#  define YYPTRDIFF_MAXIMUM LONG_MAX
# endif
#endif

#ifndef YYSIZE_T
# ifdef __SIZE_TYPE__
#  define YYSIZE_T __SIZE_TYPE__
# elif defined size_t
#  define YYSIZE_T size_t
# elif defined __STDC_VERSION__ && 199901 <= __STDC_VERSION__
#  include <stddef.h> /* INFRINGES ON USER NAME SPACE */
#  define YYSIZE_T size_t
# else
#  define YYSIZE_T unsigned
# endif
#endif

#define YYSIZE_MAXIMUM                                  \
  YY_CAST (YYPTRDIFF_T,                                 \
           (YYPTRDIFF_MAXIMUM < YY_CAST (YYSIZE_T, -1)  \
            ? YYPTRDIFF_MAXIMUM                         \
            : YY_CAST (YYSIZE_T, -1)))

#define YYSIZEOF(X) YY_CAST (YYPTRDIFF_T, sizeof (X))


/* Stored state numbers (used for stacks). */
typedef yytype_uint8 yy_state_t;

/* State numbers in computations.  */
typedef int yy_state_fast_t;

#ifndef YY_
# if defined YYENABLE_NLS && YYENABLE_NLS
#  if ENABLE_NLS
#   include <libintl.h> /* INFRINGES ON USER NAME SPACE */
#   define YY_(Msgid) dgettext ("bison-runtime", Msgid)
#  endif
# endif
# ifndef YY_
#  define YY_(Msgid) Msgid
# endif
#endif


#ifndef YY_ATTRIBUTE_PURE
# if defined __GNUC__ && 2 < __GNUC__ + (96 <= __GNUC_MINOR__)
#  define YY_ATTRIBUTE_PURE __attribute__ ((__pure__))
# else
#  define YY_ATTRIBUTE_PURE
# endif
#endif

#ifndef YY_ATTRIBUTE_UNUSED
# if defined __GNUC__ && 2 < __GNUC__ + (7 <= __GNUC_MINOR__)
#  define YY_ATTRIBUTE_UNUSED __attribute__ ((__unused__))
# else
#  define YY_ATTRIBUTE_UNUSED
# endif
#endif

/* Suppress unused-variable warnings by "using" E.  */
#if ! defined lint || defined __GNUC__
# define YY_USE(E) ((void) (E))
#else
# define YY_USE(E) /* empty */
#endif

/* Suppress an incorrect diagnostic about yylval being uninitialized.  */
#if defined __GNUC__ && ! defined __ICC && 406 <= __GNUC__ * 100 + __GNUC_MINOR__
# if __GNUC__ * 100 + __GNUC_MINOR__ < 407
#  define YY_IGNORE_MAYBE_UNINITIALIZED_BEGIN                           \
    _Pragma ("GCC diagnostic push")                                     \
    _Pragma ("GCC diagnostic ignored \"-Wuninitialized\"")
# else
#  define YY_IGNORE_MAYBE_UNINITIALIZED_BEGIN                           \
    _Pragma ("GCC diagnostic push")                                     \
    _Pragma ("GCC diagnostic ignored \"-Wuninitialized\"")              \
    _Pragma ("GCC diagnostic ignored \"-Wmaybe-uninitialized\"")
# endif
# define YY_IGNORE_MAYBE_UNINITIALIZED_END      \
    _Pragma ("GCC diagnostic pop")
#else
# define YY_INITIAL_VALUE(Value) Value
#endif
#ifndef YY_IGNORE_MAYBE_UNINITIALIZED_BEGIN
# define YY_IGNORE_MAYBE_UNINITIALIZED_BEGIN
# define YY_IGNORE_MAYBE_UNINITIALIZED_END
#endif
#ifndef YY_INITIAL_VALUE
# define YY_INITIAL_VALUE(Value) /* Nothing. */
#endif

#if defined __cplusplus && defined __GNUC__ && ! defined __ICC && 6 <= __GNUC__
# define YY_IGNORE_USELESS_CAST_BEGIN                          \
    _Pragma ("GCC diagnostic push")                            \
    _Pragma ("GCC diagnostic ignored \"-Wuseless-cast\"")
# define YY_IGNORE_USELESS_CAST_END            \
    _Pragma ("GCC diagnostic pop")
#endif
#ifndef YY_IGNORE_USELESS_CAST_BEGIN
# define YY_IGNORE_USELESS_CAST_BEGIN
# define YY_IGNORE_USELESS_CAST_END
#endif


#define YY_ASSERT(E) ((void) (0 && (E)))

#if 1

/* The parser invokes alloca or malloc; define the necessary symbols.  */

# ifdef YYSTACK_USE_ALLOCA
#  if YYSTACK_USE_ALLOCA
#   ifdef __GNUC__
#    define YYSTACK_ALLOC __builtin_alloca
#   elif defined __BUILTIN_VA_ARG_INCR
#    include <alloca.h> /* INFRINGES ON USER NAME SPACE */
#   elif defined _AIX
#    define YYSTACK_ALLOC __alloca
#   elif defined _MSC_VER
#    include <malloc.h> /* INFRINGES ON USER NAME SPACE */
#    define alloca _alloca
#   else
#    define YYSTACK_ALLOC alloca
#    if ! defined _ALLOCA_H && ! defined EXIT_SUCCESS
#     include <stdlib.h> /* INFRINGES ON USER NAME SPACE */
      /* Use EXIT_SUCCESS as a witness for stdlib.h.  */
#     ifndef EXIT_SUCCESS
#      define EXIT_SUCCESS 0
#     endif
#    endif
#   endif
#  endif
# endif

# ifdef YYSTACK_ALLOC
   /* Pacify GCC's 'empty if-body' warning.  */
#  define YYSTACK_FREE(Ptr) do { /* empty */; } while (0)
#  ifndef YYSTACK_ALLOC_MAXIMUM
    /* The OS might guarantee only one guard page at the bottom of the stack,
       and a page size can be as small as 4096 bytes.  So we cannot safely
       invoke alloca (N) if N exceeds 4096.  Use a slightly smaller number
       to allow for a few compiler-allocated temporary stack slots.  */
#   define YYSTACK_ALLOC_MAXIMUM 4032 /* reasonable circa 2006 */
#  endif
# else
#  define YYSTACK_ALLOC YYMALLOC
#  define YYSTACK_FREE YYFREE
#  ifndef YYSTACK_ALLOC_MAXIMUM
#   define YYSTACK_ALLOC_MAXIMUM YYSIZE_MAXIMUM
#  endif
#  if (defined __cplusplus && ! defined EXIT_SUCCESS \
       && ! ((defined YYMALLOC || defined malloc) \
             && (defined YYFREE || defined free)))
#   include <stdlib.h> /* INFRINGES ON USER NAME SPACE */
#   ifndef EXIT_SUCCESS
#    define EXIT_SUCCESS 0
#   endif
#  endif
#  ifndef YYMALLOC
#   define YYMALLOC malloc
#   if ! defined malloc && ! defined EXIT_SUCCESS
void *malloc (YYSIZE_T); /* INFRINGES ON USER NAME SPACE */
#   endif
#  endif
#  ifndef YYFREE
#   define YYFREE free
#   if ! defined free && ! defined EXIT_SUCCESS
void free (void *); /* INFRINGES ON USER NAME SPACE */
#   endif
#  endif
# endif
#endif /* 1 */

#if (! defined yyoverflow \
     && (! defined __cplusplus \
         || (defined YYLTYPE_IS_TRIVIAL && YYLTYPE_IS_TRIVIAL \
             && defined YYSTYPE_IS_TRIVIAL && YYSTYPE_IS_TRIVIAL)))

/* A type that is properly aligned for any stack member.  */
union yyalloc
{
  yy_state_t yyss_alloc;
  YYSTYPE yyvs_alloc;
  YYLTYPE yyls_alloc;
};

/* The size of the maximum gap between one aligned stack and the next.  */
# define YYSTACK_GAP_MAXIMUM (YYSIZEOF (union yyalloc) - 1)

/* The size of an array large to enough to hold all stacks, each with
   N elements.  */
# define YYSTACK_BYTES(N) \
     ((N) * (YYSIZEOF (yy_state_t) + YYSIZEOF (YYSTYPE) \
             + YYSIZEOF (YYLTYPE)) \
      + 2 * YYSTACK_GAP_MAXIMUM)

# define YYCOPY_NEEDED 1

/* Relocate STACK from its old location to the new one.  The
   local variables YYSIZE and YYSTACKSIZE give the old and new number of
   elements in the stack, and YYPTR gives the new location of the
   stack.  Advance YYPTR to a properly aligned location for the next
   stack.  */
# define YYSTACK_RELOCATE(Stack_alloc, Stack)                           \
    do                                                                  \
      {                                                                 \
        YYPTRDIFF_T yynewbytes;                                         \
        YYCOPY (&yyptr->Stack_alloc, Stack, yysize);                    \
        Stack = &yyptr->Stack_alloc;                                    \
        yynewbytes = yystacksize * YYSIZEOF (*Stack) + YYSTACK_GAP_MAXIMUM; \
        yyptr += yynewbytes / YYSIZEOF (*yyptr);                        \
      }                                                                 \
    while (0)

#endif

#if defined YYCOPY_NEEDED && YYCOPY_NEEDED
/* Copy COUNT objects from SRC to DST.  The source and destination do
   not overlap.  */
# ifndef YYCOPY
#  if defined __GNUC__ && 1 < __GNUC__
#   define YYCOPY(Dst, Src, Count) \
      __builtin_memcpy (Dst, Src, YY_CAST (YYSIZE_T, (Count)) * sizeof (*(Src)))
#  else
#   define YYCOPY(Dst, Src, Count)              \
      do                                        \
        {                                       \
          YYPTRDIFF_T yyi;                      \
          for (yyi = 0; yyi < (Count); yyi++)   \
            (Dst)[yyi] = (Src)[yyi];            \
        }                                       \
      while (0)
#  endif
# endif
#endif /* !YYCOPY_NEEDED */

/* YYFINAL -- State number of the termination state.  */
#define YYFINAL  61
/* YYLAST -- Last index in YYTABLE.  */
#define YYLAST   270

/* YYNTOKENS -- Number of terminals.  */
#define YYNTOKENS  73
/* YYNNTS -- Number of nonterminals.  */
#define YYNNTS  44
/* YYNRULES -- Number of rules.  */
#define YYNRULES  122
/* YYNSTATES -- Number of states.  */
#define YYNSTATES  235

/* YYMAXUTOK -- Last valid token kind.  */
#define YYMAXUTOK   315


/* YYTRANSLATE(TOKEN-NUM) -- Symbol number corresponding to TOKEN-NUM
   as returned by yylex, with out-of-bounds checking.  */
#define YYTRANSLATE(YYX)                                \
  (0 <= (YYX) && (YYX) <= YYMAXUTOK                     \
   ? YY_CAST (yysymbol_kind_t, yytranslate[YYX])        \
   : YYSYMBOL_YYUNDEF)

/* YYTRANSLATE[TOKEN-NUM] -- Symbol number corresponding to TOKEN-NUM
   as returned by yylex.  */
static const yytype_int8 yytranslate[] =
{
       0,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
      63,    64,    66,    70,    65,    71,    67,    72,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,    61,
      68,    62,    69,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     1,     2,     3,     4,
       5,     6,     7,     8,     9,    10,    11,    12,    13,    14,
      15,    16,    17,    18,    19,    20,    21,    22,    23,    24,
      25,    26,    27,    28,    29,    30,    31,    32,    33,    34,
      35,    36,    37,    38,    39,    40,    41,    42,    43,    44,
      45,    46,    47,    48,    49,    50,    51,    52,    53,    54,
      55,    56,    57,    58,    59,    60
};

#if YYDEBUG
/* YYRLINE[YYN] -- Source line where rule number YYN was defined.  */
static const yytype_int16 yyrline[] =
{
       0,    69,    69,    74,    79,    84,    92,    93,    94,    95,
      96,    97,    98,   102,   106,   110,   114,   121,   128,   135,
     139,   143,   147,   151,   155,   159,   166,   170,   174,   178,
     279,   283,   290,   297,   301,   308,   312,   319,   326,   330,
     334,   341,   345,   352,   356,   360,   364,   371,   379,   380,
     387,   391,   399,   403,   408,   412,   417,   421,   430,   434,
     438,   446,   450,   454,   458,   462,   466,   476,   480,   487,
     491,   498,   502,   506,   510,   515,   519,   523,   527,   531,
     535,   543,   548,   553,   581,   609,   637,   665,   696,   700,
     707,   715,   720,   724,   731,   735,   739,   747,   751,   761,
     766,   772,   784,   797,   809,   825,   829,   837,   841,   848,
     852,   856,   861,   869,   870,   871,   875,   876,   880,   881,
     884,   886,   887
};
#endif

/** Accessing symbol of state STATE.  */
#define YY_ACCESSING_SYMBOL(State) YY_CAST (yysymbol_kind_t, yystos[State])

#if 1
/* The user-facing name of the symbol whose (internal) number is
   YYSYMBOL.  No bounds checking.  */
static const char *yysymbol_name (yysymbol_kind_t yysymbol) YY_ATTRIBUTE_UNUSED;

/* YYTNAME[SYMBOL-NUM] -- String name of the symbol SYMBOL-NUM.
   First, the terminals, then, starting at YYNTOKENS, nonterminals.  */
static const char *const yytname[] =
{
  "\"end of file\"", "error", "\"invalid token\"", "SHOW", "TABLES",
  "CREATE", "TABLE", "DROP", "DESC", "INSERT", "INTO", "VALUES", "DELETE",
  "FROM", "ASC", "EXPLAIN", "ORDER", "BY", "ON", "SEMI", "COUNT", "MIN",
  "MAX", "AVG", "SUM", "GROUP", "HAVING", "AS", "LOAD", "ANTI", "WHERE",
  "UPDATE", "SET", "SELECT", "INT", "CHAR", "FLOAT", "INDEX", "AND",
  "JOIN", "EXIT", "HELP", "TXN_BEGIN", "TXN_COMMIT", "TXN_ABORT",
  "TXN_ROLLBACK", "ORDER_BY", "ENABLE_NESTLOOP", "ENABLE_SORTMERGE",
  "STATIC_CHECKPOINT", "LIMIT", "LEQ", "NEQ", "GEQ", "T_EOF", "IDENTIFIER",
  "VALUE_STRING", "PATH_TOKEN", "VALUE_INT", "VALUE_FLOAT", "VALUE_BOOL",
  "';'", "'='", "'('", "')'", "','", "'*'", "'.'", "'<'", "'>'", "'+'",
  "'-'", "'/'", "$accept", "start", "stmt", "txnStmt", "dbStmt", "setStmt",
  "ddl", "dml", "explainStmt", "loadStmt", "fieldList", "colNameList",
  "field", "type", "valueList", "value", "condition", "optWhereClause",
  "whereClause", "opt_group_by", "opt_having", "havingClause",
  "havingCondition", "aggExpr", "col", "colList", "op", "expr",
  "setClauses", "setClause", "valuexpr", "selector", "select_item",
  "tableExpression", "tableRef", "optAlias", "opt_order_clause",
  "order_clause", "opt_asc_desc", "opt_limit", "set_knob_type", "tbName",
  "filename", "colName", YY_NULLPTR
};

static const char *
yysymbol_name (yysymbol_kind_t yysymbol)
{
  return yytname[yysymbol];
}
#endif

#define YYPACT_NINF (-167)

#define yypact_value_is_default(Yyn) \
  ((Yyn) == YYPACT_NINF)

#define YYTABLE_NINF (-121)

#define yytable_value_is_error(Yyn) \
  0

/* YYPACT[STATE-NUM] -- Index in YYTABLE of the portion describing
   STATE-NUM.  */
static const yytype_int16 yypact[] =
{
      89,     1,    38,     9,   -38,    27,    32,   153,     2,   -38,
      52,   -13,  -167,  -167,  -167,  -167,  -167,  -167,  -167,    61,
       8,  -167,  -167,  -167,  -167,  -167,  -167,  -167,  -167,    60,
     -38,   -38,  -167,   -38,   -38,  -167,  -167,   -38,   -38,    42,
    -167,  -167,  -167,    72,    70,  -167,  -167,    23,    30,    40,
      45,    63,    82,    90,  -167,    -1,  -167,   -10,  -167,    92,
    -167,  -167,  -167,   -38,   105,   109,  -167,   120,    94,   161,
     -38,   100,   132,   -23,   138,   138,   138,   138,   139,  -167,
    -167,   -38,   130,   100,  -167,   100,   100,   100,   133,   138,
    -167,  -167,  -167,    -9,  -167,   135,  -167,   131,   134,   136,
     137,   147,   148,  -167,   -17,  -167,    -1,  -167,  -167,    51,
    -167,   101,   117,  -167,   123,    88,  -167,   164,   179,   100,
    -167,   111,  -167,  -167,  -167,  -167,  -167,  -167,   160,   175,
     -38,   -38,   193,  -167,  -167,   100,  -167,   159,  -167,  -167,
    -167,   100,  -167,  -167,  -167,  -167,  -167,   125,  -167,   138,
    -167,  -167,  -167,  -167,  -167,  -167,  -167,  -167,  -167,  -167,
     111,  -167,    53,  -167,   157,  -167,   -38,   -38,   202,  -167,
     207,   208,  -167,   177,  -167,  -167,    88,  -167,   196,  -167,
      88,    88,    88,    88,  -167,  -167,   111,   215,   218,   138,
     138,   130,   221,   174,  -167,  -167,  -167,  -167,  -167,  -167,
     138,   138,   164,  -167,   178,   201,  -167,   179,   179,   223,
     192,  -167,   164,   164,   138,   130,   111,   118,   138,   186,
    -167,  -167,  -167,  -167,  -167,  -167,    25,   181,  -167,  -167,
    -167,  -167,   138,    25,  -167
};

/* YYDEFACT[STATE-NUM] -- Default reduction number in state STATE-NUM.
   Performed when YYTABLE does not specify something else to do.  Zero
   means the default is an error.  */
static const yytype_int8 yydefact[] =
{
       0,     0,     0,     0,     0,     0,     0,     0,     0,     0,
       0,     0,     4,     3,    13,    14,    15,    16,     5,     0,
       0,     9,     6,    10,     7,     8,    11,    12,    17,     0,
       0,     0,    25,     0,     0,   120,    21,     0,     0,     0,
      31,    30,   121,     0,     0,   118,   119,     0,     0,     0,
       0,     0,     0,   122,    94,     0,    97,     0,    95,     0,
      68,     1,     2,     0,     0,     0,    20,     0,     0,    48,
       0,     0,     0,     0,     0,     0,     0,     0,     0,   108,
      98,     0,     0,     0,    24,     0,     0,     0,     0,     0,
      27,    32,   122,    48,    88,     0,    18,     0,     0,     0,
       0,     0,     0,   107,    48,    99,   105,    96,    67,     0,
      33,     0,     0,    35,     0,     0,    50,    49,     0,     0,
      28,     0,    61,    62,    66,    65,    64,    63,     0,     0,
       0,     0,    53,   106,    19,     0,    38,     0,    40,    37,
      22,     0,    23,    45,    43,    44,    46,     0,    41,     0,
      79,    78,    80,    71,    76,    72,    73,    74,    75,    77,
       0,    89,    81,    82,     0,    90,     0,     0,   102,   100,
       0,    55,    34,     0,    36,    26,     0,    51,    81,    47,
       0,     0,     0,     0,    87,    93,     0,     0,     0,     0,
       0,     0,   110,     0,    42,    85,    83,    84,    86,    91,
       0,     0,   101,    69,    52,    54,    56,     0,     0,     0,
     117,    39,   103,   104,     0,     0,     0,     0,     0,     0,
      29,    70,    57,    58,    60,    59,   115,   109,   116,   114,
     113,   111,     0,   115,   112
};

/* YYPGOTO[NTERM-NUM].  */
static const yytype_int16 yypgoto[] =
{
    -167,  -167,  -167,  -167,  -167,  -167,   246,   250,  -167,  -167,
    -167,   171,   124,  -167,  -167,   -92,   112,   -57,   -94,  -167,
    -167,  -167,    48,  -166,   -11,  -167,  -150,  -136,  -167,   141,
    -167,  -167,   182,  -167,   -90,   163,  -167,  -167,    37,  -167,
    -167,    -3,  -167,   -67
};

/* YYDEFGOTO[NTERM-NUM].  */
static const yytype_uint8 yydefgoto[] =
{
       0,    19,    20,    21,    22,    23,    24,    25,    26,    27,
     109,   112,   110,   139,   147,   178,   116,    90,   117,   171,
     192,   205,   206,    55,   118,   204,   160,   164,    93,    94,
     165,    57,    58,   104,   105,    80,   210,   227,   231,   220,
      47,    59,    43,    60
};

/* YYTABLE[YYPACT[STATE-NUM]] -- What to do in state STATE-NUM.  If
   positive, shift that token.  If negative, reduce the rule whose
   number is the opposite.  If YYTABLE_NINF, syntax error.  */
static const yytype_int16 yytable[] =
{
      56,    36,   128,    81,    95,    28,    44,    48,    49,    50,
      51,    52,   129,    89,   186,    33,   108,    35,   111,   113,
     113,    89,   130,   148,   179,   207,    78,    64,    65,   162,
      66,    67,    53,   229,    68,    69,   120,    37,    29,   230,
     168,   169,    53,    97,    30,    38,    34,   132,   131,   207,
     199,   224,    95,    54,    79,    82,   119,   216,   217,    42,
      84,    61,    98,    99,   100,   101,   102,    91,   111,    62,
     184,    56,   185,    63,   174,    31,   187,   188,   106,    29,
     223,   225,    70,   -92,   194,    72,   184,    32,   195,   196,
     197,   198,     1,    73,     2,   202,     3,     4,     5,    45,
      46,     6,    71,    74,     7,    88,   212,   213,    75,   143,
     163,   144,   145,   146,   -92,   134,   135,     8,   -92,   180,
       9,    10,    11,   181,   182,   183,    76,   106,   106,    12,
      13,    14,    15,    16,    17,   136,   137,   138,    48,    49,
      50,    51,    52,    18,   143,    77,   144,   145,   146,   163,
      48,    49,    50,    51,    52,    92,    39,  -120,     2,    83,
       3,     4,     5,   106,   106,     6,    53,   143,    85,   144,
     145,   146,    86,    53,   143,   163,   144,   145,   146,   203,
     208,   140,   141,    87,     9,    53,    11,   142,   141,   175,
     176,    89,    96,    53,   103,   122,   115,   121,   123,   166,
     124,   125,   149,   221,   208,   163,   163,   226,   150,   151,
     152,   126,   127,   143,   167,   144,   145,   146,   170,   153,
     189,   233,   173,   154,   190,   155,   156,   157,   158,   159,
     150,   151,   152,   200,   191,   193,   201,   209,   211,   215,
     218,   153,   219,   214,   228,   154,   232,   155,   156,   157,
     158,   159,   143,    40,   144,   145,   146,    41,   114,   172,
     161,   177,   180,   222,   107,     0,   181,   182,   183,   133,
     234
};

static const yytype_int16 yycheck[] =
{
      11,     4,    19,    13,    71,     4,     9,    20,    21,    22,
      23,    24,    29,    30,   164,     6,    83,    55,    85,    86,
      87,    30,    39,   115,   160,   191,    27,    30,    31,   121,
      33,    34,    55,     8,    37,    38,    93,    10,    37,    14,
     130,   131,    55,    66,     6,    13,    37,   104,    65,   215,
     186,   217,   119,    66,    55,    65,    65,   207,   208,    57,
      63,     0,    73,    74,    75,    76,    77,    70,   135,    61,
     162,    82,   164,    13,   141,    37,   166,   167,    81,    37,
     216,   217,    10,    30,   176,    62,   178,    49,   180,   181,
     182,   183,     3,    63,     5,   189,     7,     8,     9,    47,
      48,    12,    32,    63,    15,    11,   200,   201,    63,    56,
     121,    58,    59,    60,    61,    64,    65,    28,    65,    66,
      31,    32,    33,    70,    71,    72,    63,   130,   131,    40,
      41,    42,    43,    44,    45,    34,    35,    36,    20,    21,
      22,    23,    24,    54,    56,    63,    58,    59,    60,   160,
      20,    21,    22,    23,    24,    55,     3,    67,     5,    67,
       7,     8,     9,   166,   167,    12,    55,    56,    63,    58,
      59,    60,    63,    55,    56,   186,    58,    59,    60,   190,
     191,    64,    65,    63,    31,    55,    33,    64,    65,    64,
      65,    30,    60,    55,    55,    64,    63,    62,    64,    39,
      64,    64,    38,   214,   215,   216,   217,   218,    51,    52,
      53,    64,    64,    56,    39,    58,    59,    60,    25,    62,
      18,   232,    63,    66,    17,    68,    69,    70,    71,    72,
      51,    52,    53,    18,    26,    58,    18,    16,    64,    38,
      17,    62,    50,    65,    58,    66,    65,    68,    69,    70,
      71,    72,    56,     7,    58,    59,    60,     7,    87,   135,
     119,   149,    66,   215,    82,    -1,    70,    71,    72,   106,
     233
};

/* YYSTOS[STATE-NUM] -- The symbol kind of the accessing symbol of
   state STATE-NUM.  */
static const yytype_int8 yystos[] =
{
       0,     3,     5,     7,     8,     9,    12,    15,    28,    31,
      32,    33,    40,    41,    42,    43,    44,    45,    54,    74,
      75,    76,    77,    78,    79,    80,    81,    82,     4,    37,
       6,    37,    49,     6,    37,    55,   114,    10,    13,     3,
      79,    80,    57,   115,   114,    47,    48,   113,    20,    21,
      22,    23,    24,    55,    66,    96,    97,   104,   105,   114,
     116,     0,    61,    13,   114,   114,   114,   114,   114,   114,
      10,    32,    62,    63,    63,    63,    63,    63,    27,    55,
     108,    13,    65,    67,   114,    63,    63,    63,    11,    30,
      90,   114,    55,   101,   102,   116,    60,    66,    97,    97,
      97,    97,    97,    55,   106,   107,   114,   105,   116,    83,
      85,   116,    84,   116,    84,    63,    89,    91,    97,    65,
      90,    62,    64,    64,    64,    64,    64,    64,    19,    29,
      39,    65,    90,   108,    64,    65,    34,    35,    36,    86,
      64,    65,    64,    56,    58,    59,    60,    87,    88,    38,
      51,    52,    53,    62,    66,    68,    69,    70,    71,    72,
      99,   102,    88,    97,   100,   103,    39,    39,   107,   107,
      25,    92,    85,    63,   116,    64,    65,    89,    88,   100,
      66,    70,    71,    72,    88,    88,    99,   107,   107,    18,
      17,    26,    93,    58,    88,    88,    88,    88,    88,   100,
      18,    18,    91,    97,    98,    94,    95,    96,    97,    16,
     109,    64,    91,    91,    65,    38,    99,    99,    17,    50,
     112,    97,    95,   100,    96,   100,    97,   110,    58,     8,
      14,   111,    65,    97,   111
};

/* YYR1[RULE-NUM] -- Symbol kind of the left-hand side of rule RULE-NUM.  */
static const yytype_int8 yyr1[] =
{
       0,    73,    74,    74,    74,    74,    75,    75,    75,    75,
      75,    75,    75,    76,    76,    76,    76,    77,    78,    79,
      79,    79,    79,    79,    79,    79,    80,    80,    80,    80,
      81,    81,    82,    83,    83,    84,    84,    85,    86,    86,
      86,    87,    87,    88,    88,    88,    88,    89,    90,    90,
      91,    91,    92,    92,    93,    93,    94,    94,    95,    95,
      95,    96,    96,    96,    96,    96,    96,    97,    97,    98,
      98,    99,    99,    99,    99,    99,    99,    99,    99,    99,
      99,   100,   100,   100,   100,   100,   100,   100,   101,   101,
     102,   103,   103,   103,   104,   104,   104,   105,   105,   106,
     106,   106,   106,   106,   106,   107,   107,   108,   108,   109,
     109,   110,   110,   111,   111,   111,   112,   112,   113,   113,
     114,   115,   116
};

/* YYR2[RULE-NUM] -- Number of symbols on the right-hand side of rule RULE-NUM.  */
static const yytype_int8 yyr2[] =
{
       0,     2,     2,     1,     1,     1,     1,     1,     1,     1,
       1,     1,     1,     1,     1,     1,     1,     2,     4,     6,
       3,     2,     6,     6,     4,     2,     7,     4,     5,     9,
       2,     2,     4,     1,     3,     1,     3,     2,     1,     4,
       1,     1,     3,     1,     1,     1,     1,     3,     0,     2,
       1,     3,     3,     0,     2,     0,     1,     3,     3,     3,
       3,     4,     4,     4,     4,     4,     4,     3,     1,     1,
       3,     1,     1,     1,     1,     1,     1,     1,     1,     1,
       1,     1,     1,     3,     3,     3,     3,     2,     1,     3,
       3,     3,     1,     2,     1,     1,     3,     1,     2,     1,
       3,     5,     3,     6,     6,     1,     2,     2,     1,     3,
       0,     2,     4,     1,     1,     0,     2,     0,     1,     1,
       1,     1,     1
};


enum { YYENOMEM = -2 };

#define yyerrok         (yyerrstatus = 0)
#define yyclearin       (yychar = YYEMPTY)

#define YYACCEPT        goto yyacceptlab
#define YYABORT         goto yyabortlab
#define YYERROR         goto yyerrorlab
#define YYNOMEM         goto yyexhaustedlab


#define YYRECOVERING()  (!!yyerrstatus)

#define YYBACKUP(Token, Value)                                    \
  do                                                              \
    if (yychar == YYEMPTY)                                        \
      {                                                           \
        yychar = (Token);                                         \
        yylval = (Value);                                         \
        YYPOPSTACK (yylen);                                       \
        yystate = *yyssp;                                         \
        goto yybackup;                                            \
      }                                                           \
    else                                                          \
      {                                                           \
        yyerror (&yylloc, YY_("syntax error: cannot back up")); \
        YYERROR;                                                  \
      }                                                           \
  while (0)

/* Backward compatibility with an undocumented macro.
   Use YYerror or YYUNDEF. */
#define YYERRCODE YYUNDEF

/* YYLLOC_DEFAULT -- Set CURRENT to span from RHS[1] to RHS[N].
   If N is 0, then set CURRENT to the empty location which ends
   the previous symbol: RHS[0] (always defined).  */

#ifndef YYLLOC_DEFAULT
# define YYLLOC_DEFAULT(Current, Rhs, N)                                \
    do                                                                  \
      if (N)                                                            \
        {                                                               \
          (Current).first_line   = YYRHSLOC (Rhs, 1).first_line;        \
          (Current).first_column = YYRHSLOC (Rhs, 1).first_column;      \
          (Current).last_line    = YYRHSLOC (Rhs, N).last_line;         \
          (Current).last_column  = YYRHSLOC (Rhs, N).last_column;       \
        }                                                               \
      else                                                              \
        {                                                               \
          (Current).first_line   = (Current).last_line   =              \
            YYRHSLOC (Rhs, 0).last_line;                                \
          (Current).first_column = (Current).last_column =              \
            YYRHSLOC (Rhs, 0).last_column;                              \
        }                                                               \
    while (0)
#endif

#define YYRHSLOC(Rhs, K) ((Rhs)[K])


/* Enable debugging if requested.  */
#if YYDEBUG

# ifndef YYFPRINTF
#  include <stdio.h> /* INFRINGES ON USER NAME SPACE */
#  define YYFPRINTF fprintf
# endif

# define YYDPRINTF(Args)                        \
do {                                            \
  if (yydebug)                                  \
    YYFPRINTF Args;                             \
} while (0)


/* YYLOCATION_PRINT -- Print the location on the stream.
   This macro was not mandated originally: define only if we know
   we won't break user code: when these are the locations we know.  */

# ifndef YYLOCATION_PRINT

#  if defined YY_LOCATION_PRINT

   /* Temporary convenience wrapper in case some people defined the
      undocumented and private YY_LOCATION_PRINT macros.  */
#   define YYLOCATION_PRINT(File, Loc)  YY_LOCATION_PRINT(File, *(Loc))

#  elif defined YYLTYPE_IS_TRIVIAL && YYLTYPE_IS_TRIVIAL

/* Print *YYLOCP on YYO.  Private, do not rely on its existence. */

YY_ATTRIBUTE_UNUSED
static int
yy_location_print_ (FILE *yyo, YYLTYPE const * const yylocp)
{
  int res = 0;
  int end_col = 0 != yylocp->last_column ? yylocp->last_column - 1 : 0;
  if (0 <= yylocp->first_line)
    {
      res += YYFPRINTF (yyo, "%d", yylocp->first_line);
      if (0 <= yylocp->first_column)
        res += YYFPRINTF (yyo, ".%d", yylocp->first_column);
    }
  if (0 <= yylocp->last_line)
    {
      if (yylocp->first_line < yylocp->last_line)
        {
          res += YYFPRINTF (yyo, "-%d", yylocp->last_line);
          if (0 <= end_col)
            res += YYFPRINTF (yyo, ".%d", end_col);
        }
      else if (0 <= end_col && yylocp->first_column < end_col)
        res += YYFPRINTF (yyo, "-%d", end_col);
    }
  return res;
}

#   define YYLOCATION_PRINT  yy_location_print_

    /* Temporary convenience wrapper in case some people defined the
       undocumented and private YY_LOCATION_PRINT macros.  */
#   define YY_LOCATION_PRINT(File, Loc)  YYLOCATION_PRINT(File, &(Loc))

#  else

#   define YYLOCATION_PRINT(File, Loc) ((void) 0)
    /* Temporary convenience wrapper in case some people defined the
       undocumented and private YY_LOCATION_PRINT macros.  */
#   define YY_LOCATION_PRINT  YYLOCATION_PRINT

#  endif
# endif /* !defined YYLOCATION_PRINT */


# define YY_SYMBOL_PRINT(Title, Kind, Value, Location)                    \
do {                                                                      \
  if (yydebug)                                                            \
    {                                                                     \
      YYFPRINTF (stderr, "%s ", Title);                                   \
      yy_symbol_print (stderr,                                            \
                  Kind, Value, Location); \
      YYFPRINTF (stderr, "\n");                                           \
    }                                                                     \
} while (0)


/*-----------------------------------.
| Print this symbol's value on YYO.  |
`-----------------------------------*/

static void
yy_symbol_value_print (FILE *yyo,
                       yysymbol_kind_t yykind, YYSTYPE const * const yyvaluep, YYLTYPE const * const yylocationp)
{
  FILE *yyoutput = yyo;
  YY_USE (yyoutput);
  YY_USE (yylocationp);
  if (!yyvaluep)
    return;
  YY_IGNORE_MAYBE_UNINITIALIZED_BEGIN
  YY_USE (yykind);
  YY_IGNORE_MAYBE_UNINITIALIZED_END
}


/*---------------------------.
| Print this symbol on YYO.  |
`---------------------------*/

static void
yy_symbol_print (FILE *yyo,
                 yysymbol_kind_t yykind, YYSTYPE const * const yyvaluep, YYLTYPE const * const yylocationp)
{
  YYFPRINTF (yyo, "%s %s (",
             yykind < YYNTOKENS ? "token" : "nterm", yysymbol_name (yykind));

  YYLOCATION_PRINT (yyo, yylocationp);
  YYFPRINTF (yyo, ": ");
  yy_symbol_value_print (yyo, yykind, yyvaluep, yylocationp);
  YYFPRINTF (yyo, ")");
}

/*------------------------------------------------------------------.
| yy_stack_print -- Print the state stack from its BOTTOM up to its |
| TOP (included).                                                   |
`------------------------------------------------------------------*/

static void
yy_stack_print (yy_state_t *yybottom, yy_state_t *yytop)
{
  YYFPRINTF (stderr, "Stack now");
  for (; yybottom <= yytop; yybottom++)
    {
      int yybot = *yybottom;
      YYFPRINTF (stderr, " %d", yybot);
    }
  YYFPRINTF (stderr, "\n");
}

# define YY_STACK_PRINT(Bottom, Top)                            \
do {                                                            \
  if (yydebug)                                                  \
    yy_stack_print ((Bottom), (Top));                           \
} while (0)


/*------------------------------------------------.
| Report that the YYRULE is going to be reduced.  |
`------------------------------------------------*/

static void
yy_reduce_print (yy_state_t *yyssp, YYSTYPE *yyvsp, YYLTYPE *yylsp,
                 int yyrule)
{
  int yylno = yyrline[yyrule];
  int yynrhs = yyr2[yyrule];
  int yyi;
  YYFPRINTF (stderr, "Reducing stack by rule %d (line %d):\n",
             yyrule - 1, yylno);
  /* The symbols being reduced.  */
  for (yyi = 0; yyi < yynrhs; yyi++)
    {
      YYFPRINTF (stderr, "   $%d = ", yyi + 1);
      yy_symbol_print (stderr,
                       YY_ACCESSING_SYMBOL (+yyssp[yyi + 1 - yynrhs]),
                       &yyvsp[(yyi + 1) - (yynrhs)],
                       &(yylsp[(yyi + 1) - (yynrhs)]));
      YYFPRINTF (stderr, "\n");
    }
}

# define YY_REDUCE_PRINT(Rule)          \
do {                                    \
  if (yydebug)                          \
    yy_reduce_print (yyssp, yyvsp, yylsp, Rule); \
} while (0)

/* Nonzero means print parse trace.  It is left uninitialized so that
   multiple parsers can coexist.  */
int yydebug;
#else /* !YYDEBUG */
# define YYDPRINTF(Args) ((void) 0)
# define YY_SYMBOL_PRINT(Title, Kind, Value, Location)
# define YY_STACK_PRINT(Bottom, Top)
# define YY_REDUCE_PRINT(Rule)
#endif /* !YYDEBUG */


/* YYINITDEPTH -- initial size of the parser's stacks.  */
#ifndef YYINITDEPTH
# define YYINITDEPTH 200
#endif

/* YYMAXDEPTH -- maximum size the stacks can grow to (effective only
   if the built-in stack extension method is used).

   Do not make this value too large; the results are undefined if
   YYSTACK_ALLOC_MAXIMUM < YYSTACK_BYTES (YYMAXDEPTH)
   evaluated with infinite-precision integer arithmetic.  */

#ifndef YYMAXDEPTH
# define YYMAXDEPTH 10000
#endif


/* Context of a parse error.  */
typedef struct
{
  yy_state_t *yyssp;
  yysymbol_kind_t yytoken;
  YYLTYPE *yylloc;
} yypcontext_t;

/* Put in YYARG at most YYARGN of the expected tokens given the
   current YYCTX, and return the number of tokens stored in YYARG.  If
   YYARG is null, return the number of expected tokens (guaranteed to
   be less than YYNTOKENS).  Return YYENOMEM on memory exhaustion.
   Return 0 if there are more than YYARGN expected tokens, yet fill
   YYARG up to YYARGN. */
static int
yypcontext_expected_tokens (const yypcontext_t *yyctx,
                            yysymbol_kind_t yyarg[], int yyargn)
{
  /* Actual size of YYARG. */
  int yycount = 0;
  int yyn = yypact[+*yyctx->yyssp];
  if (!yypact_value_is_default (yyn))
    {
      /* Start YYX at -YYN if negative to avoid negative indexes in
         YYCHECK.  In other words, skip the first -YYN actions for
         this state because they are default actions.  */
      int yyxbegin = yyn < 0 ? -yyn : 0;
      /* Stay within bounds of both yycheck and yytname.  */
      int yychecklim = YYLAST - yyn + 1;
      int yyxend = yychecklim < YYNTOKENS ? yychecklim : YYNTOKENS;
      int yyx;
      for (yyx = yyxbegin; yyx < yyxend; ++yyx)
        if (yycheck[yyx + yyn] == yyx && yyx != YYSYMBOL_YYerror
            && !yytable_value_is_error (yytable[yyx + yyn]))
          {
            if (!yyarg)
              ++yycount;
            else if (yycount == yyargn)
              return 0;
            else
              yyarg[yycount++] = YY_CAST (yysymbol_kind_t, yyx);
          }
    }
  if (yyarg && yycount == 0 && 0 < yyargn)
    yyarg[0] = YYSYMBOL_YYEMPTY;
  return yycount;
}




#ifndef yystrlen
# if defined __GLIBC__ && defined _STRING_H
#  define yystrlen(S) (YY_CAST (YYPTRDIFF_T, strlen (S)))
# else
/* Return the length of YYSTR.  */
static YYPTRDIFF_T
yystrlen (const char *yystr)
{
  YYPTRDIFF_T yylen;
  for (yylen = 0; yystr[yylen]; yylen++)
    continue;
  return yylen;
}
# endif
#endif

#ifndef yystpcpy
# if defined __GLIBC__ && defined _STRING_H && defined _GNU_SOURCE
#  define yystpcpy stpcpy
# else
/* Copy YYSRC to YYDEST, returning the address of the terminating '\0' in
   YYDEST.  */
static char *
yystpcpy (char *yydest, const char *yysrc)
{
  char *yyd = yydest;
  const char *yys = yysrc;

  while ((*yyd++ = *yys++) != '\0')
    continue;

  return yyd - 1;
}
# endif
#endif

#ifndef yytnamerr
/* Copy to YYRES the contents of YYSTR after stripping away unnecessary
   quotes and backslashes, so that it's suitable for yyerror.  The
   heuristic is that double-quoting is unnecessary unless the string
   contains an apostrophe, a comma, or backslash (other than
   backslash-backslash).  YYSTR is taken from yytname.  If YYRES is
   null, do not copy; instead, return the length of what the result
   would have been.  */
static YYPTRDIFF_T
yytnamerr (char *yyres, const char *yystr)
{
  if (*yystr == '"')
    {
      YYPTRDIFF_T yyn = 0;
      char const *yyp = yystr;
      for (;;)
        switch (*++yyp)
          {
          case '\'':
          case ',':
            goto do_not_strip_quotes;

          case '\\':
            if (*++yyp != '\\')
              goto do_not_strip_quotes;
            else
              goto append;

          append:
          default:
            if (yyres)
              yyres[yyn] = *yyp;
            yyn++;
            break;

          case '"':
            if (yyres)
              yyres[yyn] = '\0';
            return yyn;
          }
    do_not_strip_quotes: ;
    }

  if (yyres)
    return yystpcpy (yyres, yystr) - yyres;
  else
    return yystrlen (yystr);
}
#endif


static int
yy_syntax_error_arguments (const yypcontext_t *yyctx,
                           yysymbol_kind_t yyarg[], int yyargn)
{
  /* Actual size of YYARG. */
  int yycount = 0;
  /* There are many possibilities here to consider:
     - If this state is a consistent state with a default action, then
       the only way this function was invoked is if the default action
       is an error action.  In that case, don't check for expected
       tokens because there are none.
     - The only way there can be no lookahead present (in yychar) is if
       this state is a consistent state with a default action.  Thus,
       detecting the absence of a lookahead is sufficient to determine
       that there is no unexpected or expected token to report.  In that
       case, just report a simple "syntax error".
     - Don't assume there isn't a lookahead just because this state is a
       consistent state with a default action.  There might have been a
       previous inconsistent state, consistent state with a non-default
       action, or user semantic action that manipulated yychar.
     - Of course, the expected token list depends on states to have
       correct lookahead information, and it depends on the parser not
       to perform extra reductions after fetching a lookahead from the
       scanner and before detecting a syntax error.  Thus, state merging
       (from LALR or IELR) and default reductions corrupt the expected
       token list.  However, the list is correct for canonical LR with
       one exception: it will still contain any token that will not be
       accepted due to an error action in a later state.
  */
  if (yyctx->yytoken != YYSYMBOL_YYEMPTY)
    {
      int yyn;
      if (yyarg)
        yyarg[yycount] = yyctx->yytoken;
      ++yycount;
      yyn = yypcontext_expected_tokens (yyctx,
                                        yyarg ? yyarg + 1 : yyarg, yyargn - 1);
      if (yyn == YYENOMEM)
        return YYENOMEM;
      else
        yycount += yyn;
    }
  return yycount;
}

/* Copy into *YYMSG, which is of size *YYMSG_ALLOC, an error message
   about the unexpected token YYTOKEN for the state stack whose top is
   YYSSP.

   Return 0 if *YYMSG was successfully written.  Return -1 if *YYMSG is
   not large enough to hold the message.  In that case, also set
   *YYMSG_ALLOC to the required number of bytes.  Return YYENOMEM if the
   required number of bytes is too large to store.  */
static int
yysyntax_error (YYPTRDIFF_T *yymsg_alloc, char **yymsg,
                const yypcontext_t *yyctx)
{
  enum { YYARGS_MAX = 5 };
  /* Internationalized format string. */
  const char *yyformat = YY_NULLPTR;
  /* Arguments of yyformat: reported tokens (one for the "unexpected",
     one per "expected"). */
  yysymbol_kind_t yyarg[YYARGS_MAX];
  /* Cumulated lengths of YYARG.  */
  YYPTRDIFF_T yysize = 0;

  /* Actual size of YYARG. */
  int yycount = yy_syntax_error_arguments (yyctx, yyarg, YYARGS_MAX);
  if (yycount == YYENOMEM)
    return YYENOMEM;

  switch (yycount)
    {
#define YYCASE_(N, S)                       \
      case N:                               \
        yyformat = S;                       \
        break
    default: /* Avoid compiler warnings. */
      YYCASE_(0, YY_("syntax error"));
      YYCASE_(1, YY_("syntax error, unexpected %s"));
      YYCASE_(2, YY_("syntax error, unexpected %s, expecting %s"));
      YYCASE_(3, YY_("syntax error, unexpected %s, expecting %s or %s"));
      YYCASE_(4, YY_("syntax error, unexpected %s, expecting %s or %s or %s"));
      YYCASE_(5, YY_("syntax error, unexpected %s, expecting %s or %s or %s or %s"));
#undef YYCASE_
    }

  /* Compute error message size.  Don't count the "%s"s, but reserve
     room for the terminator.  */
  yysize = yystrlen (yyformat) - 2 * yycount + 1;
  {
    int yyi;
    for (yyi = 0; yyi < yycount; ++yyi)
      {
        YYPTRDIFF_T yysize1
          = yysize + yytnamerr (YY_NULLPTR, yytname[yyarg[yyi]]);
        if (yysize <= yysize1 && yysize1 <= YYSTACK_ALLOC_MAXIMUM)
          yysize = yysize1;
        else
          return YYENOMEM;
      }
  }

  if (*yymsg_alloc < yysize)
    {
      *yymsg_alloc = 2 * yysize;
      if (! (yysize <= *yymsg_alloc
             && *yymsg_alloc <= YYSTACK_ALLOC_MAXIMUM))
        *yymsg_alloc = YYSTACK_ALLOC_MAXIMUM;
      return -1;
    }

  /* Avoid sprintf, as that infringes on the user's name space.
     Don't have undefined behavior even if the translation
     produced a string with the wrong number of "%s"s.  */
  {
    char *yyp = *yymsg;
    int yyi = 0;
    while ((*yyp = *yyformat) != '\0')
      if (*yyp == '%' && yyformat[1] == 's' && yyi < yycount)
        {
          yyp += yytnamerr (yyp, yytname[yyarg[yyi++]]);
          yyformat += 2;
        }
      else
        {
          ++yyp;
          ++yyformat;
        }
  }
  return 0;
}


/*-----------------------------------------------.
| Release the memory associated to this symbol.  |
`-----------------------------------------------*/

static void
yydestruct (const char *yymsg,
            yysymbol_kind_t yykind, YYSTYPE *yyvaluep, YYLTYPE *yylocationp)
{
  YY_USE (yyvaluep);
  YY_USE (yylocationp);
  if (!yymsg)
    yymsg = "Deleting";
  YY_SYMBOL_PRINT (yymsg, yykind, yyvaluep, yylocationp);

  YY_IGNORE_MAYBE_UNINITIALIZED_BEGIN
  YY_USE (yykind);
  YY_IGNORE_MAYBE_UNINITIALIZED_END
}






/*----------.
| yyparse.  |
`----------*/

int
yyparse (void)
{
/* Lookahead token kind.  */
int yychar;


/* The semantic value of the lookahead symbol.  */
/* Default value used for initialization, for pacifying older GCCs
   or non-GCC compilers.  */
YY_INITIAL_VALUE (static YYSTYPE yyval_default;)
YYSTYPE yylval YY_INITIAL_VALUE (= yyval_default);

/* Location data for the lookahead symbol.  */
static YYLTYPE yyloc_default
# if defined YYLTYPE_IS_TRIVIAL && YYLTYPE_IS_TRIVIAL
  = { 1, 1, 1, 1 }
# endif
;
YYLTYPE yylloc = yyloc_default;

    /* Number of syntax errors so far.  */
    int yynerrs = 0;

    yy_state_fast_t yystate = 0;
    /* Number of tokens to shift before error messages enabled.  */
    int yyerrstatus = 0;

    /* Refer to the stacks through separate pointers, to allow yyoverflow
       to reallocate them elsewhere.  */

    /* Their size.  */
    YYPTRDIFF_T yystacksize = YYINITDEPTH;

    /* The state stack: array, bottom, top.  */
    yy_state_t yyssa[YYINITDEPTH];
    yy_state_t *yyss = yyssa;
    yy_state_t *yyssp = yyss;

    /* The semantic value stack: array, bottom, top.  */
    YYSTYPE yyvsa[YYINITDEPTH];
    YYSTYPE *yyvs = yyvsa;
    YYSTYPE *yyvsp = yyvs;

    /* The location stack: array, bottom, top.  */
    YYLTYPE yylsa[YYINITDEPTH];
    YYLTYPE *yyls = yylsa;
    YYLTYPE *yylsp = yyls;

  int yyn;
  /* The return value of yyparse.  */
  int yyresult;
  /* Lookahead symbol kind.  */
  yysymbol_kind_t yytoken = YYSYMBOL_YYEMPTY;
  /* The variables used to return semantic value and location from the
     action routines.  */
  YYSTYPE yyval;
  YYLTYPE yyloc;

  /* The locations where the error started and ended.  */
  YYLTYPE yyerror_range[3];

  /* Buffer for error messages, and its allocated size.  */
  char yymsgbuf[128];
  char *yymsg = yymsgbuf;
  YYPTRDIFF_T yymsg_alloc = sizeof yymsgbuf;

#define YYPOPSTACK(N)   (yyvsp -= (N), yyssp -= (N), yylsp -= (N))

  /* The number of symbols on the RHS of the reduced rule.
     Keep to zero when no symbol should be popped.  */
  int yylen = 0;

  YYDPRINTF ((stderr, "Starting parse\n"));

  yychar = YYEMPTY; /* Cause a token to be read.  */

  yylsp[0] = yylloc;
  goto yysetstate;


/*------------------------------------------------------------.
| yynewstate -- push a new state, which is found in yystate.  |
`------------------------------------------------------------*/
yynewstate:
  /* In all cases, when you get here, the value and location stacks
     have just been pushed.  So pushing a state here evens the stacks.  */
  yyssp++;


/*--------------------------------------------------------------------.
| yysetstate -- set current state (the top of the stack) to yystate.  |
`--------------------------------------------------------------------*/
yysetstate:
  YYDPRINTF ((stderr, "Entering state %d\n", yystate));
  YY_ASSERT (0 <= yystate && yystate < YYNSTATES);
  YY_IGNORE_USELESS_CAST_BEGIN
  *yyssp = YY_CAST (yy_state_t, yystate);
  YY_IGNORE_USELESS_CAST_END
  YY_STACK_PRINT (yyss, yyssp);

  if (yyss + yystacksize - 1 <= yyssp)
#if !defined yyoverflow && !defined YYSTACK_RELOCATE
    YYNOMEM;
#else
    {
      /* Get the current used size of the three stacks, in elements.  */
      YYPTRDIFF_T yysize = yyssp - yyss + 1;

# if defined yyoverflow
      {
        /* Give user a chance to reallocate the stack.  Use copies of
           these so that the &'s don't force the real ones into
           memory.  */
        yy_state_t *yyss1 = yyss;
        YYSTYPE *yyvs1 = yyvs;
        YYLTYPE *yyls1 = yyls;

        /* Each stack pointer address is followed by the size of the
           data in use in that stack, in bytes.  This used to be a
           conditional around just the two extra args, but that might
           be undefined if yyoverflow is a macro.  */
        yyoverflow (YY_("memory exhausted"),
                    &yyss1, yysize * YYSIZEOF (*yyssp),
                    &yyvs1, yysize * YYSIZEOF (*yyvsp),
                    &yyls1, yysize * YYSIZEOF (*yylsp),
                    &yystacksize);
        yyss = yyss1;
        yyvs = yyvs1;
        yyls = yyls1;
      }
# else /* defined YYSTACK_RELOCATE */
      /* Extend the stack our own way.  */
      if (YYMAXDEPTH <= yystacksize)
        YYNOMEM;
      yystacksize *= 2;
      if (YYMAXDEPTH < yystacksize)
        yystacksize = YYMAXDEPTH;

      {
        yy_state_t *yyss1 = yyss;
        union yyalloc *yyptr =
          YY_CAST (union yyalloc *,
                   YYSTACK_ALLOC (YY_CAST (YYSIZE_T, YYSTACK_BYTES (yystacksize))));
        if (! yyptr)
          YYNOMEM;
        YYSTACK_RELOCATE (yyss_alloc, yyss);
        YYSTACK_RELOCATE (yyvs_alloc, yyvs);
        YYSTACK_RELOCATE (yyls_alloc, yyls);
#  undef YYSTACK_RELOCATE
        if (yyss1 != yyssa)
          YYSTACK_FREE (yyss1);
      }
# endif

      yyssp = yyss + yysize - 1;
      yyvsp = yyvs + yysize - 1;
      yylsp = yyls + yysize - 1;

      YY_IGNORE_USELESS_CAST_BEGIN
      YYDPRINTF ((stderr, "Stack size increased to %ld\n",
                  YY_CAST (long, yystacksize)));
      YY_IGNORE_USELESS_CAST_END

      if (yyss + yystacksize - 1 <= yyssp)
        YYABORT;
    }
#endif /* !defined yyoverflow && !defined YYSTACK_RELOCATE */


  if (yystate == YYFINAL)
    YYACCEPT;

  goto yybackup;


/*-----------.
| yybackup.  |
`-----------*/
yybackup:
  /* Do appropriate processing given the current state.  Read a
     lookahead token if we need one and don't already have one.  */

  /* First try to decide what to do without reference to lookahead token.  */
  yyn = yypact[yystate];
  if (yypact_value_is_default (yyn))
    goto yydefault;

  /* Not known => get a lookahead token if don't already have one.  */

  /* YYCHAR is either empty, or end-of-input, or a valid lookahead.  */
  if (yychar == YYEMPTY)
    {
      YYDPRINTF ((stderr, "Reading a token\n"));
      yychar = yylex (&yylval, &yylloc);
    }

  if (yychar <= YYEOF)
    {
      yychar = YYEOF;
      yytoken = YYSYMBOL_YYEOF;
      YYDPRINTF ((stderr, "Now at end of input.\n"));
    }
  else if (yychar == YYerror)
    {
      /* The scanner already issued an error message, process directly
         to error recovery.  But do not keep the error token as
         lookahead, it is too special and may lead us to an endless
         loop in error recovery. */
      yychar = YYUNDEF;
      yytoken = YYSYMBOL_YYerror;
      yyerror_range[1] = yylloc;
      goto yyerrlab1;
    }
  else
    {
      yytoken = YYTRANSLATE (yychar);
      YY_SYMBOL_PRINT ("Next token is", yytoken, &yylval, &yylloc);
    }

  /* If the proper action on seeing token YYTOKEN is to reduce or to
     detect an error, take that action.  */
  yyn += yytoken;
  if (yyn < 0 || YYLAST < yyn || yycheck[yyn] != yytoken)
    goto yydefault;
  yyn = yytable[yyn];
  if (yyn <= 0)
    {
      if (yytable_value_is_error (yyn))
        goto yyerrlab;
      yyn = -yyn;
      goto yyreduce;
    }

  /* Count tokens shifted since error; after three, turn off error
     status.  */
  if (yyerrstatus)
    yyerrstatus--;

  /* Shift the lookahead token.  */
  YY_SYMBOL_PRINT ("Shifting", yytoken, &yylval, &yylloc);
  yystate = yyn;
  YY_IGNORE_MAYBE_UNINITIALIZED_BEGIN
  *++yyvsp = yylval;
  YY_IGNORE_MAYBE_UNINITIALIZED_END
  *++yylsp = yylloc;

  /* Discard the shifted token.  */
  yychar = YYEMPTY;
  goto yynewstate;


/*-----------------------------------------------------------.
| yydefault -- do the default action for the current state.  |
`-----------------------------------------------------------*/
yydefault:
  yyn = yydefact[yystate];
  if (yyn == 0)
    goto yyerrlab;
  goto yyreduce;


/*-----------------------------.
| yyreduce -- do a reduction.  |
`-----------------------------*/
yyreduce:
  /* yyn is the number of a rule to reduce with.  */
  yylen = yyr2[yyn];

  /* If YYLEN is nonzero, implement the default value of the action:
     '$$ = $1'.

     Otherwise, the following line sets YYVAL to garbage.
     This behavior is undocumented and Bison
     users should not rely upon it.  Assigning to YYVAL
     unconditionally makes the parser a bit smaller, and it avoids a
     GCC warning that YYVAL may be used uninitialized.  */
  yyval = yyvsp[1-yylen];

  /* Default location. */
  YYLLOC_DEFAULT (yyloc, (yylsp - yylen), yylen);
  yyerror_range[1] = yyloc;
  YY_REDUCE_PRINT (yyn);
  switch (yyn)
    {
  case 2: /* start: stmt ';'  */
#line 70 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        parse_tree = (yyvsp[-1].sv_node);
        YYACCEPT;
    }
#line 1763 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 3: /* start: HELP  */
#line 75 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        parse_tree = std::make_shared<Help>();
        YYACCEPT;
    }
#line 1772 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 4: /* start: EXIT  */
#line 80 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        parse_tree = nullptr;
        YYACCEPT;
    }
#line 1781 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 5: /* start: T_EOF  */
#line 85 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        parse_tree = nullptr;
        YYACCEPT;
    }
#line 1790 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 13: /* txnStmt: TXN_BEGIN  */
#line 103 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_node) = std::make_shared<TxnBegin>();
    }
#line 1798 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 14: /* txnStmt: TXN_COMMIT  */
#line 107 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_node) = std::make_shared<TxnCommit>();
    }
#line 1806 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 15: /* txnStmt: TXN_ABORT  */
#line 111 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_node) = std::make_shared<TxnAbort>();
    }
#line 1814 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 16: /* txnStmt: TXN_ROLLBACK  */
#line 115 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_node) = std::make_shared<TxnRollback>();
    }
#line 1822 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 17: /* dbStmt: SHOW TABLES  */
#line 122 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_node) = std::make_shared<ShowTables>();
    }
#line 1830 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 18: /* setStmt: SET set_knob_type '=' VALUE_BOOL  */
#line 129 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_node) = std::make_shared<SetStmt>((yyvsp[-2].sv_setKnobType), (yyvsp[0].sv_bool));
    }
#line 1838 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 19: /* ddl: CREATE TABLE tbName '(' fieldList ')'  */
#line 136 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_node) = std::make_shared<CreateTable>((yyvsp[-3].sv_str), (yyvsp[-1].sv_fields));
    }
#line 1846 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 20: /* ddl: DROP TABLE tbName  */
#line 140 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_node) = std::make_shared<DropTable>((yyvsp[0].sv_str));
    }
#line 1854 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 21: /* ddl: DESC tbName  */
#line 144 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_node) = std::make_shared<DescTable>((yyvsp[0].sv_str));
    }
#line 1862 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 22: /* ddl: CREATE INDEX tbName '(' colNameList ')'  */
#line 148 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_node) = std::make_shared<CreateIndex>((yyvsp[-3].sv_str), (yyvsp[-1].sv_strs));
    }
#line 1870 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 23: /* ddl: DROP INDEX tbName '(' colNameList ')'  */
#line 152 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_node) = std::make_shared<DropIndex>((yyvsp[-3].sv_str), (yyvsp[-1].sv_strs));
    }
#line 1878 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 24: /* ddl: SHOW INDEX FROM tbName  */
#line 156 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_node) = std::make_shared<ShowIndex>((yyvsp[0].sv_str));
    }
#line 1886 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 25: /* ddl: CREATE STATIC_CHECKPOINT  */
#line 160 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_node) = std::make_shared<CreateStaticCheckpoint>();
    }
#line 1894 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 26: /* dml: INSERT INTO tbName VALUES '(' valueList ')'  */
#line 167 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_node) = std::make_shared<InsertStmt>((yyvsp[-4].sv_str), (yyvsp[-1].sv_vals));
    }
#line 1902 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 27: /* dml: DELETE FROM tbName optWhereClause  */
#line 171 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_node) = std::make_shared<DeleteStmt>((yyvsp[-1].sv_str), (yyvsp[0].sv_conds));
    }
#line 1910 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 28: /* dml: UPDATE tbName SET setClauses optWhereClause  */
#line 175 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_node) = std::make_shared<UpdateStmt>((yyvsp[-3].sv_str), (yyvsp[-1].sv_set_clauses), (yyvsp[0].sv_conds));
    }
#line 1918 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 29: /* dml: SELECT selector FROM tableExpression optWhereClause opt_group_by opt_having opt_order_clause opt_limit  */
#line 179 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {


        // 检查是否包含聚合函数
        std::vector<std::shared_ptr<AggExpr>> aggexprs;
        std::vector<std::shared_ptr<Col>> cols;
        std::vector<std::shared_ptr<Col>> select_cols; // 只包含普通列，不包括聚合函数别名
        bool has_aggregate = false;
        for (const auto& item : (yyvsp[-7].sv_select_items)) {
            if (item->agg_expr) {
                // 如果聚合函数没有别名，生成默认列名
                std::string col_name = item->agg_expr->alias;
                if (col_name.empty()) {
                    // 生成默认列名，如 MAX(id), COUNT(*) 等
                    std::string func_name;
                    switch (item->agg_expr->type) {
                        case AGG_COUNT: func_name = "COUNT"; break;
                        case AGG_SUM: func_name = "SUM"; break;
                        case AGG_AVG: func_name = "AVG"; break;
                        case AGG_MAX: func_name = "MAX"; break;
                        case AGG_MIN: func_name = "MIN"; break;
                        default: func_name = "UNKNOWN"; break;
                    }

                    if (item->agg_expr->col) {
                        col_name = func_name + "(" + item->agg_expr->col->col_name + ")";
                    } else {
                        col_name = func_name + "(*)";
                    }
                    item->agg_expr->alias = col_name;  // 更新别名
                }

                std::shared_ptr<Col> col = std::make_shared<Col>("", col_name);
                aggexprs.push_back(item->agg_expr);
                cols.push_back(col);
                has_aggregate = true;
            }
            else
            {
                cols.push_back(item->column);
                select_cols.push_back(item->column); // 只添加普通列到select_cols
            }
        }

        if (has_aggregate || !(yyvsp[-3].sv_group_by).empty()) {
            // 构建聚合查询（有聚合函数或有GROUP BY子句）
            std::vector<std::string> Agg_tables;
            for(auto& aggtab : (yyvsp[-5].sv_table_expr).tables)
            {
                Agg_tables.push_back(aggtab.first);
            }
            (yyval.sv_node) = std::make_shared<GroupByStmt>(
                select_cols, // 使用select_cols而不是cols
                aggexprs,
                Agg_tables,
                std::vector<std::shared_ptr<JoinExpr>>{},
                (yyvsp[-3].sv_group_by),
                (yyvsp[-2].sv_having),
                (yyvsp[-4].sv_conds),
                (yyvsp[-1].sv_orderby),
                (yyvsp[0].sv_int)  // 添加LIMIT参数
            );
        }
        else
        {
            // 检查是否包含SEMI JOIN
            bool has_semi_join = false;
            for (const auto& join : (yyvsp[-5].sv_table_expr).joins) {
                if (join->type == SEMI_JOIN) {
                    has_semi_join = true;
                    break;
                }
            }
            // 检查是否含有anti join
            bool has_anti_join = false;
            for (const auto& join : (yyvsp[-5].sv_table_expr).joins) {
                if (join->type == ANTI_JOIN) {
                    has_anti_join = true;
                    break;
                }
            }


            if (has_semi_join) {
                (yyval.sv_node) = std::make_shared<SemijoinStmt>(cols, (yyvsp[-5].sv_table_expr).tables, (yyvsp[-5].sv_table_expr).joins, (yyvsp[-4].sv_conds));
            }
            else if(has_anti_join){
                (yyval.sv_node) = std::make_shared<AntijoinStmt>(cols, (yyvsp[-5].sv_table_expr).tables, (yyvsp[-5].sv_table_expr).joins, (yyvsp[-4].sv_conds));
            }
            else {
                // 普通查询
                (yyval.sv_node) = std::make_shared<SelectStmt>(cols, (yyvsp[-5].sv_table_expr).tables, (yyvsp[-5].sv_table_expr).joins, (yyvsp[-4].sv_conds), (yyvsp[-1].sv_orderby), (yyvsp[0].sv_int));  // 添加LIMIT参数
            }
        }
        

    }
#line 2020 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 30: /* explainStmt: EXPLAIN dml  */
#line 280 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_node) = std::make_shared<ExplainStmt>((yyvsp[0].sv_node));
    }
#line 2028 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 31: /* explainStmt: EXPLAIN ddl  */
#line 284 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_node) = std::make_shared<ExplainStmt>((yyvsp[0].sv_node));
    }
#line 2036 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 32: /* loadStmt: LOAD filename INTO tbName  */
#line 291 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_node) = std::make_shared<LoadStmt>((yyvsp[0].sv_str), (yyvsp[-2].sv_str));
    }
#line 2044 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 33: /* fieldList: field  */
#line 298 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_fields) = std::vector<std::shared_ptr<Field>>{(yyvsp[0].sv_field)};
    }
#line 2052 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 34: /* fieldList: fieldList ',' field  */
#line 302 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_fields).push_back((yyvsp[0].sv_field));
    }
#line 2060 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 35: /* colNameList: colName  */
#line 309 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_strs) = std::vector<std::string>{(yyvsp[0].sv_str)};
    }
#line 2068 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 36: /* colNameList: colNameList ',' colName  */
#line 313 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_strs).push_back((yyvsp[0].sv_str));
    }
#line 2076 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 37: /* field: colName type  */
#line 320 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_field) = std::make_shared<ColDef>((yyvsp[-1].sv_str), (yyvsp[0].sv_type_len));
    }
#line 2084 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 38: /* type: INT  */
#line 327 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_type_len) = std::make_shared<TypeLen>(SV_TYPE_INT, sizeof(int));
    }
#line 2092 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 39: /* type: CHAR '(' VALUE_INT ')'  */
#line 331 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_type_len) = std::make_shared<TypeLen>(SV_TYPE_STRING, (yyvsp[-1].sv_int));
    }
#line 2100 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 40: /* type: FLOAT  */
#line 335 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_type_len) = std::make_shared<TypeLen>(SV_TYPE_FLOAT, sizeof(float));
    }
#line 2108 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 41: /* valueList: value  */
#line 342 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_vals) = std::vector<std::shared_ptr<Value>>{(yyvsp[0].sv_val)};
    }
#line 2116 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 42: /* valueList: valueList ',' value  */
#line 346 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_vals).push_back((yyvsp[0].sv_val));
    }
#line 2124 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 43: /* value: VALUE_INT  */
#line 353 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_val) = std::make_shared<IntLit>((yyvsp[0].sv_int));
    }
#line 2132 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 44: /* value: VALUE_FLOAT  */
#line 357 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_val) = std::make_shared<FloatLit>((yyvsp[0].sv_float));
    }
#line 2140 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 45: /* value: VALUE_STRING  */
#line 361 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_val) = std::make_shared<StringLit>((yyvsp[0].sv_str));
    }
#line 2148 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 46: /* value: VALUE_BOOL  */
#line 365 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_val) = std::make_shared<BoolLit>((yyvsp[0].sv_bool));
    }
#line 2156 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 47: /* condition: col op expr  */
#line 372 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_cond) = std::make_shared<BinaryExpr>((yyvsp[-2].sv_col), (yyvsp[-1].sv_comp_op), (yyvsp[0].sv_expr));
    }
#line 2164 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 48: /* optWhereClause: %empty  */
#line 379 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
                      { /* ignore*/ }
#line 2170 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 49: /* optWhereClause: WHERE whereClause  */
#line 381 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_conds) = (yyvsp[0].sv_conds);
    }
#line 2178 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 50: /* whereClause: condition  */
#line 388 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_conds) = std::vector<std::shared_ptr<BinaryExpr>>{(yyvsp[0].sv_cond)};
    }
#line 2186 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 51: /* whereClause: whereClause AND condition  */
#line 392 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_conds).push_back((yyvsp[0].sv_cond));
    }
#line 2194 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 52: /* opt_group_by: GROUP BY colList  */
#line 400 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_group_by) = (yyvsp[0].sv_cols);
    }
#line 2202 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 53: /* opt_group_by: %empty  */
#line 403 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
                      { (yyval.sv_group_by) = std::vector<std::shared_ptr<Col>>{}; }
#line 2208 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 54: /* opt_having: HAVING havingClause  */
#line 409 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_having) = (yyvsp[0].sv_having);
    }
#line 2216 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 55: /* opt_having: %empty  */
#line 412 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
                      { (yyval.sv_having) = std::vector<std::shared_ptr<BinaryAggExpr>>{}; }
#line 2222 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 56: /* havingClause: havingCondition  */
#line 418 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_having) = std::vector<std::shared_ptr<BinaryAggExpr>>{(yyvsp[0].sv_having_cond)};
    }
#line 2230 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 57: /* havingClause: havingClause AND havingCondition  */
#line 422 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_having).push_back((yyvsp[0].sv_having_cond));
    }
#line 2238 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 58: /* havingCondition: aggExpr op expr  */
#line 431 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_having_cond) = std::make_shared<BinaryAggExpr>((yyvsp[-2].sv_agg), (yyvsp[-1].sv_comp_op), (yyvsp[0].sv_expr));
    }
#line 2246 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 59: /* havingCondition: col op expr  */
#line 435 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_having_cond) = std::make_shared<BinaryAggExpr>((yyvsp[-2].sv_col), (yyvsp[-1].sv_comp_op), (yyvsp[0].sv_expr));
    }
#line 2254 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 60: /* havingCondition: col op aggExpr  */
#line 439 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_having_cond) = std::make_shared<BinaryAggExpr>((yyvsp[-2].sv_col), (yyvsp[-1].sv_comp_op), (yyvsp[0].sv_agg));
    }
#line 2262 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 61: /* aggExpr: COUNT '(' '*' ')'  */
#line 447 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_agg) = std::make_shared<AggExpr>(AGG_COUNT, nullptr, "");
    }
#line 2270 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 62: /* aggExpr: COUNT '(' col ')'  */
#line 451 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_agg) = std::make_shared<AggExpr>(AGG_COUNT, (yyvsp[-1].sv_col), "");
    }
#line 2278 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 63: /* aggExpr: SUM '(' col ')'  */
#line 455 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_agg) = std::make_shared<AggExpr>(AGG_SUM, (yyvsp[-1].sv_col), "");
    }
#line 2286 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 64: /* aggExpr: AVG '(' col ')'  */
#line 459 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_agg) = std::make_shared<AggExpr>(AGG_AVG, (yyvsp[-1].sv_col), "");
    }
#line 2294 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 65: /* aggExpr: MAX '(' col ')'  */
#line 463 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_agg) = std::make_shared<AggExpr>(AGG_MAX, (yyvsp[-1].sv_col), "");
    }
#line 2302 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 66: /* aggExpr: MIN '(' col ')'  */
#line 467 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_agg) = std::make_shared<AggExpr>(AGG_MIN, (yyvsp[-1].sv_col), "");
    }
#line 2310 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 67: /* col: tbName '.' colName  */
#line 477 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_col) = std::make_shared<Col>((yyvsp[-2].sv_str), (yyvsp[0].sv_str));
    }
#line 2318 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 68: /* col: colName  */
#line 481 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_col) = std::make_shared<Col>("", (yyvsp[0].sv_str));
    }
#line 2326 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 69: /* colList: col  */
#line 488 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_cols) = std::vector<std::shared_ptr<Col>>{(yyvsp[0].sv_col)};
    }
#line 2334 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 70: /* colList: colList ',' col  */
#line 492 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_cols).push_back((yyvsp[0].sv_col));
    }
#line 2342 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 71: /* op: '='  */
#line 499 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_comp_op) = SV_OP_EQ;
    }
#line 2350 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 72: /* op: '<'  */
#line 503 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_comp_op) = SV_OP_LT;
    }
#line 2358 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 73: /* op: '>'  */
#line 507 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_comp_op) = SV_OP_GT;
    }
#line 2366 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 74: /* op: '+'  */
#line 511 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        // printf("op: +\n");
        (yyval.sv_comp_op) = SV_OP_ADD;
    }
#line 2375 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 75: /* op: '-'  */
#line 516 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_comp_op) = SV_OP_SUB;
    }
#line 2383 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 76: /* op: '*'  */
#line 520 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_comp_op) = SV_OP_MUL;
    }
#line 2391 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 77: /* op: '/'  */
#line 524 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_comp_op) = SV_OP_DIV;
    }
#line 2399 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 78: /* op: NEQ  */
#line 528 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_comp_op) = SV_OP_NE;
    }
#line 2407 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 79: /* op: LEQ  */
#line 532 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_comp_op) = SV_OP_LE;
    }
#line 2415 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 80: /* op: GEQ  */
#line 536 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_comp_op) = SV_OP_GE;
    }
#line 2423 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 81: /* expr: value  */
#line 544 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        // printf("expr: value\n");
        (yyval.sv_expr) = std::static_pointer_cast<Expr>((yyvsp[0].sv_val));
    }
#line 2432 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 82: /* expr: col  */
#line 549 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        // printf("expr: col\n");
        (yyval.sv_expr) = std::static_pointer_cast<Expr>((yyvsp[0].sv_col));
    }
#line 2441 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 83: /* expr: value '+' value  */
#line 554 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        // 尝试将左值转换为各种类型
        auto left_int = std::dynamic_pointer_cast<IntLit>((yyvsp[-2].sv_val));
        auto left_float = std::dynamic_pointer_cast<FloatLit>((yyvsp[-2].sv_val));
        auto left_string = std::dynamic_pointer_cast<StringLit>((yyvsp[-2].sv_val));
        auto left_bool = std::dynamic_pointer_cast<BoolLit>((yyvsp[-2].sv_val));
        // 尝试将右值转换为各种类型
        auto right_int = std::dynamic_pointer_cast<IntLit>((yyvsp[0].sv_val));
        auto right_float = std::dynamic_pointer_cast<FloatLit>((yyvsp[0].sv_val));
        auto right_string = std::dynamic_pointer_cast<StringLit>((yyvsp[0].sv_val));
        auto right_bool = std::dynamic_pointer_cast<BoolLit>((yyvsp[0].sv_val));
        // 整数 + 整数
        if (left_int && right_int) {
            (yyval.sv_expr) = std::static_pointer_cast<Expr>(std::make_shared<IntLit>(left_int->val + right_int->val));
        }
        // 整数 + 浮点数 或 浮点数 + 整数/浮点数
        else if ((left_int && right_float) || (left_float && right_int) || (left_float && right_float)) {
            float left_val = left_float ? left_float->val : left_int->val;
            float right_val = right_float ? right_float->val : right_int->val;
            (yyval.sv_expr) = std::static_pointer_cast<Expr>(std::make_shared<FloatLit>(left_val + right_val));
        }
        else {
            // 类型不支持相加时抛出错误
            // yyerror("不支持的运算类型组合");
            (yyval.sv_expr) = nullptr;
        }
    }
#line 2473 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 84: /* expr: value '-' value  */
#line 582 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        // 尝试将左值转换为各种类型
        auto left_int = std::dynamic_pointer_cast<IntLit>((yyvsp[-2].sv_val));
        auto left_float = std::dynamic_pointer_cast<FloatLit>((yyvsp[-2].sv_val));
        auto left_string = std::dynamic_pointer_cast<StringLit>((yyvsp[-2].sv_val));
        auto left_bool = std::dynamic_pointer_cast<BoolLit>((yyvsp[-2].sv_val));
        // 尝试将右值转换为各种类型
        auto right_int = std::dynamic_pointer_cast<IntLit>((yyvsp[0].sv_val));
        auto right_float = std::dynamic_pointer_cast<FloatLit>((yyvsp[0].sv_val));
        auto right_string = std::dynamic_pointer_cast<StringLit>((yyvsp[0].sv_val));
        auto right_bool = std::dynamic_pointer_cast<BoolLit>((yyvsp[0].sv_val));
        // 整数 + 整数
        if (left_int && right_int) {
            (yyval.sv_expr) = std::static_pointer_cast<Expr>(std::make_shared<IntLit>(left_int->val - right_int->val));
        }
        // 整数 + 浮点数 或 浮点数 + 整数/浮点数
        else if ((left_int && right_float) || (left_float && right_int) || (left_float && right_float)) {
            float left_val = left_float ? left_float->val : left_int->val;
            float right_val = right_float ? right_float->val : right_int->val;
            (yyval.sv_expr) = std::static_pointer_cast<Expr>(std::make_shared<FloatLit>(left_val - right_val));
        }
        else {
            // 类型不支持相加时抛出错误
            // yyerror("不支持的运算类型组合");
            (yyval.sv_expr) = nullptr;
        }
    }
#line 2505 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 85: /* expr: value '*' value  */
#line 610 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        // 尝试将左值转换为各种类型
        auto left_int = std::dynamic_pointer_cast<IntLit>((yyvsp[-2].sv_val));
        auto left_float = std::dynamic_pointer_cast<FloatLit>((yyvsp[-2].sv_val));
        auto left_string = std::dynamic_pointer_cast<StringLit>((yyvsp[-2].sv_val));
        auto left_bool = std::dynamic_pointer_cast<BoolLit>((yyvsp[-2].sv_val));
        // 尝试将右值转换为各种类型
        auto right_int = std::dynamic_pointer_cast<IntLit>((yyvsp[0].sv_val));
        auto right_float = std::dynamic_pointer_cast<FloatLit>((yyvsp[0].sv_val));
        auto right_string = std::dynamic_pointer_cast<StringLit>((yyvsp[0].sv_val));
        auto right_bool = std::dynamic_pointer_cast<BoolLit>((yyvsp[0].sv_val));
        // 整数 + 整数
        if (left_int && right_int) {
            (yyval.sv_expr) = std::static_pointer_cast<Expr>(std::make_shared<IntLit>(left_int->val * right_int->val));
        }
        // 整数 + 浮点数 或 浮点数 + 整数/浮点数
        else if ((left_int && right_float) || (left_float && right_int) || (left_float && right_float)) {
            float left_val = left_float ? left_float->val : left_int->val;
            float right_val = right_float ? right_float->val : right_int->val;
            (yyval.sv_expr) = std::static_pointer_cast<Expr>(std::make_shared<FloatLit>(left_val * right_val));
        }
        else {
            // 类型不支持相加时抛出错误
            // yyerror("不支持的运算类型组合");
            (yyval.sv_expr) = nullptr;
        }
    }
#line 2537 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 86: /* expr: value '/' value  */
#line 638 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        // 尝试将左值转换为各种类型
        auto left_int = std::dynamic_pointer_cast<IntLit>((yyvsp[-2].sv_val));
        auto left_float = std::dynamic_pointer_cast<FloatLit>((yyvsp[-2].sv_val));
        auto left_string = std::dynamic_pointer_cast<StringLit>((yyvsp[-2].sv_val));
        auto left_bool = std::dynamic_pointer_cast<BoolLit>((yyvsp[-2].sv_val));
        // 尝试将右值转换为各种类型
        auto right_int = std::dynamic_pointer_cast<IntLit>((yyvsp[0].sv_val));
        auto right_float = std::dynamic_pointer_cast<FloatLit>((yyvsp[0].sv_val));
        auto right_string = std::dynamic_pointer_cast<StringLit>((yyvsp[0].sv_val));
        auto right_bool = std::dynamic_pointer_cast<BoolLit>((yyvsp[0].sv_val));
        // 整数 + 整数
        if (left_int && right_int) {
            (yyval.sv_expr) = std::static_pointer_cast<Expr>(std::make_shared<IntLit>(left_int->val / right_int->val));
        }
        // 整数 + 浮点数 或 浮点数 + 整数/浮点数
        else if ((left_int && right_float) || (left_float && right_int) || (left_float && right_float)) {
            float left_val = left_float ? left_float->val : left_int->val;
            float right_val = right_float ? right_float->val : right_int->val;
            (yyval.sv_expr) = std::static_pointer_cast<Expr>(std::make_shared<FloatLit>(left_val / right_val));
        }
        else {
            // 类型不支持相加时抛出错误
            // yyerror("不支持的运算类型组合");
            (yyval.sv_expr) = nullptr;
        }
    }
#line 2569 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 87: /* expr: value value  */
#line 666 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        // 尝试将左值转换为各种类型
        auto left_int = std::dynamic_pointer_cast<IntLit>((yyvsp[-1].sv_val));
        auto left_float = std::dynamic_pointer_cast<FloatLit>((yyvsp[-1].sv_val));
        auto left_string = std::dynamic_pointer_cast<StringLit>((yyvsp[-1].sv_val));
        auto left_bool = std::dynamic_pointer_cast<BoolLit>((yyvsp[-1].sv_val));
        // 尝试将右值转换为各种类型
        auto right_int = std::dynamic_pointer_cast<IntLit>((yyvsp[0].sv_val));
        auto right_float = std::dynamic_pointer_cast<FloatLit>((yyvsp[0].sv_val));
        auto right_string = std::dynamic_pointer_cast<StringLit>((yyvsp[0].sv_val));
        auto right_bool = std::dynamic_pointer_cast<BoolLit>((yyvsp[0].sv_val));
        // 整数 + 整数
        if (left_int && right_int) {
            (yyval.sv_expr) = std::static_pointer_cast<Expr>(std::make_shared<IntLit>(left_int->val + right_int->val));
        }
        // 整数 + 浮点数 或 浮点数 + 整数/浮点数
        else if ((left_int && right_float) || (left_float && right_int) || (left_float && right_float)) {
            float left_val = left_float ? left_float->val : left_int->val;
            float right_val = right_float ? right_float->val : right_int->val;
            (yyval.sv_expr) = std::static_pointer_cast<Expr>(std::make_shared<FloatLit>(left_val + right_val));
        }
        else {
            // 类型不支持相加时抛出错误
            // yyerror("不支持的运算类型组合");
            (yyval.sv_expr) = nullptr;
        }
    }
#line 2601 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 88: /* setClauses: setClause  */
#line 697 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_set_clauses) = std::vector<std::shared_ptr<SetClause>>{(yyvsp[0].sv_set_clause)};
    }
#line 2609 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 89: /* setClauses: setClauses ',' setClause  */
#line 701 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_set_clauses).push_back((yyvsp[0].sv_set_clause));
    }
#line 2617 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 90: /* setClause: colName '=' valuexpr  */
#line 708 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        // printf("setClause\n");
        (yyval.sv_set_clause) = std::make_shared<SetClause>((yyvsp[-2].sv_str), (yyvsp[0].sv_twoexpr));
    }
#line 2626 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 91: /* valuexpr: expr op expr  */
#line 716 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        // printf("valuexpr\n");
        (yyval.sv_twoexpr) = std::make_shared<BinaryUpdateExpr>((yyvsp[-2].sv_expr), (yyvsp[-1].sv_comp_op), (yyvsp[0].sv_expr));
    }
#line 2635 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 92: /* valuexpr: value  */
#line 721 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_twoexpr) = std::make_shared<BinaryUpdateExpr>((yyvsp[0].sv_val), SV_OP_NONE, nullptr);
    }
#line 2643 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 93: /* valuexpr: expr value  */
#line 725 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_twoexpr) = std::make_shared<BinaryUpdateExpr>((yyvsp[-1].sv_expr), SV_OP_ADD, (yyvsp[0].sv_val));
    }
#line 2651 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 94: /* selector: '*'  */
#line 732 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_select_items) = {};
    }
#line 2659 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 95: /* selector: select_item  */
#line 736 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_select_items) = std::vector<std::shared_ptr<SelectItem>>{(yyvsp[0].sv_select_item)};
    }
#line 2667 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 96: /* selector: selector ',' select_item  */
#line 740 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_select_items).push_back((yyvsp[0].sv_select_item));
    }
#line 2675 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 97: /* select_item: col  */
#line 748 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_select_item) = std::make_shared<SelectItem>((yyvsp[0].sv_col), nullptr);
    }
#line 2683 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 98: /* select_item: aggExpr optAlias  */
#line 752 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyvsp[-1].sv_agg)->alias = (yyvsp[0].sv_str);  // $2现在是std::string，可以直接赋值
        (yyval.sv_select_item) = std::make_shared<SelectItem>(nullptr, (yyvsp[-1].sv_agg));
    }
#line 2692 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 99: /* tableExpression: tableRef  */
#line 762 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_table_expr).tables = std::vector<std::pair<std::string, std::string>>{(yyvsp[0].sv_strpair)};
        (yyval.sv_table_expr).joins = {};
    }
#line 2701 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 100: /* tableExpression: tableExpression ',' tableRef  */
#line 767 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_table_expr).tables = (yyvsp[-2].sv_table_expr).tables;
        (yyval.sv_table_expr).tables.push_back((yyvsp[0].sv_strpair));
        (yyval.sv_table_expr).joins = (yyvsp[-2].sv_table_expr).joins;
    }
#line 2711 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 101: /* tableExpression: tableExpression JOIN tableRef ON whereClause  */
#line 773 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_table_expr).tables = (yyvsp[-4].sv_table_expr).tables;
        (yyval.sv_table_expr).tables.push_back((yyvsp[-2].sv_strpair));
        
        // 创建JOIN表达式
        std::string left_table = (yyvsp[-4].sv_table_expr).tables.back().first;
        std::string right_table = (yyvsp[-2].sv_strpair).first;
        auto join = std::make_shared<JoinExpr>(left_table, right_table, (yyvsp[0].sv_conds), INNER_JOIN);
        (yyval.sv_table_expr).joins = (yyvsp[-4].sv_table_expr).joins;
        (yyval.sv_table_expr).joins.push_back(join);
    }
#line 2727 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 102: /* tableExpression: tableExpression JOIN tableRef  */
#line 785 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_table_expr).tables = (yyvsp[-2].sv_table_expr).tables;
        (yyval.sv_table_expr).tables.push_back((yyvsp[0].sv_strpair));
        (yyval.sv_table_expr).joins = (yyvsp[-2].sv_table_expr).joins;
        
        // 这种情况下JOIN条件会在WHERE子句中处理
        // 这里仅记录JOIN操作，不设置ON条件
        std::string left_table = (yyvsp[-2].sv_table_expr).tables.back().first;
        std::string right_table = (yyvsp[0].sv_strpair).first;
        auto join = std::make_shared<JoinExpr>(left_table, right_table, std::vector<std::shared_ptr<BinaryExpr>>(), INNER_JOIN);
        (yyval.sv_table_expr).joins.push_back(join);
    }
#line 2744 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 103: /* tableExpression: tableExpression SEMI JOIN tableRef ON whereClause  */
#line 798 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_table_expr).tables = (yyvsp[-5].sv_table_expr).tables;
        (yyval.sv_table_expr).tables.push_back((yyvsp[-2].sv_strpair));
        
        // 创建SEMI JOIN表达式
        std::string left_table = (yyvsp[-5].sv_table_expr).tables.back().first;
        std::string right_table = (yyvsp[-2].sv_strpair).first;
        auto join = std::make_shared<JoinExpr>(left_table, right_table, (yyvsp[0].sv_conds), SEMI_JOIN);
        (yyval.sv_table_expr).joins = (yyvsp[-5].sv_table_expr).joins;
        (yyval.sv_table_expr).joins.push_back(join);
    }
#line 2760 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 104: /* tableExpression: tableExpression ANTI JOIN tableRef ON whereClause  */
#line 810 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_table_expr).tables = (yyvsp[-5].sv_table_expr).tables;
        (yyval.sv_table_expr).tables.push_back((yyvsp[-2].sv_strpair));

        // 创建ANTI JOIN表达式
        std::string left_table = (yyvsp[-5].sv_table_expr).tables.back().first;
        std::string right_table = (yyvsp[-2].sv_strpair).first;
        auto join = std::make_shared<JoinExpr>(left_table, right_table, (yyvsp[0].sv_conds), ANTI_JOIN);
        (yyval.sv_table_expr).joins = (yyvsp[-5].sv_table_expr).joins;
        (yyval.sv_table_expr).joins.push_back(join);
    }
#line 2776 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 105: /* tableRef: tbName  */
#line 826 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_strpair) = std::make_pair((yyvsp[0].sv_str), "");  // 无别名时，别名为空字符串
    }
#line 2784 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 106: /* tableRef: tbName optAlias  */
#line 830 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_strpair) = std::make_pair((yyvsp[-1].sv_str), (yyvsp[0].sv_str));
    }
#line 2792 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 107: /* optAlias: AS IDENTIFIER  */
#line 838 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_str) = (yyvsp[0].sv_str);
    }
#line 2800 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 108: /* optAlias: IDENTIFIER  */
#line 842 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_str) = (yyvsp[0].sv_str);
    }
#line 2808 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 109: /* opt_order_clause: ORDER BY order_clause  */
#line 849 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    { 
        (yyval.sv_orderby) = (yyvsp[0].sv_orderby); 
    }
#line 2816 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 110: /* opt_order_clause: %empty  */
#line 852 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
                      { /* ignore*/ }
#line 2822 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 111: /* order_clause: col opt_asc_desc  */
#line 857 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    { 
        auto order = std::make_shared<OrderBy>((yyvsp[-1].sv_col), (yyvsp[0].sv_orderby_dir));
        (yyval.sv_orderby) = std::vector<std::shared_ptr<OrderBy>>{order};
    }
#line 2831 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 112: /* order_clause: order_clause ',' col opt_asc_desc  */
#line 862 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
    {
        auto next_order = std::make_shared<OrderBy>((yyvsp[-1].sv_col), (yyvsp[0].sv_orderby_dir));
        (yyval.sv_orderby).push_back(next_order);
    }
#line 2840 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 113: /* opt_asc_desc: ASC  */
#line 869 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
                 { (yyval.sv_orderby_dir) = OrderBy_ASC;     }
#line 2846 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 114: /* opt_asc_desc: DESC  */
#line 870 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
                 { (yyval.sv_orderby_dir) = OrderBy_DESC;    }
#line 2852 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 115: /* opt_asc_desc: %empty  */
#line 871 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
            { (yyval.sv_orderby_dir) = OrderBy_DEFAULT; }
#line 2858 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 116: /* opt_limit: LIMIT VALUE_INT  */
#line 875 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
                     { (yyval.sv_int) = (yyvsp[0].sv_int); }
#line 2864 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 117: /* opt_limit: %empty  */
#line 876 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
                      { (yyval.sv_int) = -1; }
#line 2870 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 118: /* set_knob_type: ENABLE_NESTLOOP  */
#line 880 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
                    { (yyval.sv_setKnobType) = EnableNestLoop; }
#line 2876 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 119: /* set_knob_type: ENABLE_SORTMERGE  */
#line 881 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"
                         { (yyval.sv_setKnobType) = EnableSortMerge; }
#line 2882 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"
    break;


#line 2886 "/root/dbms3.1/db2025-x1/src/parser/yacc.tab.cpp"

      default: break;
    }
  /* User semantic actions sometimes alter yychar, and that requires
     that yytoken be updated with the new translation.  We take the
     approach of translating immediately before every use of yytoken.
     One alternative is translating here after every semantic action,
     but that translation would be missed if the semantic action invokes
     YYABORT, YYACCEPT, or YYERROR immediately after altering yychar or
     if it invokes YYBACKUP.  In the case of YYABORT or YYACCEPT, an
     incorrect destructor might then be invoked immediately.  In the
     case of YYERROR or YYBACKUP, subsequent parser actions might lead
     to an incorrect destructor call or verbose syntax error message
     before the lookahead is translated.  */
  YY_SYMBOL_PRINT ("-> $$ =", YY_CAST (yysymbol_kind_t, yyr1[yyn]), &yyval, &yyloc);

  YYPOPSTACK (yylen);
  yylen = 0;

  *++yyvsp = yyval;
  *++yylsp = yyloc;

  /* Now 'shift' the result of the reduction.  Determine what state
     that goes to, based on the state we popped back to and the rule
     number reduced by.  */
  {
    const int yylhs = yyr1[yyn] - YYNTOKENS;
    const int yyi = yypgoto[yylhs] + *yyssp;
    yystate = (0 <= yyi && yyi <= YYLAST && yycheck[yyi] == *yyssp
               ? yytable[yyi]
               : yydefgoto[yylhs]);
  }

  goto yynewstate;


/*--------------------------------------.
| yyerrlab -- here on detecting error.  |
`--------------------------------------*/
yyerrlab:
  /* Make sure we have latest lookahead translation.  See comments at
     user semantic actions for why this is necessary.  */
  yytoken = yychar == YYEMPTY ? YYSYMBOL_YYEMPTY : YYTRANSLATE (yychar);
  /* If not already recovering from an error, report this error.  */
  if (!yyerrstatus)
    {
      ++yynerrs;
      {
        yypcontext_t yyctx
          = {yyssp, yytoken, &yylloc};
        char const *yymsgp = YY_("syntax error");
        int yysyntax_error_status;
        yysyntax_error_status = yysyntax_error (&yymsg_alloc, &yymsg, &yyctx);
        if (yysyntax_error_status == 0)
          yymsgp = yymsg;
        else if (yysyntax_error_status == -1)
          {
            if (yymsg != yymsgbuf)
              YYSTACK_FREE (yymsg);
            yymsg = YY_CAST (char *,
                             YYSTACK_ALLOC (YY_CAST (YYSIZE_T, yymsg_alloc)));
            if (yymsg)
              {
                yysyntax_error_status
                  = yysyntax_error (&yymsg_alloc, &yymsg, &yyctx);
                yymsgp = yymsg;
              }
            else
              {
                yymsg = yymsgbuf;
                yymsg_alloc = sizeof yymsgbuf;
                yysyntax_error_status = YYENOMEM;
              }
          }
        yyerror (&yylloc, yymsgp);
        if (yysyntax_error_status == YYENOMEM)
          YYNOMEM;
      }
    }

  yyerror_range[1] = yylloc;
  if (yyerrstatus == 3)
    {
      /* If just tried and failed to reuse lookahead token after an
         error, discard it.  */

      if (yychar <= YYEOF)
        {
          /* Return failure if at end of input.  */
          if (yychar == YYEOF)
            YYABORT;
        }
      else
        {
          yydestruct ("Error: discarding",
                      yytoken, &yylval, &yylloc);
          yychar = YYEMPTY;
        }
    }

  /* Else will try to reuse lookahead token after shifting the error
     token.  */
  goto yyerrlab1;


/*---------------------------------------------------.
| yyerrorlab -- error raised explicitly by YYERROR.  |
`---------------------------------------------------*/
yyerrorlab:
  /* Pacify compilers when the user code never invokes YYERROR and the
     label yyerrorlab therefore never appears in user code.  */
  if (0)
    YYERROR;
  ++yynerrs;

  /* Do not reclaim the symbols of the rule whose action triggered
     this YYERROR.  */
  YYPOPSTACK (yylen);
  yylen = 0;
  YY_STACK_PRINT (yyss, yyssp);
  yystate = *yyssp;
  goto yyerrlab1;


/*-------------------------------------------------------------.
| yyerrlab1 -- common code for both syntax error and YYERROR.  |
`-------------------------------------------------------------*/
yyerrlab1:
  yyerrstatus = 3;      /* Each real token shifted decrements this.  */

  /* Pop stack until we find a state that shifts the error token.  */
  for (;;)
    {
      yyn = yypact[yystate];
      if (!yypact_value_is_default (yyn))
        {
          yyn += YYSYMBOL_YYerror;
          if (0 <= yyn && yyn <= YYLAST && yycheck[yyn] == YYSYMBOL_YYerror)
            {
              yyn = yytable[yyn];
              if (0 < yyn)
                break;
            }
        }

      /* Pop the current state because it cannot handle the error token.  */
      if (yyssp == yyss)
        YYABORT;

      yyerror_range[1] = *yylsp;
      yydestruct ("Error: popping",
                  YY_ACCESSING_SYMBOL (yystate), yyvsp, yylsp);
      YYPOPSTACK (1);
      yystate = *yyssp;
      YY_STACK_PRINT (yyss, yyssp);
    }

  YY_IGNORE_MAYBE_UNINITIALIZED_BEGIN
  *++yyvsp = yylval;
  YY_IGNORE_MAYBE_UNINITIALIZED_END

  yyerror_range[2] = yylloc;
  ++yylsp;
  YYLLOC_DEFAULT (*yylsp, yyerror_range, 2);

  /* Shift the error token.  */
  YY_SYMBOL_PRINT ("Shifting", YY_ACCESSING_SYMBOL (yyn), yyvsp, yylsp);

  yystate = yyn;
  goto yynewstate;


/*-------------------------------------.
| yyacceptlab -- YYACCEPT comes here.  |
`-------------------------------------*/
yyacceptlab:
  yyresult = 0;
  goto yyreturnlab;


/*-----------------------------------.
| yyabortlab -- YYABORT comes here.  |
`-----------------------------------*/
yyabortlab:
  yyresult = 1;
  goto yyreturnlab;


/*-----------------------------------------------------------.
| yyexhaustedlab -- YYNOMEM (memory exhaustion) comes here.  |
`-----------------------------------------------------------*/
yyexhaustedlab:
  yyerror (&yylloc, YY_("memory exhausted"));
  yyresult = 2;
  goto yyreturnlab;


/*----------------------------------------------------------.
| yyreturnlab -- parsing is finished, clean up and return.  |
`----------------------------------------------------------*/
yyreturnlab:
  if (yychar != YYEMPTY)
    {
      /* Make sure we have latest lookahead translation.  See comments at
         user semantic actions for why this is necessary.  */
      yytoken = YYTRANSLATE (yychar);
      yydestruct ("Cleanup: discarding lookahead",
                  yytoken, &yylval, &yylloc);
    }
  /* Do not reclaim the symbols of the rule whose action triggered
     this YYABORT or YYACCEPT.  */
  YYPOPSTACK (yylen);
  YY_STACK_PRINT (yyss, yyssp);
  while (yyssp != yyss)
    {
      yydestruct ("Cleanup: popping",
                  YY_ACCESSING_SYMBOL (+*yyssp), yyvsp, yylsp);
      YYPOPSTACK (1);
    }
#ifndef yyoverflow
  if (yyss != yyssa)
    YYSTACK_FREE (yyss);
#endif
  if (yymsg != yymsgbuf)
    YYSTACK_FREE (yymsg);
  return yyresult;
}

#line 888 "/root/dbms3.1/db2025-x1/src/parser/yacc.y"

