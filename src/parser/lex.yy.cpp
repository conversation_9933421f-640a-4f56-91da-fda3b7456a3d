#line 2 "/root/dbms3.1/db2025-x1/src/parser/lex.yy.cpp"

#line 4 "/root/dbms3.1/db2025-x1/src/parser/lex.yy.cpp"

#define  YY_INT_ALIGNED short int

/* A lexical scanner generated by flex */

#define FLEX_SCANNER
#define YY_FLEX_MAJOR_VERSION 2
#define YY_FLEX_MINOR_VERSION 6
#define YY_FLEX_SUBMINOR_VERSION 4
#if YY_FLEX_SUBMINOR_VERSION > 0
#define FLEX_BETA
#endif

#ifdef yyget_lval
#define yyget_lval_ALREADY_DEFINED
#else
#define yyget_lval yyget_lval
#endif

#ifdef yyset_lval
#define yyset_lval_ALREADY_DEFINED
#else
#define yyset_lval yyset_lval
#endif

#ifdef yyget_lloc
#define yyget_lloc_ALREADY_DEFINED
#else
#define yyget_lloc yyget_lloc
#endif

#ifdef yyset_lloc
#define yyset_lloc_ALREADY_DEFINED
#else
#define yyset_lloc yyset_lloc
#endif

/* First, we deal with  platform-specific or compiler-specific issues. */

/* begin standard C headers. */
#include <stdio.h>
#include <string.h>
#include <errno.h>
#include <stdlib.h>

/* end standard C headers. */

/* flex integer type definitions */

#ifndef FLEXINT_H
#define FLEXINT_H

/* C99 systems have <inttypes.h>. Non-C99 systems may or may not. */

#if defined (__STDC_VERSION__) && __STDC_VERSION__ >= 199901L

/* C99 says to define __STDC_LIMIT_MACROS before including stdint.h,
 * if you want the limit (max/min) macros for int types. 
 */
#ifndef __STDC_LIMIT_MACROS
#define __STDC_LIMIT_MACROS 1
#endif

#include <inttypes.h>
typedef int8_t flex_int8_t;
typedef uint8_t flex_uint8_t;
typedef int16_t flex_int16_t;
typedef uint16_t flex_uint16_t;
typedef int32_t flex_int32_t;
typedef uint32_t flex_uint32_t;
#else
typedef signed char flex_int8_t;
typedef short int flex_int16_t;
typedef int flex_int32_t;
typedef unsigned char flex_uint8_t; 
typedef unsigned short int flex_uint16_t;
typedef unsigned int flex_uint32_t;

/* Limits of integral types. */
#ifndef INT8_MIN
#define INT8_MIN               (-128)
#endif
#ifndef INT16_MIN
#define INT16_MIN              (-32767-1)
#endif
#ifndef INT32_MIN
#define INT32_MIN              (-2147483647-1)
#endif
#ifndef INT8_MAX
#define INT8_MAX               (127)
#endif
#ifndef INT16_MAX
#define INT16_MAX              (32767)
#endif
#ifndef INT32_MAX
#define INT32_MAX              (2147483647)
#endif
#ifndef UINT8_MAX
#define UINT8_MAX              (255U)
#endif
#ifndef UINT16_MAX
#define UINT16_MAX             (65535U)
#endif
#ifndef UINT32_MAX
#define UINT32_MAX             (4294967295U)
#endif

#ifndef SIZE_MAX
#define SIZE_MAX               (~(size_t)0)
#endif

#endif /* ! C99 */

#endif /* ! FLEXINT_H */

/* begin standard C++ headers. */

/* TODO: this is always defined, so inline it */
#define yyconst const

#if defined(__GNUC__) && __GNUC__ >= 3
#define yynoreturn __attribute__((__noreturn__))
#else
#define yynoreturn
#endif

/* Returned upon end-of-file. */
#define YY_NULL 0

/* Promotes a possibly negative, possibly signed char to an
 *   integer in range [0..255] for use as an array index.
 */
#define YY_SC_TO_UI(c) ((YY_CHAR) (c))

/* Enter a start condition.  This macro really ought to take a parameter,
 * but we do it the disgusting crufty way forced on us by the ()-less
 * definition of BEGIN.
 */
#define BEGIN (yy_start) = 1 + 2 *
/* Translate the current start state into a value that can be later handed
 * to BEGIN to return to the state.  The YYSTATE alias is for lex
 * compatibility.
 */
#define YY_START (((yy_start) - 1) / 2)
#define YYSTATE YY_START
/* Action number for EOF rule of a given start state. */
#define YY_STATE_EOF(state) (YY_END_OF_BUFFER + state + 1)
/* Special action meaning "start processing a new file". */
#define YY_NEW_FILE yyrestart( yyin  )
#define YY_END_OF_BUFFER_CHAR 0

/* Size of default input buffer. */
#ifndef YY_BUF_SIZE
#ifdef __ia64__
/* On IA-64, the buffer size is 16k, not 8k.
 * Moreover, YY_BUF_SIZE is 2*YY_READ_BUF_SIZE in the general case.
 * Ditto for the __ia64__ case accordingly.
 */
#define YY_BUF_SIZE 32768
#else
#define YY_BUF_SIZE 16384
#endif /* __ia64__ */
#endif

/* The state buf must be large enough to hold one state per character in the main buffer.
 */
#define YY_STATE_BUF_SIZE   ((YY_BUF_SIZE + 2) * sizeof(yy_state_type))

#ifndef YY_TYPEDEF_YY_BUFFER_STATE
#define YY_TYPEDEF_YY_BUFFER_STATE
typedef struct yy_buffer_state *YY_BUFFER_STATE;
#endif

#ifndef YY_TYPEDEF_YY_SIZE_T
#define YY_TYPEDEF_YY_SIZE_T
typedef size_t yy_size_t;
#endif

extern int yyleng;

extern FILE *yyin, *yyout;

#define EOB_ACT_CONTINUE_SCAN 0
#define EOB_ACT_END_OF_FILE 1
#define EOB_ACT_LAST_MATCH 2
    
    #define YY_LESS_LINENO(n)
    #define YY_LINENO_REWIND_TO(ptr)
    
/* Return all but the first "n" matched characters back to the input stream. */
#define yyless(n) \
	do \
		{ \
		/* Undo effects of setting up yytext. */ \
        int yyless_macro_arg = (n); \
        YY_LESS_LINENO(yyless_macro_arg);\
		*yy_cp = (yy_hold_char); \
		YY_RESTORE_YY_MORE_OFFSET \
		(yy_c_buf_p) = yy_cp = yy_bp + yyless_macro_arg - YY_MORE_ADJ; \
		YY_DO_BEFORE_ACTION; /* set up yytext again */ \
		} \
	while ( 0 )
#define unput(c) yyunput( c, (yytext_ptr)  )

#ifndef YY_STRUCT_YY_BUFFER_STATE
#define YY_STRUCT_YY_BUFFER_STATE
struct yy_buffer_state
	{
	FILE *yy_input_file;

	char *yy_ch_buf;		/* input buffer */
	char *yy_buf_pos;		/* current position in input buffer */

	/* Size of input buffer in bytes, not including room for EOB
	 * characters.
	 */
	int yy_buf_size;

	/* Number of characters read into yy_ch_buf, not including EOB
	 * characters.
	 */
	int yy_n_chars;

	/* Whether we "own" the buffer - i.e., we know we created it,
	 * and can realloc() it to grow it, and should free() it to
	 * delete it.
	 */
	int yy_is_our_buffer;

	/* Whether this is an "interactive" input source; if so, and
	 * if we're using stdio for input, then we want to use getc()
	 * instead of fread(), to make sure we stop fetching input after
	 * each newline.
	 */
	int yy_is_interactive;

	/* Whether we're considered to be at the beginning of a line.
	 * If so, '^' rules will be active on the next match, otherwise
	 * not.
	 */
	int yy_at_bol;

    int yy_bs_lineno; /**< The line count. */
    int yy_bs_column; /**< The column count. */

	/* Whether to try to fill the input buffer when we reach the
	 * end of it.
	 */
	int yy_fill_buffer;

	int yy_buffer_status;

#define YY_BUFFER_NEW 0
#define YY_BUFFER_NORMAL 1
	/* When an EOF's been seen but there's still some text to process
	 * then we mark the buffer as YY_EOF_PENDING, to indicate that we
	 * shouldn't try reading from the input source any more.  We might
	 * still have a bunch of tokens to match, though, because of
	 * possible backing-up.
	 *
	 * When we actually see the EOF, we change the status to "new"
	 * (via yyrestart()), so that the user can continue scanning by
	 * just pointing yyin at a new input file.
	 */
#define YY_BUFFER_EOF_PENDING 2

	};
#endif /* !YY_STRUCT_YY_BUFFER_STATE */

/* Stack of input buffers. */
static size_t yy_buffer_stack_top = 0; /**< index of top of stack. */
static size_t yy_buffer_stack_max = 0; /**< capacity of stack. */
static YY_BUFFER_STATE * yy_buffer_stack = NULL; /**< Stack as an array. */

/* We provide macros for accessing buffer states in case in the
 * future we want to put the buffer states in a more general
 * "scanner state".
 *
 * Returns the top of the stack, or NULL.
 */
#define YY_CURRENT_BUFFER ( (yy_buffer_stack) \
                          ? (yy_buffer_stack)[(yy_buffer_stack_top)] \
                          : NULL)
/* Same as previous macro, but useful when we know that the buffer stack is not
 * NULL or when we need an lvalue. For internal use only.
 */
#define YY_CURRENT_BUFFER_LVALUE (yy_buffer_stack)[(yy_buffer_stack_top)]

/* yy_hold_char holds the character lost when yytext is formed. */
static char yy_hold_char;
static int yy_n_chars;		/* number of characters read into yy_ch_buf */
int yyleng;

/* Points to current character in buffer. */
static char *yy_c_buf_p = NULL;
static int yy_init = 0;		/* whether we need to initialize */
static int yy_start = 0;	/* start state number */

/* Flag which is used to allow yywrap()'s to do buffer switches
 * instead of setting up a fresh yyin.  A bit of a hack ...
 */
static int yy_did_buffer_switch_on_eof;

void yyrestart ( FILE *input_file  );
void yy_switch_to_buffer ( YY_BUFFER_STATE new_buffer  );
YY_BUFFER_STATE yy_create_buffer ( FILE *file, int size  );
void yy_delete_buffer ( YY_BUFFER_STATE b  );
void yy_flush_buffer ( YY_BUFFER_STATE b  );
void yypush_buffer_state ( YY_BUFFER_STATE new_buffer  );
void yypop_buffer_state ( void );

static void yyensure_buffer_stack ( void );
static void yy_load_buffer_state ( void );
static void yy_init_buffer ( YY_BUFFER_STATE b, FILE *file  );
#define YY_FLUSH_BUFFER yy_flush_buffer( YY_CURRENT_BUFFER )

YY_BUFFER_STATE yy_scan_buffer ( char *base, yy_size_t size  );
YY_BUFFER_STATE yy_scan_string ( const char *yy_str  );
YY_BUFFER_STATE yy_scan_bytes ( const char *bytes, int len  );

void *yyalloc ( yy_size_t  );
void *yyrealloc ( void *, yy_size_t  );
void yyfree ( void *  );

#define yy_new_buffer yy_create_buffer
#define yy_set_interactive(is_interactive) \
	{ \
	if ( ! YY_CURRENT_BUFFER ){ \
        yyensure_buffer_stack (); \
		YY_CURRENT_BUFFER_LVALUE =    \
            yy_create_buffer( yyin, YY_BUF_SIZE ); \
	} \
	YY_CURRENT_BUFFER_LVALUE->yy_is_interactive = is_interactive; \
	}
#define yy_set_bol(at_bol) \
	{ \
	if ( ! YY_CURRENT_BUFFER ){\
        yyensure_buffer_stack (); \
		YY_CURRENT_BUFFER_LVALUE =    \
            yy_create_buffer( yyin, YY_BUF_SIZE ); \
	} \
	YY_CURRENT_BUFFER_LVALUE->yy_at_bol = at_bol; \
	}
#define YY_AT_BOL() (YY_CURRENT_BUFFER_LVALUE->yy_at_bol)

/* Begin user sect3 */

#define yywrap() (/*CONSTCOND*/1)
#define YY_SKIP_YYWRAP
typedef flex_uint8_t YY_CHAR;

FILE *yyin = NULL, *yyout = NULL;

typedef int yy_state_type;

extern int yylineno;
int yylineno = 1;

extern char *yytext;
#ifdef yytext_ptr
#undef yytext_ptr
#endif
#define yytext_ptr yytext

static yy_state_type yy_get_previous_state ( void );
static yy_state_type yy_try_NUL_trans ( yy_state_type current_state  );
static int yy_get_next_buffer ( void );
static void yynoreturn yy_fatal_error ( const char* msg  );

/* Done after the current pattern has been matched and before the
 * corresponding action - sets up yytext.
 */
#define YY_DO_BEFORE_ACTION \
	(yytext_ptr) = yy_bp; \
	yyleng = (int) (yy_cp - yy_bp); \
	(yy_hold_char) = *yy_cp; \
	*yy_cp = '\0'; \
	(yy_c_buf_p) = yy_cp;
#define YY_NUM_RULES 69
#define YY_END_OF_BUFFER 70
/* This struct is not used in this scanner,
   but its presence is necessary. */
struct yy_trans_info
	{
	flex_int32_t yy_verify;
	flex_int32_t yy_nxt;
	};
static const flex_int16_t yy_accept[251] =
    {   0,
        0,    0,    0,    0,   70,   68,    6,    7,    7,   68,
       68,   62,   62,   62,   62,   64,   62,   62,   63,   63,
       63,   63,   63,   63,   63,   63,   63,   63,   63,   63,
       63,   63,   63,   63,   63,   63,   63,   63,   68,    3,
        4,    6,    7,   61,    0,   66,    5,    0,   67,   64,
        1,   67,   67,   65,   59,   60,   58,   63,   63,   63,
       47,   63,   63,   36,   63,   63,   63,   63,   63,   63,
       63,   63,   63,   63,   63,   63,   63,   63,   63,   63,
       63,   63,   63,   53,   63,   63,   63,   63,   63,   63,
       63,   63,   63,   63,   63,    2,    5,    5,    5,   65,

       63,   31,   63,   37,   42,   63,   63,   63,   63,   63,
       63,   63,   63,   63,   63,   63,   63,   63,   63,   63,
       63,   63,   63,   63,   27,   63,   63,   63,   41,   40,
       63,   63,   63,   63,   25,   63,   63,   43,   63,   63,
       63,   63,   63,    5,    5,   63,   55,   63,   28,   63,
       63,   63,   63,   17,   16,   63,   33,   63,   63,   63,
       22,   63,   63,   34,   63,   63,   19,   32,   63,   44,
       63,   63,   63,   54,    8,   63,   63,   56,   63,   63,
       63,   11,    9,   63,   39,   63,   63,   63,   63,   57,
       29,   45,   63,   30,   63,   48,   35,   63,   63,   63,

       15,   63,   63,   23,   10,   14,   21,   63,   63,   46,
       18,   63,   26,   63,   13,   24,   20,   63,   38,   63,
       63,   63,   63,   12,   63,   63,   63,   63,   63,   63,
       63,   63,   63,   63,   63,   63,   63,   63,   63,   63,
       63,   63,   63,   49,   63,   63,   50,   63,   51,    0
    } ;

static const YY_CHAR yy_ec[256] =
    {   0,
        1,    1,    1,    1,    1,    1,    1,    1,    2,    3,
        1,    1,    4,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    2,    5,    1,    1,    1,    1,    1,    6,    7,
        8,    9,   10,   11,   12,   13,   14,   15,   15,   15,
       15,   15,   15,   15,   15,   15,   15,    1,   16,   17,
       18,   19,    1,    1,   20,   21,   22,   23,   24,   25,
       26,   27,   28,   29,   30,   31,   32,   33,   34,   35,
       36,   37,   38,   39,   40,   41,   42,   43,   44,   36,
        1,    1,    1,    1,   45,    1,   46,   47,   48,   49,

       50,   51,   52,   53,   54,   55,   56,   57,   58,   59,
       60,   61,   36,   62,   63,   64,   65,   66,   67,   68,
       69,   36,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,

        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1
    } ;

static const YY_CHAR yy_meta[70] =
    {   0,
        1,    1,    2,    1,    1,    1,    1,    1,    3,    1,
        1,    4,    4,    4,    4,    1,    1,    1,    1,    4,
        4,    4,    4,    4,    4,    4,    4,    4,    4,    4,
        4,    4,    4,    4,    4,    4,    4,    4,    4,    4,
        4,    4,    4,    4,    4,    4,    4,    4,    4,    4,
        4,    4,    4,    4,    4,    4,    4,    4,    4,    4,
        4,    4,    4,    4,    4,    4,    4,    4,    4
    } ;

static const flex_int16_t yy_base[259] =
    {   0,
        0,    0,  162,  160,  167, 1441,  162, 1441,  160,  143,
      151, 1441,   58,  138,   62,   64,   56,  132,   68,   72,
       80,   86,  106,  131,   91,  146,  142,  112,   99,  166,
      169,  172,  190,  239,  198,  194,  225,  229,  133, 1441,
      121,  130, 1441, 1441,  115, 1441,  304,   94,   74,  233,
     1441,   81,   88,  115, 1441, 1441, 1441,  245,  256,  312,
      271,  315,  318,  260,  287,  324,  333,  340,  283,  346,
      360,  367,  372,  388,  395,  398,  403,  413,  428,  336,
      433,  406,  445,  263,  457,  453,  476,  479,  482,  473,
      504,  487,  507,  510,  523, 1441,    0,  573,  592,  123,

      551,  274,  531,  536,  541,  558,  602,  605,  608,  611,
      616,  620,  634,  548,  637,  631,  640,  659,  668,  646,
      671,  677,  680,  684,  701,  704,  708,  719,  715,  727,
      731,  734,  740,  744,  737,  757,  761,  764,  770,  773,
      782,  791,  795,  857,    0,  805,  798,  801,  808,  823,
      833,  840,  868,  826,  829,  871,  836,  874,  877,  886,
      896,  899,  902,  905,  909,  917,  924,  927,  930,  933,
      943,  950,  953,  960,  969,  972,  975,  978,  990,  994,
      997, 1000, 1003, 1010, 1018, 1021, 1026, 1029, 1044, 1047,
     1050, 1053, 1056, 1071, 1074, 1077, 1080, 1083, 1087, 1092,

     1097, 1103, 1106, 1109, 1118, 1129, 1133, 1136, 1142, 1145,
     1149, 1152, 1158, 1164, 1170, 1173, 1176, 1179, 1182, 1185,
     1191, 1194, 1209, 1212, 1218, 1215, 1221, 1235, 1238, 1242,
     1248, 1251, 1254, 1260, 1275, 1279, 1285, 1305, 1301, 1309,
     1312, 1318, 1328, 1336, 1340, 1345, 1348, 1354, 1362, 1441,
     1418, 1422,   87, 1424,   83, 1428, 1432, 1436
    } ;

static const flex_int16_t yy_def[259] =
    {   0,
      250,    1,  251,  251,  250,  250,  250,  250,  250,  250,
      252,  250,  253,  253,  254,  253,  250,  250,  255,  255,
      255,  255,  255,  255,  255,  255,  255,  255,  255,  255,
      255,  255,  255,  255,  255,  255,  255,  255,  253,  250,
      250,  250,  250,  250,  252,  250,  256,  253,  254,  253,
      250,  254,  254,  253,  250,  250,  250,  255,  255,  255,
      255,  255,  255,  255,  255,  255,  255,  255,  255,  255,
      255,  255,  255,  255,  255,  255,  255,  255,  255,  255,
      255,  255,  255,  255,  255,  255,  255,  255,  255,  255,
      255,  255,  255,  255,  255,  250,  257,  256,  258,  253,

      255,  255,  255,  255,  255,  255,  255,  255,  255,  255,
      255,  255,  255,  255,  255,  255,  255,  255,  255,  255,
      255,  255,  255,  255,  255,  255,  255,  255,  255,  255,
      255,  255,  255,  255,  255,  255,  255,  255,  255,  255,
      255,  255,  255,  258,  144,  255,  255,  255,  255,  255,
      255,  255,  255,  255,  255,  255,  255,  255,  255,  255,
      255,  255,  255,  255,  255,  255,  255,  255,  255,  255,
      255,  255,  255,  255,  255,  255,  255,  255,  255,  255,
      255,  255,  255,  255,  255,  255,  255,  255,  255,  255,
      255,  255,  255,  255,  255,  255,  255,  255,  255,  255,

      255,  255,  255,  255,  255,  255,  255,  255,  255,  255,
      255,  255,  255,  255,  255,  255,  255,  255,  255,  255,
      255,  255,  255,  255,  255,  255,  255,  255,  255,  255,
      255,  255,  255,  255,  255,  255,  255,  255,  255,  255,
      255,  255,  255,  255,  255,  255,  255,  255,  255,    0,
      250,  250,  250,  250,  250,  250,  250,  250
    } ;

static const flex_int16_t yy_nxt[1511] =
    {   0,
        6,    7,    8,    9,   10,   11,   12,   12,   12,   12,
       12,   13,   14,   15,   16,   12,   17,   12,   18,   19,
       20,   21,   22,   23,   24,   25,   26,   27,   28,   29,
       30,   31,   29,   32,   29,   29,   33,   34,   35,   36,
       37,   38,   29,   29,   39,   19,   20,   21,   22,   23,
       24,   25,   26,   27,   28,   29,   30,   31,   29,   32,
       29,   33,   34,   35,   36,   37,   38,   29,   29,   47,
       51,   49,   50,   55,   56,   53,   54,   49,   50,   48,
       48,   49,  250,   48,   48,   49,   58,   53,   59,  250,
       48,   48,   48,   49,   53,   63,  250,   48,   48,   49,

       60,   53,   48,   48,   49,   61,   65,   49,   62,   68,
       48,   48,   49,   66,   59,   64,   67,   48,   48,   49,
       46,   63,   69,   48,   48,   49,   60,   75,   49,  100,
       61,   42,   65,   62,   96,   68,   49,  100,   70,   66,
       64,   67,   48,   48,   49,   79,   49,   69,   71,   57,
       72,   49,   75,   48,   48,   49,   46,   48,   48,   49,
       44,   73,   43,   42,   70,   76,  250,   74,   41,   77,
       41,   79,  250,   71,   78,  250,   72,   48,   48,   49,
       48,   48,   49,   48,   48,   49,  250,   73,   82,  250,
      250,   76,   74,   80,  250,   77,   83,  250,  250,   81,

       78,   48,   48,   49,   84,   48,   48,   49,   85,   48,
       48,   49,  250,  250,   82,  250,  250,   91,  250,   80,
      250,  250,   83,   86,  250,   81,  250,  250,   93,  250,
       84,  250,  250,   85,   92,  250,   48,   48,   49,  250,
       48,   48,   49,   91,   94,   54,   49,   50,  250,   86,
       48,   48,   49,  250,   93,   95,   48,   48,   49,   92,
      250,  250,   87,  250,  250,   88,  250,   48,   48,   49,
       94,   48,   48,   49,   48,   48,   49,   89,   90,  250,
      250,   95,   48,   48,   49,   48,   48,   49,   87,  101,
      250,   88,  104,  250,   48,   48,   49,  250,   48,   48,

       49,  250,   89,   90,   97,   97,  107,   97,   97,   97,
       97,   97,   97,   97,   97,  101,  113,   99,  104,   97,
       97,   97,   97,   48,   48,   49,   48,   48,   49,   48,
       48,   49,  107,  250,  102,   48,   48,   49,  250,  250,
      105,  250,  113,  106,   48,   48,   49,   48,   48,   49,
      103,   48,   48,   49,  250,  108,  110,   48,   48,   49,
      102,  250,  250,  109,  250,  114,  105,  127,  250,  106,
      111,   48,   48,   49,  250,  103,  250,  112,   48,   48,
       49,  108,  110,   48,   48,   49,  250,  115,  109,  250,
      250,  114,  250,  127,  116,  250,  111,  117,  250,   48,

       48,   49,  112,  250,  250,  118,   48,   48,   49,   48,
       48,   49,  250,  115,   48,   48,   49,   48,   48,   49,
      116,  119,  250,  117,   48,   48,   49,  250,  120,  250,
      250,  118,  250,  122,  250,  123,  250,  250,  121,   48,
       48,   49,  250,  250,   48,   48,   49,  119,  129,  250,
      124,  125,  128,  250,  120,  126,   48,   48,   49,  122,
      250,  123,  250,  121,   48,   48,   49,  250,   48,   48,
       49,  250,  250,  129,  250,  124,  125,  130,  128,  131,
      250,  126,  250,  132,   48,   48,   49,   48,   48,   49,
       48,   48,   49,   48,   48,   49,  250,  250,   48,   48,

       49,  137,  250,  130,  138,  131,  133,  134,  250,  132,
      250,  250,  136,  250,  135,   48,   48,   49,   48,   48,
       49,   48,   48,   49,  139,  250,  140,  137,  250,  141,
      138,  250,  133,  134,   48,   48,   49,  250,  136,  135,
      142,  250,   48,   48,   49,  250,  143,   48,   48,   49,
      139,  140,   48,   48,   49,  141,  250,  250,  147,   48,
       48,   49,   48,   48,   49,  250,  142,  250,  156,   48,
       48,   49,  143,   97,   97,  250,   97,   97,   97,   97,
       97,   97,   97,   97,  147,  148,   99,  146,   97,   97,
       97,   97,   97,   97,  156,   97,   97,   97,   97,   97,

       97,   97,   97,  250,  250,  145,  250,   97,   97,   97,
       97,  148,  146,   48,   48,   49,   48,   48,   49,   48,
       48,   49,   48,   48,   49,  250,  250,   48,   48,   49,
      152,   48,   48,   49,  250,  250,  150,  250,  149,  153,
      151,  154,   48,   48,   49,   48,   48,   49,   48,   48,
       49,   48,   48,   49,  250,  250,  152,   48,   48,   49,
      250,  158,  150,  149,  250,  153,  151,  154,  155,  250,
       48,   48,   49,  250,  250,  157,  250,  159,  160,   48,
       48,   49,   48,   48,   49,  162,  250,  158,   48,   48,
       49,   48,   48,   49,  155,   48,   48,   49,  163,  161,

      157,  250,  159,  165,  160,  250,  250,  166,  250,  250,
      162,  164,   48,   48,   49,   48,   48,   49,  250,   48,
       48,   49,  250,  250,  163,  161,   48,   48,   49,  165,
       48,   48,   49,  166,  167,  169,  168,  164,   48,   48,
       49,  170,   48,   48,   49,   48,   48,   49,   48,   48,
       49,   48,   48,   49,  171,   48,   48,   49,  250,  250,
      167,  169,  168,  173,  172,  250,  250,  170,   48,   48,
       49,  174,   48,   48,   49,   48,   48,   49,  250,  250,
      171,   48,   48,   49,   48,   48,   49,  250,  250,  173,
      172,  250,  250,   48,   48,   49,  178,  174,  175,  176,

      177,  179,   48,   48,   49,  250,   48,   48,   49,   48,
       48,   49,   48,   48,   49,  250,   48,   48,   49,   48,
       48,   49,  178,  175,  176,  250,  177,  179,  250,  250,
      180,  181,  250,  183,   48,   48,   49,   48,   48,   49,
       48,   48,   49,  182,   48,   48,   49,   48,   48,   49,
      184,   48,   48,   49,  250,  180,  181,   97,   97,  183,
       97,   97,   97,   97,   97,   97,   97,   97,  182,  250,
      145,  185,   97,   97,   97,   97,  184,  250,  186,   48,
       48,   49,   48,   48,   49,   48,   48,   49,   48,   48,
       49,  250,  250,  189,  250,  250,  185,   48,   48,   49,

      190,  188,  250,  186,  250,  250,  187,   48,   48,   49,
       48,   48,   49,   48,   48,   49,   48,   48,   49,  189,
       48,   48,   49,  250,  191,  250,  190,  188,   48,   48,
       49,  187,  250,  192,  193,   48,   48,   49,   48,   48,
       49,   48,   48,   49,   48,   48,   49,  250,  250,  191,
      250,  194,  250,  195,   48,   48,   49,  250,  250,  192,
      193,   48,   48,   49,   48,   48,   49,  250,  196,  250,
      198,   48,   48,   49,  199,  250,  194,  250,  195,  197,
       48,   48,   49,   48,   48,   49,   48,   48,   49,   48,
       48,   49,  250,  196,  250,  250,  198,  250,  201,  200,

      199,   48,   48,   49,  197,   48,   48,   49,   48,   48,
       49,   48,   48,   49,   48,   48,   49,  203,  250,  250,
      204,   48,   48,   49,  201,  200,  250,  250,  202,   48,
       48,   49,   48,   48,   49,  250,  250,   48,   48,   49,
       48,   48,   49,  203,  206,  250,  204,  250,  205,  207,
      250,  250,  208,  202,  250,   48,   48,   49,   48,   48,
       49,   48,   48,   49,   48,   48,   49,   48,   48,   49,
      206,  209,  250,  205,  250,  207,  250,  250,  208,  250,
      250,  210,   48,   48,   49,   48,   48,   49,   48,   48,
       49,   48,   48,   49,   48,   48,   49,  209,   48,   48,

       49,  250,  212,   48,   48,   49,  250,  210,   48,   48,
       49,  250,  211,  214,   48,   48,   49,   48,   48,   49,
       48,   48,   49,  250,  250,  213,  216,  250,  212,   48,
       48,   49,  250,  250,  215,  250,  250,  211,  250,  214,
       48,   48,   49,  217,   48,   48,   49,   48,   48,   49,
      213,  250,  216,   48,   48,   49,   48,   48,   49,  215,
       48,   48,   49,   48,   48,   49,  250,  250,  217,   48,
       48,   49,  250,  220,  219,   48,   48,   49,  250,  250,
      218,   48,   48,   49,   48,   48,   49,   48,   48,   49,
       48,   48,   49,   48,   48,   49,   48,   48,   49,  220,

      219,  250,   48,   48,   49,   48,   48,   49,  221,  250,
      250,  222,  225,  250,  224,  250,  223,  226,  250,  250,
       48,   48,   49,   48,   48,   49,   48,   48,   49,   48,
       48,   49,   48,   48,   49,  250,  250,  222,  225,  250,
      224,  223,  227,  226,  228,  250,   48,   48,   49,   48,
       48,   49,  229,   48,   48,   49,  250,  230,  231,   48,
       48,   49,   48,   48,   49,   48,   48,   49,  227,  234,
      228,   48,   48,   49,  250,  250,  232,  229,  250,  250,
      233,  235,  230,  250,  231,  236,   48,   48,   49,  237,
       48,   48,   49,  250,  250,  234,   48,   48,   49,  250,

      250,  232,  239,  250,  250,  233,  250,  235,  238,  250,
      250,  236,   48,   48,   49,  237,   48,   48,   49,  240,
       48,   48,   49,   48,   48,   49,  250,  250,  239,   48,
       48,   49,  250,  250,  238,  250,  250,  242,  241,   48,
       48,   49,  243,  245,  250,  240,  244,   48,   48,   49,
      250,   48,   48,   49,  250,  246,   48,   48,   49,   48,
       48,   49,  242,  247,  241,   48,   48,   49,  243,  245,
      250,  250,  244,   48,   48,   49,  250,  248,  250,  250,
      250,  246,  250,  250,  250,  250,  250,  250,  250,  247,
      250,  250,  249,  250,  250,  250,  250,  250,  250,  250,

      250,  250,  250,  248,  250,  250,  250,  250,  250,  250,
      250,  250,  250,  250,  250,  250,  250,  249,   40,   40,
       40,   40,   45,   45,   45,   45,   52,   52,   98,  250,
       98,   98,   97,  250,   97,   97,  144,  250,  144,  144,
        5,  250,  250,  250,  250,  250,  250,  250,  250,  250,
      250,  250,  250,  250,  250,  250,  250,  250,  250,  250,
      250,  250,  250,  250,  250,  250,  250,  250,  250,  250,
      250,  250,  250,  250,  250,  250,  250,  250,  250,  250,
      250,  250,  250,  250,  250,  250,  250,  250,  250,  250,
      250,  250,  250,  250,  250,  250,  250,  250,  250,  250,

      250,  250,  250,  250,  250,  250,  250,  250,  250,  250
    } ;

static const flex_int16_t yy_chk[1511] =
    {   0,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,   13,
       15,   13,   13,   17,   17,   15,   16,   16,   16,   19,
       19,   19,   49,   20,   20,   20,  255,   49,   19,   52,
      253,   21,   21,   21,   52,   20,   53,   22,   22,   22,

       19,   53,   25,   25,   25,   19,   21,   48,   19,   22,
       29,   29,   29,   21,   19,   20,   21,   23,   23,   23,
       45,   20,   22,   28,   28,   28,   19,   25,   54,   54,
       19,   42,   21,   19,   41,   22,  100,  100,   23,   21,
       20,   21,   24,   24,   24,   28,   39,   22,   23,   18,
       24,   14,   25,   27,   27,   27,   11,   26,   26,   26,
       10,   24,    9,    7,   23,   26,    5,   24,    4,   26,
        3,   28,    0,   23,   27,    0,   24,   30,   30,   30,
       31,   31,   31,   32,   32,   32,    0,   24,   31,    0,
        0,   26,   24,   30,    0,   26,   31,    0,    0,   30,

       27,   33,   33,   33,   32,   36,   36,   36,   32,   35,
       35,   35,    0,    0,   31,    0,    0,   35,    0,   30,
        0,    0,   31,   33,    0,   30,    0,    0,   36,    0,
       32,    0,    0,   32,   35,    0,   37,   37,   37,    0,
       38,   38,   38,   35,   37,   50,   50,   50,    0,   33,
       34,   34,   34,    0,   36,   38,   58,   58,   58,   35,
        0,    0,   34,    0,    0,   34,    0,   59,   59,   59,
       37,   64,   64,   64,   84,   84,   84,   34,   34,    0,
        0,   38,   61,   61,   61,  102,  102,  102,   34,   59,
        0,   34,   61,    0,   69,   69,   69,    0,   65,   65,

       65,    0,   34,   34,   47,   47,   65,   47,   47,   47,
       47,   47,   47,   47,   47,   59,   69,   47,   61,   47,
       47,   47,   47,   60,   60,   60,   62,   62,   62,   63,
       63,   63,   65,    0,   60,   66,   66,   66,    0,    0,
       62,    0,   69,   63,   67,   67,   67,   80,   80,   80,
       60,   68,   68,   68,    0,   66,   67,   70,   70,   70,
       60,    0,    0,   66,    0,   70,   62,   80,    0,   63,
       68,   71,   71,   71,    0,   60,    0,   68,   72,   72,
       72,   66,   67,   73,   73,   73,    0,   71,   66,    0,
        0,   70,    0,   80,   71,    0,   68,   72,    0,   74,

       74,   74,   68,    0,    0,   73,   75,   75,   75,   76,
       76,   76,    0,   71,   77,   77,   77,   82,   82,   82,
       71,   74,    0,   72,   78,   78,   78,    0,   75,    0,
        0,   73,    0,   77,    0,   78,    0,    0,   76,   79,
       79,   79,    0,    0,   81,   81,   81,   74,   82,    0,
       78,   78,   81,    0,   75,   79,   83,   83,   83,   77,
        0,   78,    0,   76,   86,   86,   86,    0,   85,   85,
       85,    0,    0,   82,    0,   78,   78,   83,   81,   85,
        0,   79,    0,   86,   90,   90,   90,   87,   87,   87,
       88,   88,   88,   89,   89,   89,    0,    0,   92,   92,

       92,   89,    0,   83,   90,   85,   87,   87,    0,   86,
        0,    0,   88,    0,   87,   91,   91,   91,   93,   93,
       93,   94,   94,   94,   91,    0,   92,   89,    0,   93,
       90,    0,   87,   87,   95,   95,   95,    0,   88,   87,
       94,    0,  103,  103,  103,    0,   95,  104,  104,  104,
       91,   92,  105,  105,  105,   93,    0,    0,  103,  114,
      114,  114,  101,  101,  101,    0,   94,    0,  114,  106,
      106,  106,   95,   98,   98,    0,   98,   98,   98,   98,
       98,   98,   98,   98,  103,  106,   98,  101,   98,   98,
       98,   98,   99,   99,  114,   99,   99,   99,   99,   99,

       99,   99,   99,    0,    0,   99,    0,   99,   99,   99,
       99,  106,  101,  107,  107,  107,  108,  108,  108,  109,
      109,  109,  110,  110,  110,    0,    0,  111,  111,  111,
      110,  112,  112,  112,    0,    0,  108,    0,  107,  111,
      109,  112,  116,  116,  116,  113,  113,  113,  115,  115,
      115,  117,  117,  117,    0,    0,  110,  120,  120,  120,
        0,  116,  108,  107,    0,  111,  109,  112,  113,    0,
      118,  118,  118,    0,    0,  115,    0,  117,  118,  119,
      119,  119,  121,  121,  121,  120,    0,  116,  122,  122,
      122,  123,  123,  123,  113,  124,  124,  124,  121,  119,

      115,    0,  117,  123,  118,    0,    0,  124,    0,    0,
      120,  122,  125,  125,  125,  126,  126,  126,    0,  127,
      127,  127,    0,    0,  121,  119,  129,  129,  129,  123,
      128,  128,  128,  124,  125,  127,  126,  122,  130,  130,
      130,  128,  131,  131,  131,  132,  132,  132,  135,  135,
      135,  133,  133,  133,  131,  134,  134,  134,    0,    0,
      125,  127,  126,  133,  132,    0,    0,  128,  136,  136,
      136,  134,  137,  137,  137,  138,  138,  138,    0,    0,
      131,  139,  139,  139,  140,  140,  140,    0,    0,  133,
      132,    0,    0,  141,  141,  141,  140,  134,  136,  137,

      139,  141,  142,  142,  142,    0,  143,  143,  143,  147,
      147,  147,  148,  148,  148,    0,  146,  146,  146,  149,
      149,  149,  140,  136,  137,    0,  139,  141,    0,    0,
      142,  143,    0,  148,  150,  150,  150,  154,  154,  154,
      155,  155,  155,  146,  151,  151,  151,  157,  157,  157,
      150,  152,  152,  152,    0,  142,  143,  144,  144,  148,
      144,  144,  144,  144,  144,  144,  144,  144,  146,    0,
      144,  151,  144,  144,  144,  144,  150,    0,  152,  153,
      153,  153,  156,  156,  156,  158,  158,  158,  159,  159,
      159,    0,    0,  158,    0,    0,  151,  160,  160,  160,

      159,  156,    0,  152,    0,    0,  153,  161,  161,  161,
      162,  162,  162,  163,  163,  163,  164,  164,  164,  158,
      165,  165,  165,    0,  160,    0,  159,  156,  166,  166,
      166,  153,    0,  162,  163,  167,  167,  167,  168,  168,
      168,  169,  169,  169,  170,  170,  170,    0,    0,  160,
        0,  165,    0,  166,  171,  171,  171,    0,    0,  162,
      163,  172,  172,  172,  173,  173,  173,    0,  169,    0,
      172,  174,  174,  174,  173,    0,  165,    0,  166,  171,
      175,  175,  175,  176,  176,  176,  177,  177,  177,  178,
      178,  178,    0,  169,    0,    0,  172,    0,  177,  176,

      173,  179,  179,  179,  171,  180,  180,  180,  181,  181,
      181,  182,  182,  182,  183,  183,  183,  180,    0,    0,
      181,  184,  184,  184,  177,  176,    0,    0,  179,  185,
      185,  185,  186,  186,  186,    0,    0,  187,  187,  187,
      188,  188,  188,  180,  186,    0,  181,    0,  184,  187,
        0,    0,  188,  179,    0,  189,  189,  189,  190,  190,
      190,  191,  191,  191,  192,  192,  192,  193,  193,  193,
      186,  189,    0,  184,    0,  187,    0,    0,  188,    0,
        0,  193,  194,  194,  194,  195,  195,  195,  196,  196,
      196,  197,  197,  197,  198,  198,  198,  189,  199,  199,

      199,    0,  198,  200,  200,  200,    0,  193,  201,  201,
      201,    0,  195,  200,  202,  202,  202,  203,  203,  203,
      204,  204,  204,    0,    0,  199,  202,    0,  198,  205,
      205,  205,    0,    0,  201,    0,    0,  195,    0,  200,
      206,  206,  206,  203,  207,  207,  207,  208,  208,  208,
      199,    0,  202,  209,  209,  209,  210,  210,  210,  201,
      211,  211,  211,  212,  212,  212,    0,    0,  203,  213,
      213,  213,    0,  212,  209,  214,  214,  214,    0,    0,
      208,  215,  215,  215,  216,  216,  216,  217,  217,  217,
      218,  218,  218,  219,  219,  219,  220,  220,  220,  212,

      209,    0,  221,  221,  221,  222,  222,  222,  214,    0,
        0,  218,  221,    0,  220,    0,  218,  222,    0,    0,
      223,  223,  223,  224,  224,  224,  226,  226,  226,  225,
      225,  225,  227,  227,  227,    0,    0,  218,  221,    0,
      220,  218,  223,  222,  225,    0,  228,  228,  228,  229,
      229,  229,  226,  230,  230,  230,    0,  227,  228,  231,
      231,  231,  232,  232,  232,  233,  233,  233,  223,  231,
      225,  234,  234,  234,    0,    0,  229,  226,    0,    0,
      230,  232,  227,    0,  228,  233,  235,  235,  235,  234,
      236,  236,  236,    0,    0,  231,  237,  237,  237,    0,

        0,  229,  236,    0,    0,  230,    0,  232,  235,    0,
        0,  233,  239,  239,  239,  234,  238,  238,  238,  237,
      240,  240,  240,  241,  241,  241,    0,    0,  236,  242,
      242,  242,    0,    0,  235,    0,    0,  239,  238,  243,
      243,  243,  240,  242,    0,  237,  241,  244,  244,  244,
        0,  245,  245,  245,    0,  243,  246,  246,  246,  247,
      247,  247,  239,  245,  238,  248,  248,  248,  240,  242,
        0,    0,  241,  249,  249,  249,    0,  246,    0,    0,
        0,  243,    0,    0,    0,    0,    0,    0,    0,  245,
        0,    0,  248,    0,    0,    0,    0,    0,    0,    0,

        0,    0,    0,  246,    0,    0,    0,    0,    0,    0,
        0,    0,    0,    0,    0,    0,    0,  248,  251,  251,
      251,  251,  252,  252,  252,  252,  254,  254,  256,    0,
      256,  256,  257,    0,  257,  257,  258,    0,  258,  258,
      250,  250,  250,  250,  250,  250,  250,  250,  250,  250,
      250,  250,  250,  250,  250,  250,  250,  250,  250,  250,
      250,  250,  250,  250,  250,  250,  250,  250,  250,  250,
      250,  250,  250,  250,  250,  250,  250,  250,  250,  250,
      250,  250,  250,  250,  250,  250,  250,  250,  250,  250,
      250,  250,  250,  250,  250,  250,  250,  250,  250,  250,

      250,  250,  250,  250,  250,  250,  250,  250,  250,  250
    } ;

static yy_state_type yy_last_accepting_state;
static char *yy_last_accepting_cpos;

extern int yy_flex_debug;
int yy_flex_debug = 0;

/* The intent behind this definition is that it'll catch
 * any uses of REJECT which flex missed.
 */
#define REJECT reject_used_but_not_detected
#define yymore() yymore_used_but_not_detected
#define YY_MORE_ADJ 0
#define YY_RESTORE_YY_MORE_OFFSET
char *yytext;
#line 1 "lex.l"
#line 2 "lex.l"
    /* keywords are case insensitive */
    /* we don't need yywrap() function */
    /* we don't need yyunput() function */
    /* we don't need input() function */
#define YY_NO_INPUT 1
    /* enable location */
#include "ast.h"
#include "yacc.tab.h"
#include <iostream>

// automatically update location
#define YY_USER_ACTION \
    yylloc->first_line = yylloc->last_line; \
    yylloc->first_column = yylloc->last_column; \
    for (int i = 0; yytext[i] != '\0'; i++) { \
        if(yytext[i] == '\n') { \
            yylloc->last_line++; \
            yylloc->last_column = 1; \
        } else { \
            yylloc->last_column++; \
        } \
    }

#line 909 "/root/dbms3.1/db2025-x1/src/parser/lex.yy.cpp"

#line 911 "/root/dbms3.1/db2025-x1/src/parser/lex.yy.cpp"

#define INITIAL 0
#define STATE_COMMENT 1

#ifndef YY_NO_UNISTD_H
/* Special case for "unistd.h", since it is non-ANSI. We include it way
 * down here because we want the user's section 1 to have been scanned first.
 * The user has a chance to override it with an option.
 */
#include <unistd.h>
#endif

#ifndef YY_EXTRA_TYPE
#define YY_EXTRA_TYPE void *
#endif

static int yy_init_globals ( void );

/* Accessor methods to globals.
   These are made visible to non-reentrant scanners for convenience. */

int yylex_destroy ( void );

int yyget_debug ( void );

void yyset_debug ( int debug_flag  );

YY_EXTRA_TYPE yyget_extra ( void );

void yyset_extra ( YY_EXTRA_TYPE user_defined  );

FILE *yyget_in ( void );

void yyset_in  ( FILE * _in_str  );

FILE *yyget_out ( void );

void yyset_out  ( FILE * _out_str  );

			int yyget_leng ( void );

char *yyget_text ( void );

int yyget_lineno ( void );

void yyset_lineno ( int _line_number  );

YYSTYPE * yyget_lval ( void );

void yyset_lval ( YYSTYPE * yylval_param  );

       YYLTYPE *yyget_lloc ( void );
    
        void yyset_lloc ( YYLTYPE * yylloc_param  );
    
/* Macros after this point can all be overridden by user definitions in
 * section 1.
 */

#ifndef YY_SKIP_YYWRAP
#ifdef __cplusplus
extern "C" int yywrap ( void );
#else
extern int yywrap ( void );
#endif
#endif

#ifndef YY_NO_UNPUT
    
#endif

#ifndef yytext_ptr
static void yy_flex_strncpy ( char *, const char *, int );
#endif

#ifdef YY_NEED_STRLEN
static int yy_flex_strlen ( const char * );
#endif

#ifndef YY_NO_INPUT
#ifdef __cplusplus
static int yyinput ( void );
#else
static int input ( void );
#endif

#endif

/* Amount of stuff to slurp up with each read. */
#ifndef YY_READ_BUF_SIZE
#ifdef __ia64__
/* On IA-64, the buffer size is 16k, not 8k */
#define YY_READ_BUF_SIZE 16384
#else
#define YY_READ_BUF_SIZE 8192
#endif /* __ia64__ */
#endif

/* Copy whatever the last rule matched to the standard output. */
#ifndef ECHO
/* This used to be an fputs(), but since the string might contain NUL's,
 * we now use fwrite().
 */
#define ECHO do { if (fwrite( yytext, (size_t) yyleng, 1, yyout )) {} } while (0)
#endif

/* Gets input and stuffs it into "buf".  number of characters read, or YY_NULL,
 * is returned in "result".
 */
#ifndef YY_INPUT
#define YY_INPUT(buf,result,max_size) \
	if ( YY_CURRENT_BUFFER_LVALUE->yy_is_interactive ) \
		{ \
		int c = '*'; \
		int n; \
		for ( n = 0; n < max_size && \
			     (c = getc( yyin )) != EOF && c != '\n'; ++n ) \
			buf[n] = (char) c; \
		if ( c == '\n' ) \
			buf[n++] = (char) c; \
		if ( c == EOF && ferror( yyin ) ) \
			YY_FATAL_ERROR( "input in flex scanner failed" ); \
		result = n; \
		} \
	else \
		{ \
		errno=0; \
		while ( (result = (int) fread(buf, 1, (yy_size_t) max_size, yyin)) == 0 && ferror(yyin)) \
			{ \
			if( errno != EINTR) \
				{ \
				YY_FATAL_ERROR( "input in flex scanner failed" ); \
				break; \
				} \
			errno=0; \
			clearerr(yyin); \
			} \
		}\
\

#endif

/* No semi-colon after return; correct usage is to write "yyterminate();" -
 * we don't want an extra ';' after the "return" because that will cause
 * some compilers to complain about unreachable statements.
 */
#ifndef yyterminate
#define yyterminate() return YY_NULL
#endif

/* Number of entries by which start-condition stack grows. */
#ifndef YY_START_STACK_INCR
#define YY_START_STACK_INCR 25
#endif

/* Report a fatal error. */
#ifndef YY_FATAL_ERROR
#define YY_FATAL_ERROR(msg) yy_fatal_error( msg )
#endif

/* end tables serialization structures and prototypes */

/* Default declaration of generated scanner - a define so the user can
 * easily add parameters.
 */
#ifndef YY_DECL
#define YY_DECL_IS_OURS 1

extern int yylex \
               (YYSTYPE * yylval_param, YYLTYPE * yylloc_param );

#define YY_DECL int yylex \
               (YYSTYPE * yylval_param, YYLTYPE * yylloc_param )
#endif /* !YY_DECL */

/* Code executed at the beginning of each rule, after yytext and yyleng
 * have been set up.
 */
#ifndef YY_USER_ACTION
#define YY_USER_ACTION
#endif

/* Code executed at the end of each rule. */
#ifndef YY_BREAK
#define YY_BREAK /*LINTED*/break;
#endif

#define YY_RULE_SETUP \
	YY_USER_ACTION

/** The main scanner function which does all the work.
 */
YY_DECL
{
	yy_state_type yy_current_state;
	char *yy_cp, *yy_bp;
	int yy_act;
    
        YYSTYPE * yylval;
    
        YYLTYPE * yylloc;
    
    yylval = yylval_param;

    yylloc = yylloc_param;

	if ( !(yy_init) )
		{
		(yy_init) = 1;

#ifdef YY_USER_INIT
		YY_USER_INIT;
#endif

		if ( ! (yy_start) )
			(yy_start) = 1;	/* first start state */

		if ( ! yyin )
			yyin = stdin;

		if ( ! yyout )
			yyout = stdout;

		if ( ! YY_CURRENT_BUFFER ) {
			yyensure_buffer_stack ();
			YY_CURRENT_BUFFER_LVALUE =
				yy_create_buffer( yyin, YY_BUF_SIZE );
		}

		yy_load_buffer_state(  );
		}

	{
#line 47 "lex.l"

#line 49 "lex.l"
    /* block comment */
#line 1149 "/root/dbms3.1/db2025-x1/src/parser/lex.yy.cpp"

	while ( /*CONSTCOND*/1 )		/* loops until end-of-file is reached */
		{
		yy_cp = (yy_c_buf_p);

		/* Support of yytext. */
		*yy_cp = (yy_hold_char);

		/* yy_bp points to the position in yy_ch_buf of the start of
		 * the current run.
		 */
		yy_bp = yy_cp;

		yy_current_state = (yy_start);
yy_match:
		do
			{
			YY_CHAR yy_c = yy_ec[YY_SC_TO_UI(*yy_cp)] ;
			if ( yy_accept[yy_current_state] )
				{
				(yy_last_accepting_state) = yy_current_state;
				(yy_last_accepting_cpos) = yy_cp;
				}
			while ( yy_chk[yy_base[yy_current_state] + yy_c] != yy_current_state )
				{
				yy_current_state = (int) yy_def[yy_current_state];
				if ( yy_current_state >= 251 )
					yy_c = yy_meta[yy_c];
				}
			yy_current_state = yy_nxt[yy_base[yy_current_state] + yy_c];
			++yy_cp;
			}
		while ( yy_base[yy_current_state] != 1441 );

yy_find_action:
		yy_act = yy_accept[yy_current_state];
		if ( yy_act == 0 )
			{ /* have to back up */
			yy_cp = (yy_last_accepting_cpos);
			yy_current_state = (yy_last_accepting_state);
			yy_act = yy_accept[yy_current_state];
			}

		YY_DO_BEFORE_ACTION;

do_action:	/* This label is used only to access EOF actions. */

		switch ( yy_act )
	{ /* beginning of action switch */
			case 0: /* must back up */
			/* undo the effects of YY_DO_BEFORE_ACTION */
			*yy_cp = (yy_hold_char);
			yy_cp = (yy_last_accepting_cpos);
			yy_current_state = (yy_last_accepting_state);
			goto yy_find_action;

case 1:
YY_RULE_SETUP
#line 50 "lex.l"
{ BEGIN(STATE_COMMENT); }
	YY_BREAK
case 2:
YY_RULE_SETUP
#line 51 "lex.l"
{ BEGIN(INITIAL); }
	YY_BREAK
case 3:
/* rule 3 can match eol */
YY_RULE_SETUP
#line 52 "lex.l"
{ /* ignore the text of the comment */ }
	YY_BREAK
case 4:
YY_RULE_SETUP
#line 53 "lex.l"
{ /* ignore *'s that aren't part of */ }
	YY_BREAK
/* single line comment */
case 5:
YY_RULE_SETUP
#line 55 "lex.l"
{ /* ignore single line comment */ }
	YY_BREAK
/* white space and new line */
case 6:
YY_RULE_SETUP
#line 57 "lex.l"
{ /* ignore white space */ }
	YY_BREAK
case 7:
/* rule 7 can match eol */
YY_RULE_SETUP
#line 58 "lex.l"
{ /* ignore new line */ }
	YY_BREAK
/* keywords */
case 8:
YY_RULE_SETUP
#line 60 "lex.l"
{ return SHOW; }
	YY_BREAK
case 9:
YY_RULE_SETUP
#line 61 "lex.l"
{ return TXN_BEGIN; }
	YY_BREAK
case 10:
YY_RULE_SETUP
#line 62 "lex.l"
{ return TXN_COMMIT; }
	YY_BREAK
case 11:
YY_RULE_SETUP
#line 63 "lex.l"
{ return TXN_ABORT; }
	YY_BREAK
case 12:
YY_RULE_SETUP
#line 64 "lex.l"
{ return TXN_ROLLBACK; }
	YY_BREAK
case 13:
YY_RULE_SETUP
#line 65 "lex.l"
{ return TABLES; }
	YY_BREAK
case 14:
YY_RULE_SETUP
#line 66 "lex.l"
{ return CREATE; }
	YY_BREAK
case 15:
YY_RULE_SETUP
#line 67 "lex.l"
{ return TABLE; }
	YY_BREAK
case 16:
YY_RULE_SETUP
#line 68 "lex.l"
{ return DROP; }
	YY_BREAK
case 17:
YY_RULE_SETUP
#line 69 "lex.l"
{ return DESC; }
	YY_BREAK
case 18:
YY_RULE_SETUP
#line 70 "lex.l"
{ return INSERT; }
	YY_BREAK
case 19:
YY_RULE_SETUP
#line 71 "lex.l"
{ return INTO; }
	YY_BREAK
case 20:
YY_RULE_SETUP
#line 72 "lex.l"
{ return VALUES; }
	YY_BREAK
case 21:
YY_RULE_SETUP
#line 73 "lex.l"
{ return DELETE; }
	YY_BREAK
case 22:
YY_RULE_SETUP
#line 74 "lex.l"
{ return FROM; }
	YY_BREAK
case 23:
YY_RULE_SETUP
#line 75 "lex.l"
{ return WHERE; }
	YY_BREAK
case 24:
YY_RULE_SETUP
#line 76 "lex.l"
{ return UPDATE; }
	YY_BREAK
case 25:
YY_RULE_SETUP
#line 77 "lex.l"
{ return SET; }
	YY_BREAK
case 26:
YY_RULE_SETUP
#line 78 "lex.l"
{ return SELECT; }
	YY_BREAK
case 27:
YY_RULE_SETUP
#line 79 "lex.l"
{ return INT; }
	YY_BREAK
case 28:
YY_RULE_SETUP
#line 80 "lex.l"
{ return CHAR; }
	YY_BREAK
case 29:
YY_RULE_SETUP
#line 81 "lex.l"
{ return FLOAT; }
	YY_BREAK
case 30:
YY_RULE_SETUP
#line 82 "lex.l"
{ return INDEX; }
	YY_BREAK
case 31:
YY_RULE_SETUP
#line 83 "lex.l"
{ return AND; }
	YY_BREAK
case 32:
YY_RULE_SETUP
#line 84 "lex.l"
{return JOIN;}
	YY_BREAK
case 33:
YY_RULE_SETUP
#line 85 "lex.l"
{ return EXIT; }
	YY_BREAK
case 34:
YY_RULE_SETUP
#line 86 "lex.l"
{ return HELP; }
	YY_BREAK
case 35:
YY_RULE_SETUP
#line 87 "lex.l"
{ return ORDER; }
	YY_BREAK
case 36:
YY_RULE_SETUP
#line 88 "lex.l"
{  return BY;  }
	YY_BREAK
case 37:
YY_RULE_SETUP
#line 89 "lex.l"
{ return ASC; }
	YY_BREAK
case 38:
YY_RULE_SETUP
#line 90 "lex.l"
{ return EXPLAIN; }
	YY_BREAK
case 39:
YY_RULE_SETUP
#line 91 "lex.l"
{ return COUNT; }
	YY_BREAK
case 40:
YY_RULE_SETUP
#line 92 "lex.l"
{ return MIN; }
	YY_BREAK
case 41:
YY_RULE_SETUP
#line 93 "lex.l"
{ return MAX; }
	YY_BREAK
case 42:
YY_RULE_SETUP
#line 94 "lex.l"
{ return AVG; }
	YY_BREAK
case 43:
YY_RULE_SETUP
#line 95 "lex.l"
{ return SUM; }
	YY_BREAK
case 44:
YY_RULE_SETUP
#line 96 "lex.l"
{ return LOAD; }
	YY_BREAK
case 45:
YY_RULE_SETUP
#line 97 "lex.l"
{ return GROUP; }
	YY_BREAK
case 46:
YY_RULE_SETUP
#line 98 "lex.l"
{ return HAVING; }
	YY_BREAK
case 47:
YY_RULE_SETUP
#line 99 "lex.l"
{ return AS; }
	YY_BREAK
case 48:
YY_RULE_SETUP
#line 100 "lex.l"
{ return LIMIT; }
	YY_BREAK
case 49:
YY_RULE_SETUP
#line 101 "lex.l"
{ return ENABLE_NESTLOOP; }
	YY_BREAK
case 50:
YY_RULE_SETUP
#line 102 "lex.l"
{ return ENABLE_SORTMERGE; }
	YY_BREAK
case 51:
YY_RULE_SETUP
#line 103 "lex.l"
{ return STATIC_CHECKPOINT; }
	YY_BREAK
case 52:
YY_RULE_SETUP
#line 104 "lex.l"
{ return AS; }
	YY_BREAK
case 53:
YY_RULE_SETUP
#line 105 "lex.l"
{ return ON; }
	YY_BREAK
case 54:
YY_RULE_SETUP
#line 106 "lex.l"
{ return SEMI; }
	YY_BREAK
case 55:
YY_RULE_SETUP
#line 107 "lex.l"
{ return ANTI; }
	YY_BREAK
case 56:
YY_RULE_SETUP
#line 108 "lex.l"
{ 
    yylval->sv_bool = true;
    return VALUE_BOOL; 
}
	YY_BREAK
case 57:
YY_RULE_SETUP
#line 112 "lex.l"
{
    yylval->sv_bool = false;
    return VALUE_BOOL;
}
	YY_BREAK
/* operators */
case 58:
YY_RULE_SETUP
#line 117 "lex.l"
{ return GEQ; }
	YY_BREAK
case 59:
YY_RULE_SETUP
#line 118 "lex.l"
{ return LEQ; }
	YY_BREAK
case 60:
YY_RULE_SETUP
#line 119 "lex.l"
{ return NEQ; }
	YY_BREAK
case 61:
YY_RULE_SETUP
#line 120 "lex.l"
{ return NEQ; }
	YY_BREAK
case 62:
YY_RULE_SETUP
#line 121 "lex.l"
{ return yytext[0]; }
	YY_BREAK
/* id */
case 63:
YY_RULE_SETUP
#line 123 "lex.l"
{
    yylval->sv_str = yytext;
    return IDENTIFIER;
}
	YY_BREAK
/* literals */
case 64:
YY_RULE_SETUP
#line 128 "lex.l"
{
    yylval->sv_int = atoi(yytext);
    return VALUE_INT;
}
	YY_BREAK
case 65:
YY_RULE_SETUP
#line 132 "lex.l"
{
    yylval->sv_float = atof(yytext);
    return VALUE_FLOAT;
}
	YY_BREAK
case 66:
/* rule 66 can match eol */
YY_RULE_SETUP
#line 136 "lex.l"
{
    yylval->sv_str = std::string(yytext + 1, strlen(yytext) - 2);
    return VALUE_STRING;
}
	YY_BREAK
case 67:
YY_RULE_SETUP
#line 141 "lex.l"
{
            yylval->sv_str = strdup(yytext);
            return PATH_TOKEN;
        }
	YY_BREAK
/* EOF */
case YY_STATE_EOF(INITIAL):
case YY_STATE_EOF(STATE_COMMENT):
#line 147 "lex.l"
{ return T_EOF; }
	YY_BREAK
/* unexpected char */
case 68:
YY_RULE_SETUP
#line 149 "lex.l"
{ std::cerr << "Lexer Error: unexpected character " << yytext[0] << std::endl; }
	YY_BREAK
case 69:
YY_RULE_SETUP
#line 150 "lex.l"
ECHO;
	YY_BREAK
#line 1588 "/root/dbms3.1/db2025-x1/src/parser/lex.yy.cpp"

	case YY_END_OF_BUFFER:
		{
		/* Amount of text matched not including the EOB char. */
		int yy_amount_of_matched_text = (int) (yy_cp - (yytext_ptr)) - 1;

		/* Undo the effects of YY_DO_BEFORE_ACTION. */
		*yy_cp = (yy_hold_char);
		YY_RESTORE_YY_MORE_OFFSET

		if ( YY_CURRENT_BUFFER_LVALUE->yy_buffer_status == YY_BUFFER_NEW )
			{
			/* We're scanning a new file or input source.  It's
			 * possible that this happened because the user
			 * just pointed yyin at a new source and called
			 * yylex().  If so, then we have to assure
			 * consistency between YY_CURRENT_BUFFER and our
			 * globals.  Here is the right place to do so, because
			 * this is the first action (other than possibly a
			 * back-up) that will match for the new input source.
			 */
			(yy_n_chars) = YY_CURRENT_BUFFER_LVALUE->yy_n_chars;
			YY_CURRENT_BUFFER_LVALUE->yy_input_file = yyin;
			YY_CURRENT_BUFFER_LVALUE->yy_buffer_status = YY_BUFFER_NORMAL;
			}

		/* Note that here we test for yy_c_buf_p "<=" to the position
		 * of the first EOB in the buffer, since yy_c_buf_p will
		 * already have been incremented past the NUL character
		 * (since all states make transitions on EOB to the
		 * end-of-buffer state).  Contrast this with the test
		 * in input().
		 */
		if ( (yy_c_buf_p) <= &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars)] )
			{ /* This was really a NUL. */
			yy_state_type yy_next_state;

			(yy_c_buf_p) = (yytext_ptr) + yy_amount_of_matched_text;

			yy_current_state = yy_get_previous_state(  );

			/* Okay, we're now positioned to make the NUL
			 * transition.  We couldn't have
			 * yy_get_previous_state() go ahead and do it
			 * for us because it doesn't know how to deal
			 * with the possibility of jamming (and we don't
			 * want to build jamming into it because then it
			 * will run more slowly).
			 */

			yy_next_state = yy_try_NUL_trans( yy_current_state );

			yy_bp = (yytext_ptr) + YY_MORE_ADJ;

			if ( yy_next_state )
				{
				/* Consume the NUL. */
				yy_cp = ++(yy_c_buf_p);
				yy_current_state = yy_next_state;
				goto yy_match;
				}

			else
				{
				yy_cp = (yy_c_buf_p);
				goto yy_find_action;
				}
			}

		else switch ( yy_get_next_buffer(  ) )
			{
			case EOB_ACT_END_OF_FILE:
				{
				(yy_did_buffer_switch_on_eof) = 0;

				if ( yywrap(  ) )
					{
					/* Note: because we've taken care in
					 * yy_get_next_buffer() to have set up
					 * yytext, we can now set up
					 * yy_c_buf_p so that if some total
					 * hoser (like flex itself) wants to
					 * call the scanner after we return the
					 * YY_NULL, it'll still work - another
					 * YY_NULL will get returned.
					 */
					(yy_c_buf_p) = (yytext_ptr) + YY_MORE_ADJ;

					yy_act = YY_STATE_EOF(YY_START);
					goto do_action;
					}

				else
					{
					if ( ! (yy_did_buffer_switch_on_eof) )
						YY_NEW_FILE;
					}
				break;
				}

			case EOB_ACT_CONTINUE_SCAN:
				(yy_c_buf_p) =
					(yytext_ptr) + yy_amount_of_matched_text;

				yy_current_state = yy_get_previous_state(  );

				yy_cp = (yy_c_buf_p);
				yy_bp = (yytext_ptr) + YY_MORE_ADJ;
				goto yy_match;

			case EOB_ACT_LAST_MATCH:
				(yy_c_buf_p) =
				&YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars)];

				yy_current_state = yy_get_previous_state(  );

				yy_cp = (yy_c_buf_p);
				yy_bp = (yytext_ptr) + YY_MORE_ADJ;
				goto yy_find_action;
			}
		break;
		}

	default:
		YY_FATAL_ERROR(
			"fatal flex scanner internal error--no action found" );
	} /* end of action switch */
		} /* end of scanning one token */
	} /* end of user's declarations */
} /* end of yylex */

/* yy_get_next_buffer - try to read in a new buffer
 *
 * Returns a code representing an action:
 *	EOB_ACT_LAST_MATCH -
 *	EOB_ACT_CONTINUE_SCAN - continue scanning from current position
 *	EOB_ACT_END_OF_FILE - end of file
 */
static int yy_get_next_buffer (void)
{
    	char *dest = YY_CURRENT_BUFFER_LVALUE->yy_ch_buf;
	char *source = (yytext_ptr);
	int number_to_move, i;
	int ret_val;

	if ( (yy_c_buf_p) > &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars) + 1] )
		YY_FATAL_ERROR(
		"fatal flex scanner internal error--end of buffer missed" );

	if ( YY_CURRENT_BUFFER_LVALUE->yy_fill_buffer == 0 )
		{ /* Don't try to fill the buffer, so this is an EOF. */
		if ( (yy_c_buf_p) - (yytext_ptr) - YY_MORE_ADJ == 1 )
			{
			/* We matched a single character, the EOB, so
			 * treat this as a final EOF.
			 */
			return EOB_ACT_END_OF_FILE;
			}

		else
			{
			/* We matched some text prior to the EOB, first
			 * process it.
			 */
			return EOB_ACT_LAST_MATCH;
			}
		}

	/* Try to read more data. */

	/* First move last chars to start of buffer. */
	number_to_move = (int) ((yy_c_buf_p) - (yytext_ptr) - 1);

	for ( i = 0; i < number_to_move; ++i )
		*(dest++) = *(source++);

	if ( YY_CURRENT_BUFFER_LVALUE->yy_buffer_status == YY_BUFFER_EOF_PENDING )
		/* don't do the read, it's not guaranteed to return an EOF,
		 * just force an EOF
		 */
		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = (yy_n_chars) = 0;

	else
		{
			int num_to_read =
			YY_CURRENT_BUFFER_LVALUE->yy_buf_size - number_to_move - 1;

		while ( num_to_read <= 0 )
			{ /* Not enough room in the buffer - grow it. */

			/* just a shorter name for the current buffer */
			YY_BUFFER_STATE b = YY_CURRENT_BUFFER_LVALUE;

			int yy_c_buf_p_offset =
				(int) ((yy_c_buf_p) - b->yy_ch_buf);

			if ( b->yy_is_our_buffer )
				{
				int new_size = b->yy_buf_size * 2;

				if ( new_size <= 0 )
					b->yy_buf_size += b->yy_buf_size / 8;
				else
					b->yy_buf_size *= 2;

				b->yy_ch_buf = (char *)
					/* Include room in for 2 EOB chars. */
					yyrealloc( (void *) b->yy_ch_buf,
							 (yy_size_t) (b->yy_buf_size + 2)  );
				}
			else
				/* Can't grow it, we don't own it. */
				b->yy_ch_buf = NULL;

			if ( ! b->yy_ch_buf )
				YY_FATAL_ERROR(
				"fatal error - scanner input buffer overflow" );

			(yy_c_buf_p) = &b->yy_ch_buf[yy_c_buf_p_offset];

			num_to_read = YY_CURRENT_BUFFER_LVALUE->yy_buf_size -
						number_to_move - 1;

			}

		if ( num_to_read > YY_READ_BUF_SIZE )
			num_to_read = YY_READ_BUF_SIZE;

		/* Read in more data. */
		YY_INPUT( (&YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[number_to_move]),
			(yy_n_chars), num_to_read );

		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = (yy_n_chars);
		}

	if ( (yy_n_chars) == 0 )
		{
		if ( number_to_move == YY_MORE_ADJ )
			{
			ret_val = EOB_ACT_END_OF_FILE;
			yyrestart( yyin  );
			}

		else
			{
			ret_val = EOB_ACT_LAST_MATCH;
			YY_CURRENT_BUFFER_LVALUE->yy_buffer_status =
				YY_BUFFER_EOF_PENDING;
			}
		}

	else
		ret_val = EOB_ACT_CONTINUE_SCAN;

	if (((yy_n_chars) + number_to_move) > YY_CURRENT_BUFFER_LVALUE->yy_buf_size) {
		/* Extend the array by 50%, plus the number we really need. */
		int new_size = (yy_n_chars) + number_to_move + ((yy_n_chars) >> 1);
		YY_CURRENT_BUFFER_LVALUE->yy_ch_buf = (char *) yyrealloc(
			(void *) YY_CURRENT_BUFFER_LVALUE->yy_ch_buf, (yy_size_t) new_size  );
		if ( ! YY_CURRENT_BUFFER_LVALUE->yy_ch_buf )
			YY_FATAL_ERROR( "out of dynamic memory in yy_get_next_buffer()" );
		/* "- 2" to take care of EOB's */
		YY_CURRENT_BUFFER_LVALUE->yy_buf_size = (int) (new_size - 2);
	}

	(yy_n_chars) += number_to_move;
	YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars)] = YY_END_OF_BUFFER_CHAR;
	YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars) + 1] = YY_END_OF_BUFFER_CHAR;

	(yytext_ptr) = &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[0];

	return ret_val;
}

/* yy_get_previous_state - get the state just before the EOB char was reached */

    static yy_state_type yy_get_previous_state (void)
{
	yy_state_type yy_current_state;
	char *yy_cp;
    
	yy_current_state = (yy_start);

	for ( yy_cp = (yytext_ptr) + YY_MORE_ADJ; yy_cp < (yy_c_buf_p); ++yy_cp )
		{
		YY_CHAR yy_c = (*yy_cp ? yy_ec[YY_SC_TO_UI(*yy_cp)] : 1);
		if ( yy_accept[yy_current_state] )
			{
			(yy_last_accepting_state) = yy_current_state;
			(yy_last_accepting_cpos) = yy_cp;
			}
		while ( yy_chk[yy_base[yy_current_state] + yy_c] != yy_current_state )
			{
			yy_current_state = (int) yy_def[yy_current_state];
			if ( yy_current_state >= 251 )
				yy_c = yy_meta[yy_c];
			}
		yy_current_state = yy_nxt[yy_base[yy_current_state] + yy_c];
		}

	return yy_current_state;
}

/* yy_try_NUL_trans - try to make a transition on the NUL character
 *
 * synopsis
 *	next_state = yy_try_NUL_trans( current_state );
 */
    static yy_state_type yy_try_NUL_trans  (yy_state_type yy_current_state )
{
	int yy_is_jam;
    	char *yy_cp = (yy_c_buf_p);

	YY_CHAR yy_c = 1;
	if ( yy_accept[yy_current_state] )
		{
		(yy_last_accepting_state) = yy_current_state;
		(yy_last_accepting_cpos) = yy_cp;
		}
	while ( yy_chk[yy_base[yy_current_state] + yy_c] != yy_current_state )
		{
		yy_current_state = (int) yy_def[yy_current_state];
		if ( yy_current_state >= 251 )
			yy_c = yy_meta[yy_c];
		}
	yy_current_state = yy_nxt[yy_base[yy_current_state] + yy_c];
	yy_is_jam = (yy_current_state == 250);

		return yy_is_jam ? 0 : yy_current_state;
}

#ifndef YY_NO_UNPUT

#endif

#ifndef YY_NO_INPUT
#ifdef __cplusplus
    static int yyinput (void)
#else
    static int input  (void)
#endif

{
	int c;
    
	*(yy_c_buf_p) = (yy_hold_char);

	if ( *(yy_c_buf_p) == YY_END_OF_BUFFER_CHAR )
		{
		/* yy_c_buf_p now points to the character we want to return.
		 * If this occurs *before* the EOB characters, then it's a
		 * valid NUL; if not, then we've hit the end of the buffer.
		 */
		if ( (yy_c_buf_p) < &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars)] )
			/* This was really a NUL. */
			*(yy_c_buf_p) = '\0';

		else
			{ /* need more input */
			int offset = (int) ((yy_c_buf_p) - (yytext_ptr));
			++(yy_c_buf_p);

			switch ( yy_get_next_buffer(  ) )
				{
				case EOB_ACT_LAST_MATCH:
					/* This happens because yy_g_n_b()
					 * sees that we've accumulated a
					 * token and flags that we need to
					 * try matching the token before
					 * proceeding.  But for input(),
					 * there's no matching to consider.
					 * So convert the EOB_ACT_LAST_MATCH
					 * to EOB_ACT_END_OF_FILE.
					 */

					/* Reset buffer status. */
					yyrestart( yyin );

					/*FALLTHROUGH*/

				case EOB_ACT_END_OF_FILE:
					{
					if ( yywrap(  ) )
						return 0;

					if ( ! (yy_did_buffer_switch_on_eof) )
						YY_NEW_FILE;
#ifdef __cplusplus
					return yyinput();
#else
					return input();
#endif
					}

				case EOB_ACT_CONTINUE_SCAN:
					(yy_c_buf_p) = (yytext_ptr) + offset;
					break;
				}
			}
		}

	c = *(unsigned char *) (yy_c_buf_p);	/* cast for 8-bit char's */
	*(yy_c_buf_p) = '\0';	/* preserve yytext */
	(yy_hold_char) = *++(yy_c_buf_p);

	return c;
}
#endif	/* ifndef YY_NO_INPUT */

/** Immediately switch to a different input stream.
 * @param input_file A readable stream.
 * 
 * @note This function does not reset the start condition to @c INITIAL .
 */
    void yyrestart  (FILE * input_file )
{
    
	if ( ! YY_CURRENT_BUFFER ){
        yyensure_buffer_stack ();
		YY_CURRENT_BUFFER_LVALUE =
            yy_create_buffer( yyin, YY_BUF_SIZE );
	}

	yy_init_buffer( YY_CURRENT_BUFFER, input_file );
	yy_load_buffer_state(  );
}

/** Switch to a different input buffer.
 * @param new_buffer The new input buffer.
 * 
 */
    void yy_switch_to_buffer  (YY_BUFFER_STATE  new_buffer )
{
    
	/* TODO. We should be able to replace this entire function body
	 * with
	 *		yypop_buffer_state();
	 *		yypush_buffer_state(new_buffer);
     */
	yyensure_buffer_stack ();
	if ( YY_CURRENT_BUFFER == new_buffer )
		return;

	if ( YY_CURRENT_BUFFER )
		{
		/* Flush out information for old buffer. */
		*(yy_c_buf_p) = (yy_hold_char);
		YY_CURRENT_BUFFER_LVALUE->yy_buf_pos = (yy_c_buf_p);
		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = (yy_n_chars);
		}

	YY_CURRENT_BUFFER_LVALUE = new_buffer;
	yy_load_buffer_state(  );

	/* We don't actually know whether we did this switch during
	 * EOF (yywrap()) processing, but the only time this flag
	 * is looked at is after yywrap() is called, so it's safe
	 * to go ahead and always set it.
	 */
	(yy_did_buffer_switch_on_eof) = 1;
}

static void yy_load_buffer_state  (void)
{
    	(yy_n_chars) = YY_CURRENT_BUFFER_LVALUE->yy_n_chars;
	(yytext_ptr) = (yy_c_buf_p) = YY_CURRENT_BUFFER_LVALUE->yy_buf_pos;
	yyin = YY_CURRENT_BUFFER_LVALUE->yy_input_file;
	(yy_hold_char) = *(yy_c_buf_p);
}

/** Allocate and initialize an input buffer state.
 * @param file A readable stream.
 * @param size The character buffer size in bytes. When in doubt, use @c YY_BUF_SIZE.
 * 
 * @return the allocated buffer state.
 */
    YY_BUFFER_STATE yy_create_buffer  (FILE * file, int  size )
{
	YY_BUFFER_STATE b;
    
	b = (YY_BUFFER_STATE) yyalloc( sizeof( struct yy_buffer_state )  );
	if ( ! b )
		YY_FATAL_ERROR( "out of dynamic memory in yy_create_buffer()" );

	b->yy_buf_size = size;

	/* yy_ch_buf has to be 2 characters longer than the size given because
	 * we need to put in 2 end-of-buffer characters.
	 */
	b->yy_ch_buf = (char *) yyalloc( (yy_size_t) (b->yy_buf_size + 2)  );
	if ( ! b->yy_ch_buf )
		YY_FATAL_ERROR( "out of dynamic memory in yy_create_buffer()" );

	b->yy_is_our_buffer = 1;

	yy_init_buffer( b, file );

	return b;
}

/** Destroy the buffer.
 * @param b a buffer created with yy_create_buffer()
 * 
 */
    void yy_delete_buffer (YY_BUFFER_STATE  b )
{
    
	if ( ! b )
		return;

	if ( b == YY_CURRENT_BUFFER ) /* Not sure if we should pop here. */
		YY_CURRENT_BUFFER_LVALUE = (YY_BUFFER_STATE) 0;

	if ( b->yy_is_our_buffer )
		yyfree( (void *) b->yy_ch_buf  );

	yyfree( (void *) b  );
}

/* Initializes or reinitializes a buffer.
 * This function is sometimes called more than once on the same buffer,
 * such as during a yyrestart() or at EOF.
 */
    static void yy_init_buffer  (YY_BUFFER_STATE  b, FILE * file )

{
	int oerrno = errno;
    
	yy_flush_buffer( b );

	b->yy_input_file = file;
	b->yy_fill_buffer = 1;

    /* If b is the current buffer, then yy_init_buffer was _probably_
     * called from yyrestart() or through yy_get_next_buffer.
     * In that case, we don't want to reset the lineno or column.
     */
    if (b != YY_CURRENT_BUFFER){
        b->yy_bs_lineno = 1;
        b->yy_bs_column = 0;
    }

        b->yy_is_interactive = file ? (isatty( fileno(file) ) > 0) : 0;
    
	errno = oerrno;
}

/** Discard all buffered characters. On the next scan, YY_INPUT will be called.
 * @param b the buffer state to be flushed, usually @c YY_CURRENT_BUFFER.
 * 
 */
    void yy_flush_buffer (YY_BUFFER_STATE  b )
{
    	if ( ! b )
		return;

	b->yy_n_chars = 0;

	/* We always need two end-of-buffer characters.  The first causes
	 * a transition to the end-of-buffer state.  The second causes
	 * a jam in that state.
	 */
	b->yy_ch_buf[0] = YY_END_OF_BUFFER_CHAR;
	b->yy_ch_buf[1] = YY_END_OF_BUFFER_CHAR;

	b->yy_buf_pos = &b->yy_ch_buf[0];

	b->yy_at_bol = 1;
	b->yy_buffer_status = YY_BUFFER_NEW;

	if ( b == YY_CURRENT_BUFFER )
		yy_load_buffer_state(  );
}

/** Pushes the new state onto the stack. The new state becomes
 *  the current state. This function will allocate the stack
 *  if necessary.
 *  @param new_buffer The new state.
 *  
 */
void yypush_buffer_state (YY_BUFFER_STATE new_buffer )
{
    	if (new_buffer == NULL)
		return;

	yyensure_buffer_stack();

	/* This block is copied from yy_switch_to_buffer. */
	if ( YY_CURRENT_BUFFER )
		{
		/* Flush out information for old buffer. */
		*(yy_c_buf_p) = (yy_hold_char);
		YY_CURRENT_BUFFER_LVALUE->yy_buf_pos = (yy_c_buf_p);
		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = (yy_n_chars);
		}

	/* Only push if top exists. Otherwise, replace top. */
	if (YY_CURRENT_BUFFER)
		(yy_buffer_stack_top)++;
	YY_CURRENT_BUFFER_LVALUE = new_buffer;

	/* copied from yy_switch_to_buffer. */
	yy_load_buffer_state(  );
	(yy_did_buffer_switch_on_eof) = 1;
}

/** Removes and deletes the top of the stack, if present.
 *  The next element becomes the new top.
 *  
 */
void yypop_buffer_state (void)
{
    	if (!YY_CURRENT_BUFFER)
		return;

	yy_delete_buffer(YY_CURRENT_BUFFER );
	YY_CURRENT_BUFFER_LVALUE = NULL;
	if ((yy_buffer_stack_top) > 0)
		--(yy_buffer_stack_top);

	if (YY_CURRENT_BUFFER) {
		yy_load_buffer_state(  );
		(yy_did_buffer_switch_on_eof) = 1;
	}
}

/* Allocates the stack if it does not exist.
 *  Guarantees space for at least one push.
 */
static void yyensure_buffer_stack (void)
{
	yy_size_t num_to_alloc;
    
	if (!(yy_buffer_stack)) {

		/* First allocation is just for 2 elements, since we don't know if this
		 * scanner will even need a stack. We use 2 instead of 1 to avoid an
		 * immediate realloc on the next call.
         */
      num_to_alloc = 1; /* After all that talk, this was set to 1 anyways... */
		(yy_buffer_stack) = (struct yy_buffer_state**)yyalloc
								(num_to_alloc * sizeof(struct yy_buffer_state*)
								);
		if ( ! (yy_buffer_stack) )
			YY_FATAL_ERROR( "out of dynamic memory in yyensure_buffer_stack()" );

		memset((yy_buffer_stack), 0, num_to_alloc * sizeof(struct yy_buffer_state*));

		(yy_buffer_stack_max) = num_to_alloc;
		(yy_buffer_stack_top) = 0;
		return;
	}

	if ((yy_buffer_stack_top) >= ((yy_buffer_stack_max)) - 1){

		/* Increase the buffer to prepare for a possible push. */
		yy_size_t grow_size = 8 /* arbitrary grow size */;

		num_to_alloc = (yy_buffer_stack_max) + grow_size;
		(yy_buffer_stack) = (struct yy_buffer_state**)yyrealloc
								((yy_buffer_stack),
								num_to_alloc * sizeof(struct yy_buffer_state*)
								);
		if ( ! (yy_buffer_stack) )
			YY_FATAL_ERROR( "out of dynamic memory in yyensure_buffer_stack()" );

		/* zero only the new slots.*/
		memset((yy_buffer_stack) + (yy_buffer_stack_max), 0, grow_size * sizeof(struct yy_buffer_state*));
		(yy_buffer_stack_max) = num_to_alloc;
	}
}

/** Setup the input buffer state to scan directly from a user-specified character buffer.
 * @param base the character buffer
 * @param size the size in bytes of the character buffer
 * 
 * @return the newly allocated buffer state object.
 */
YY_BUFFER_STATE yy_scan_buffer  (char * base, yy_size_t  size )
{
	YY_BUFFER_STATE b;
    
	if ( size < 2 ||
	     base[size-2] != YY_END_OF_BUFFER_CHAR ||
	     base[size-1] != YY_END_OF_BUFFER_CHAR )
		/* They forgot to leave room for the EOB's. */
		return NULL;

	b = (YY_BUFFER_STATE) yyalloc( sizeof( struct yy_buffer_state )  );
	if ( ! b )
		YY_FATAL_ERROR( "out of dynamic memory in yy_scan_buffer()" );

	b->yy_buf_size = (int) (size - 2);	/* "- 2" to take care of EOB's */
	b->yy_buf_pos = b->yy_ch_buf = base;
	b->yy_is_our_buffer = 0;
	b->yy_input_file = NULL;
	b->yy_n_chars = b->yy_buf_size;
	b->yy_is_interactive = 0;
	b->yy_at_bol = 1;
	b->yy_fill_buffer = 0;
	b->yy_buffer_status = YY_BUFFER_NEW;

	yy_switch_to_buffer( b  );

	return b;
}

/** Setup the input buffer state to scan a string. The next call to yylex() will
 * scan from a @e copy of @a str.
 * @param yystr a NUL-terminated string to scan
 * 
 * @return the newly allocated buffer state object.
 * @note If you want to scan bytes that may contain NUL values, then use
 *       yy_scan_bytes() instead.
 */
YY_BUFFER_STATE yy_scan_string (const char * yystr )
{
    
	return yy_scan_bytes( yystr, (int) strlen(yystr) );
}

/** Setup the input buffer state to scan the given bytes. The next call to yylex() will
 * scan from a @e copy of @a bytes.
 * @param yybytes the byte buffer to scan
 * @param _yybytes_len the number of bytes in the buffer pointed to by @a bytes.
 * 
 * @return the newly allocated buffer state object.
 */
YY_BUFFER_STATE yy_scan_bytes  (const char * yybytes, int  _yybytes_len )
{
	YY_BUFFER_STATE b;
	char *buf;
	yy_size_t n;
	int i;
    
	/* Get memory for full buffer, including space for trailing EOB's. */
	n = (yy_size_t) (_yybytes_len + 2);
	buf = (char *) yyalloc( n  );
	if ( ! buf )
		YY_FATAL_ERROR( "out of dynamic memory in yy_scan_bytes()" );

	for ( i = 0; i < _yybytes_len; ++i )
		buf[i] = yybytes[i];

	buf[_yybytes_len] = buf[_yybytes_len+1] = YY_END_OF_BUFFER_CHAR;

	b = yy_scan_buffer( buf, n );
	if ( ! b )
		YY_FATAL_ERROR( "bad buffer in yy_scan_bytes()" );

	/* It's okay to grow etc. this buffer, and we should throw it
	 * away when we're done.
	 */
	b->yy_is_our_buffer = 1;

	return b;
}

#ifndef YY_EXIT_FAILURE
#define YY_EXIT_FAILURE 2
#endif

static void yynoreturn yy_fatal_error (const char* msg )
{
			fprintf( stderr, "%s\n", msg );
	exit( YY_EXIT_FAILURE );
}

/* Redefine yyless() so it works in section 3 code. */

#undef yyless
#define yyless(n) \
	do \
		{ \
		/* Undo effects of setting up yytext. */ \
        int yyless_macro_arg = (n); \
        YY_LESS_LINENO(yyless_macro_arg);\
		yytext[yyleng] = (yy_hold_char); \
		(yy_c_buf_p) = yytext + yyless_macro_arg; \
		(yy_hold_char) = *(yy_c_buf_p); \
		*(yy_c_buf_p) = '\0'; \
		yyleng = yyless_macro_arg; \
		} \
	while ( 0 )

/* Accessor  methods (get/set functions) to struct members. */

/** Get the current line number.
 * 
 */
int yyget_lineno  (void)
{
    
    return yylineno;
}

/** Get the input stream.
 * 
 */
FILE *yyget_in  (void)
{
        return yyin;
}

/** Get the output stream.
 * 
 */
FILE *yyget_out  (void)
{
        return yyout;
}

/** Get the length of the current token.
 * 
 */
int yyget_leng  (void)
{
        return yyleng;
}

/** Get the current token.
 * 
 */

char *yyget_text  (void)
{
        return yytext;
}

/** Set the current line number.
 * @param _line_number line number
 * 
 */
void yyset_lineno (int  _line_number )
{
    
    yylineno = _line_number;
}

/** Set the input stream. This does not discard the current
 * input buffer.
 * @param _in_str A readable stream.
 * 
 * @see yy_switch_to_buffer
 */
void yyset_in (FILE *  _in_str )
{
        yyin = _in_str ;
}

void yyset_out (FILE *  _out_str )
{
        yyout = _out_str ;
}

int yyget_debug  (void)
{
        return yy_flex_debug;
}

void yyset_debug (int  _bdebug )
{
        yy_flex_debug = _bdebug ;
}

static int yy_init_globals (void)
{
        /* Initialization is the same as for the non-reentrant scanner.
     * This function is called from yylex_destroy(), so don't allocate here.
     */

    (yy_buffer_stack) = NULL;
    (yy_buffer_stack_top) = 0;
    (yy_buffer_stack_max) = 0;
    (yy_c_buf_p) = NULL;
    (yy_init) = 0;
    (yy_start) = 0;

/* Defined in main.c */
#ifdef YY_STDINIT
    yyin = stdin;
    yyout = stdout;
#else
    yyin = NULL;
    yyout = NULL;
#endif

    /* For future reference: Set errno on error, since we are called by
     * yylex_init()
     */
    return 0;
}

/* yylex_destroy is for both reentrant and non-reentrant scanners. */
int yylex_destroy  (void)
{
    
    /* Pop the buffer stack, destroying each element. */
	while(YY_CURRENT_BUFFER){
		yy_delete_buffer( YY_CURRENT_BUFFER  );
		YY_CURRENT_BUFFER_LVALUE = NULL;
		yypop_buffer_state();
	}

	/* Destroy the stack itself. */
	yyfree((yy_buffer_stack) );
	(yy_buffer_stack) = NULL;

    /* Reset the globals. This is important in a non-reentrant scanner so the next time
     * yylex() is called, initialization will occur. */
    yy_init_globals( );

    return 0;
}

/*
 * Internal utility routines.
 */

#ifndef yytext_ptr
static void yy_flex_strncpy (char* s1, const char * s2, int n )
{
		
	int i;
	for ( i = 0; i < n; ++i )
		s1[i] = s2[i];
}
#endif

#ifdef YY_NEED_STRLEN
static int yy_flex_strlen (const char * s )
{
	int n;
	for ( n = 0; s[n]; ++n )
		;

	return n;
}
#endif

void *yyalloc (yy_size_t  size )
{
			return malloc(size);
}

void *yyrealloc  (void * ptr, yy_size_t  size )
{
		
	/* The cast to (char *) in the following accommodates both
	 * implementations that use char* generic pointers, and those
	 * that use void* generic pointers.  It works with the latter
	 * because both ANSI C and C++ allow castless assignment from
	 * any pointer type to void*, and deal with argument conversions
	 * as though doing an assignment.
	 */
	return realloc(ptr, size);
}

void yyfree (void * ptr )
{
			free( (char *) ptr );	/* see yyrealloc() for (char *) cast */
}

#define YYTABLES_NAME "yytables"

#line 150 "lex.l"


