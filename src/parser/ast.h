/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */
#pragma once

#include <vector>
#include <string>
#include <memory>

enum JoinType {
    INNER_JOIN, LEFT_JOIN, RIGHT_JOIN, FULL_JOIN, SEMI_JOIN, ANTI_JOIN
};

enum AggType {
    AGG_NONE,       // 无聚合
    AGG_COUNT,      // 计数
    AGG_SUM,        // 求和
    AGG_AVG,        // 平均值
    AGG_MAX,        // 最大值
    AGG_MIN         // 最小值
};

namespace ast {

enum SvType {
    SV_TYPE_INT, SV_TYPE_FLOAT, SV_TYPE_STRING, SV_TYPE_BOOL
};

enum SvCompOp {
    SV_OP_EQ, SV_OP_NE, SV_OP_LT, SV_OP_GT, SV_OP_LE, SV_OP_GE, SV_OP_NONE, SV_OP_ADD, SV_OP_SUB, SV_OP_MUL, SV_OP_DIV
};

enum OrderByDir {
    OrderBy_DEFAULT,
    OrderBy_ASC,
    OrderBy_DESC
};

enum SetKnobType {
    EnableNestLoop, EnableSortMerge
};

// Base class for tree nodes
struct TreeNode {
    virtual ~TreeNode() = default;  // enable polymorphism
};

struct Help : public TreeNode {
};

struct ShowTables : public TreeNode {
};

struct TxnBegin : public TreeNode {
};

struct TxnCommit : public TreeNode {
};

struct TxnAbort : public TreeNode {
};

struct TxnRollback : public TreeNode {
};

struct TypeLen : public TreeNode {
    SvType type;
    int len;

    TypeLen(SvType type_, int len_) : type(type_), len(len_) {}
};

struct Field : public TreeNode {
};

struct ColDef : public Field {
    std::string col_name;
    std::shared_ptr<TypeLen> type_len;

    ColDef(std::string col_name_, std::shared_ptr<TypeLen> type_len_) :
            col_name(std::move(col_name_)), type_len(std::move(type_len_)) {}
};

struct CreateTable : public TreeNode {
    std::string tab_name;
    std::vector<std::shared_ptr<Field>> fields;

    CreateTable(std::string tab_name_, std::vector<std::shared_ptr<Field>> fields_) :
            tab_name(std::move(tab_name_)), fields(std::move(fields_)) {}
};

struct DropTable : public TreeNode {
    std::string tab_name;

    DropTable(std::string tab_name_) : tab_name(std::move(tab_name_)) {}
};

struct DescTable : public TreeNode {
    std::string tab_name;

    DescTable(std::string tab_name_) : tab_name(std::move(tab_name_)) {}
};

struct CreateIndex : public TreeNode {
    std::string tab_name;
    std::vector<std::string> col_names;

    CreateIndex(std::string tab_name_, std::vector<std::string> col_names_) :
            tab_name(std::move(tab_name_)), col_names(std::move(col_names_)) {}
};

struct DropIndex : public TreeNode {
    std::string tab_name;
    std::vector<std::string> col_names;

    DropIndex(std::string tab_name_, std::vector<std::string> col_names_) :
            tab_name(std::move(tab_name_)), col_names(std::move(col_names_)) {}
};

struct ShowIndex : public TreeNode {
    std::string tab_name;

    ShowIndex(std::string tab_name_) :
            tab_name(std::move(tab_name_)) {}
};

struct CreateStaticCheckpoint : public TreeNode {
};

struct Expr : public TreeNode {
};

struct Value : public Expr {
};

struct IntLit : public Value {
    int val;

    IntLit(int val_) : val(val_) {}
};

struct FloatLit : public Value {
    float val;

    FloatLit(float val_) : val(val_) {}
};

struct StringLit : public Value {
    std::string val;

    StringLit(std::string val_) : val(std::move(val_)) {}
};

struct BoolLit : public Value {
    bool val;

    BoolLit(bool val_) : val(val_) {}
};

struct Col : public Expr {
    std::string tab_name;
    std::string col_name;

    Col(std::string tab_name_, std::string col_name_) :
            tab_name(std::move(tab_name_)), col_name(std::move(col_name_)) {}
};

struct BinaryExpr : public TreeNode {
    std::shared_ptr<Col> lhs;
    SvCompOp op;
    std::shared_ptr<Expr> rhs;/*可以是值和表达式*/

    BinaryExpr(std::shared_ptr<Col> lhs_, SvCompOp op_, std::shared_ptr<Expr> rhs_) :
            lhs(std::move(lhs_)), op(op_), rhs(std::move(rhs_)) {}
};

struct BinaryUpdateExpr : public TreeNode {
    std::shared_ptr<Expr> lhs;
    SvCompOp op;
    std::shared_ptr<Expr> rhs;/*可以是值和表达式*/

    BinaryUpdateExpr(std::shared_ptr<Expr> lhs_, SvCompOp op_, std::shared_ptr<Expr> rhs_) :
            lhs(std::move(lhs_)), op(op_), rhs(std::move(rhs_)) {}
};

struct SetClause : public TreeNode {
    std::string col_name;
    std::shared_ptr<Value> val;/*在ast命名空间是表达式的value*/
    std::shared_ptr<BinaryUpdateExpr> valexpr;

    SetClause(std::string col_name_, std::shared_ptr<BinaryUpdateExpr> valexpr_) :
            col_name(std::move(col_name_)), valexpr(std::move(valexpr_)) {}
};

struct BinaryAggExpr : public TreeNode {
    std::shared_ptr<Expr> lhs;
    SvCompOp op;
    std::shared_ptr<Expr> rhs;/*可以是值和表达式*/

    BinaryAggExpr(std::shared_ptr<Expr> lhs_, SvCompOp op_, std::shared_ptr<Expr> rhs_) :
            lhs(std::move(lhs_)), op(op_), rhs(std::move(rhs_)) {}
};

struct OrderBy : public TreeNode
{
    std::shared_ptr<Col> cols;
    OrderByDir orderby_dir;
    OrderBy( std::shared_ptr<Col> cols_, OrderByDir orderby_dir_) :
       cols(std::move(cols_)), orderby_dir(std::move(orderby_dir_)) {}
};

struct AggExpr : public Expr {  // 改为继承Expr
    AggType type;
    std::shared_ptr<Col> col;
    std::string alias;
    
    AggExpr(AggType type_, std::shared_ptr<Col> col_, std::string alias_ = "") :
        type(type_), col(std::move(col_)), alias(std::move(alias_)) {}
};

struct InsertStmt : public TreeNode {
    std::string tab_name;
    std::vector<std::shared_ptr<Value>> vals;

    InsertStmt(std::string tab_name_, std::vector<std::shared_ptr<Value>> vals_) :
            tab_name(std::move(tab_name_)), vals(std::move(vals_)) {}
};

struct DeleteStmt : public TreeNode {
    std::string tab_name;
    std::vector<std::shared_ptr<BinaryExpr>> conds;

    DeleteStmt(std::string tab_name_, std::vector<std::shared_ptr<BinaryExpr>> conds_) :
            tab_name(std::move(tab_name_)), conds(std::move(conds_)) {}
};

struct UpdateStmt : public TreeNode {
    /*UPDATE users
    SET name = '张三'
    WHERE id = 1001;*/
    std::string tab_name;
    std::vector<std::shared_ptr<SetClause>> set_clauses;/*要更新的字段和值:name = '张三'*/
    std::vector<std::shared_ptr<BinaryExpr>> conds;/*选择条件:id = 1001*/

    UpdateStmt(std::string tab_name_,
               std::vector<std::shared_ptr<SetClause>> set_clauses_,
               std::vector<std::shared_ptr<BinaryExpr>> conds_) :
            tab_name(std::move(tab_name_)), set_clauses(std::move(set_clauses_)), conds(std::move(conds_)) {}
};

struct JoinExpr : public TreeNode {
    std::string left;
    std::string right;
    std::vector<std::shared_ptr<BinaryExpr>> conds;
    JoinType type;

    JoinExpr(std::string left_, std::string right_,
               std::vector<std::shared_ptr<BinaryExpr>> conds_, JoinType type_) :
            left(std::move(left_)), right(std::move(right_)), conds(std::move(conds_)), type(type_) {}
};

struct SelectStmt : public TreeNode {
    std::vector<std::shared_ptr<Col>> cols;
    std::vector<std::pair<std::string, std::string>> tabs;
    std::vector<std::shared_ptr<BinaryExpr>> conds;
    std::vector<std::shared_ptr<JoinExpr>> jointree;


    bool has_sort;
    std::vector<std::shared_ptr<OrderBy>> order;
    int limit;  // 添加LIMIT字段


    SelectStmt(std::vector<std::shared_ptr<Col>> cols_,
               std::vector<std::pair<std::string, std::string>> tabs_,
               std::vector<std::shared_ptr<JoinExpr>> jointree_,
               std::vector<std::shared_ptr<BinaryExpr>> conds_,
               std::vector<std::shared_ptr<OrderBy>> order_,
               int limit_ = -1) :
            cols(std::move(cols_)), tabs(std::move(tabs_)), jointree(std::move(jointree_)),
            conds(std::move(conds_)),
            order(std::move(order_)), limit(limit_) {
                has_sort = !order.empty();
            }
};

struct GroupByStmt : public TreeNode {
    std::vector<std::shared_ptr<Col>> select_cols;         // 选择的普通列
    std::vector<std::shared_ptr<AggExpr>> agg_exprs;       // 聚合表达式
    std::vector<std::string> tabs; // 数据源表
    std::vector<std::shared_ptr<JoinExpr>> jointree;       // 连接树
    std::vector<std::shared_ptr<Col>> group_cols;          // 分组列
    std::vector<std::shared_ptr<BinaryAggExpr>> having_conds; // HAVING条件
    std::vector<std::shared_ptr<BinaryExpr>> conds;
    bool has_sort;                                         // 是否有排序
    std::vector<std::shared_ptr<OrderBy>> order;           // 排序条件
    int limit;                                             // LIMIT数量

    GroupByStmt(
        std::vector<std::shared_ptr<Col>> select_cols_,
        std::vector<std::shared_ptr<AggExpr>> agg_exprs_,
        std::vector<std::string> tabs_,
        std::vector<std::shared_ptr<JoinExpr>> jointree_,
        std::vector<std::shared_ptr<Col>> group_cols_,
        std::vector<std::shared_ptr<BinaryAggExpr>> having_conds_,
        std::vector<std::shared_ptr<BinaryExpr>> conds_,
        std::vector<std::shared_ptr<OrderBy>> order_,
        int limit_ = -1
    ) : select_cols(std::move(select_cols_)),
        agg_exprs(std::move(agg_exprs_)),
        tabs(std::move(tabs_)),
        jointree(std::move(jointree_)),
        group_cols(std::move(group_cols_)),
        having_conds(std::move(having_conds_)),
        conds(std::move(conds_)),
        order(std::move(order_)),
        limit(limit_) {
        has_sort = !order.empty();
    }
};



struct ExplainStmt : public TreeNode {
    private:
        std::shared_ptr<TreeNode> originalStmt; // 被解释的原始语句

    public:
        ExplainStmt(std::shared_ptr<TreeNode> stmt) : originalStmt(stmt) {}
        
        std::shared_ptr<TreeNode> getOriginalStmt() const {
            return originalStmt;
        }
        
        // 其他方法...
};

struct SemijoinStmt : public TreeNode {
    std::vector<std::shared_ptr<Col>> cols;
    std::vector<std::pair<std::string, std::string>> tabs;
    std::vector<std::shared_ptr<BinaryExpr>> conds;
    std::vector<std::shared_ptr<JoinExpr>> jointree;


    SemijoinStmt(std::vector<std::shared_ptr<Col>> cols_,
               std::vector<std::pair<std::string, std::string>> tabs_,
               std::vector<std::shared_ptr<JoinExpr>> jointree_,
               std::vector<std::shared_ptr<BinaryExpr>> conds_) :
            cols(std::move(cols_)), tabs(std::move(tabs_)),
            jointree(std::move(jointree_)),conds(std::move(conds_))
        {
               
        }
        
        // 其他方法...
};

//Antijoin ，直接复制粘贴Semijoin
struct AntijoinStmt : public TreeNode {
    std::vector<std::shared_ptr<Col>> cols;
    std::vector<std::pair<std::string, std::string>> tabs;
    std::vector<std::shared_ptr<BinaryExpr>> conds;
    std::vector<std::shared_ptr<JoinExpr>> jointree;


    AntijoinStmt(std::vector<std::shared_ptr<Col>> cols_,
               std::vector<std::pair<std::string, std::string>> tabs_,
               std::vector<std::shared_ptr<JoinExpr>> jointree_,
               std::vector<std::shared_ptr<BinaryExpr>> conds_) :
            cols(std::move(cols_)), tabs(std::move(tabs_)),
            jointree(std::move(jointree_)),conds(std::move(conds_))
        {
               
        }
        
        // 其他方法...
};


struct LoadStmt : public TreeNode {
    std::string tab_name;
    std::string filename;


    LoadStmt(std::string tab_name_, std::string filename_) :
            tab_name(std::move(tab_name_)), filename(std::move(filename_))
        {
               
        }
};


// set enable_nestloop
struct SetStmt : public TreeNode {
    SetKnobType set_knob_type_;
    bool bool_val_;

    SetStmt(SetKnobType &type, bool bool_value) : 
        set_knob_type_(type), bool_val_(bool_value) { }
};

struct SelectItem {
    std::shared_ptr<Col> column;        // 普通列
    std::shared_ptr<AggExpr> agg_expr;  // 聚合表达式
    
    SelectItem(std::shared_ptr<Col> col, std::shared_ptr<AggExpr> agg) :
        column(col), agg_expr(agg){}
};

// Semantic value
struct SemValue {
    int sv_int;
    float sv_float;
    std::string sv_str;
    bool sv_bool;
    OrderByDir sv_orderby_dir;
    std::vector<std::string> sv_strs;
    std::vector<std::pair<std::string, std::string>> sv_mapstr;
    std::pair<std::string, std::string> sv_strpair;

    std::shared_ptr<TreeNode> sv_node;

    SvCompOp sv_comp_op;

    std::shared_ptr<TypeLen> sv_type_len;

    std::shared_ptr<Field> sv_field;
    std::vector<std::shared_ptr<Field>> sv_fields;

    std::shared_ptr<Expr> sv_expr;

    std::shared_ptr<Value> sv_val;
    std::vector<std::shared_ptr<Value>> sv_vals;

    std::shared_ptr<Col> sv_col;
    std::vector<std::shared_ptr<Col>> sv_cols;
    std::vector<std::shared_ptr<SelectItem>> sv_select_items;
    std::shared_ptr<SelectItem> sv_select_item;

    std::shared_ptr<SetClause> sv_set_clause;
    std::vector<std::shared_ptr<SetClause>> sv_set_clauses;
    std::shared_ptr<BinaryUpdateExpr> sv_twoexpr;

    std::shared_ptr<BinaryExpr> sv_cond;
    std::vector<std::shared_ptr<BinaryExpr>> sv_conds;

    std::vector<std::shared_ptr<OrderBy>> sv_orderby;

    struct {
        std::vector<std::pair<std::string, std::string>> tables;
        std::vector<std::shared_ptr<JoinExpr>> joins;
    } sv_table_expr;

    // std::shared_ptr<OrderBy> sv_explain;

    std::vector<std::shared_ptr<BinaryAggExpr>> sv_having;
    std::shared_ptr<BinaryAggExpr> sv_having_cond;
    std::shared_ptr<AggExpr> sv_agg;
    std::vector<std::shared_ptr<Col>> sv_group_by;

    SetKnobType sv_setKnobType;
};

extern std::shared_ptr<ast::TreeNode> parse_tree;

}

#define YYSTYPE ast::SemValue
