/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once

#include <cassert>
#include <cstring>
#include <memory>
#include <string>
#include <vector>

#include "parser/parser.h"
#include "system/sm.h"
#include "common/common.h"

class Query{
    public:
    std::shared_ptr<ast::TreeNode> parse;
    // TODO jointree
    // where条件
    std::vector<Condition> conds;
    // 投影列
    std::vector<TabCol> cols;
    // 表名
    std::vector<std::string> tables;
    // update 的set 值
    std::vector<SetClause> set_clauses;
    //insert 的values值
    std::vector<Value> values;

    /*explain的子query*/
    std::shared_ptr<Query> explain_query;

    /*jointree*/
    std::vector<JoinRef> jointree;
    std::string loadFileName;
    bool is_select_all_cols{false};
    bool is_semi_join{false};
    bool is_anti_join{false};

        //聚合相关
    std::vector<ast::AggExpr> agg_exprs;       // 聚合表达式
    std::vector<TabCol> group_cols;          // 分组列
    std::vector<Condition> having_conds; // HAVING条件

    // LIMIT相关
    int limit;                               // LIMIT数量，-1表示无限制

    Query() : limit(-1) {}

};

class Analyze
{
private:
    SmManager *sm_manager_;
public:
    Analyze(SmManager *sm_manager) : sm_manager_(sm_manager){}
    ~Analyze(){}

    std::shared_ptr<Query> do_analyze(std::shared_ptr<ast::TreeNode> root);

private:
    TabCol check_column(const std::vector<ColMeta> &all_cols, TabCol target);
    TabCol semi_join_check_column(const std::vector<ColMeta> &all_cols, TabCol target, std::string left_table);
    TabCol anti_join_check_column(const std::vector<ColMeta> &all_cols, TabCol target, std::string left_table);
    void get_all_cols(const std::vector<std::string> &tab_names, std::vector<ColMeta> &all_cols);
    void get_clause(const std::vector<std::shared_ptr<ast::BinaryExpr>> &sv_conds, std::vector<Condition> &conds);
    void get_join(const std::vector<std::shared_ptr<ast::JoinExpr>> sv_joins, std::vector<JoinRef> &joins);
    void check_clause(const std::vector<std::string> &tab_names, std::vector<Condition> &conds);
    void check_join_clause(std::vector<JoinRef> &joins);
    Value convert_sv_value(const std::shared_ptr<ast::Value> &sv_val);
    CompOp convert_sv_comp_op(ast::SvCompOp op);
    SetValue convert_stvalue(const std::shared_ptr<ast::BinaryUpdateExpr> &sv_val);

    void validate_aggregation_query(std::shared_ptr<ast::GroupByStmt> stmt, std::shared_ptr<Query> query, const std::vector<ColMeta> &all_cols);
    void validate_where_clause_no_aggregates(const std::vector<std::shared_ptr<ast::BinaryExpr>>& where_conds);
    void validate_select_columns_in_group_by(std::shared_ptr<ast::GroupByStmt> stmt, const std::vector<ColMeta> &all_cols);
    void validate_aggregate_function_types(const std::vector<std::shared_ptr<ast::AggExpr>>& agg_exprs, const std::vector<ColMeta> &all_cols);
    void validate_group_by_columns(const std::vector<std::shared_ptr<ast::Col>>& group_cols, const std::vector<ColMeta> &all_cols);
};

