# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/simple_consistency_check.dir/all
all: CMakeFiles/test_db2025_compatibility.dir/all
all: CMakeFiles/tpcc_start.dir/all
all: CMakeFiles/tpcc_load.dir/all

.PHONY : all

# The main recursive "preinstall" target.
preinstall:

.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/simple_consistency_check.dir/clean
clean: CMakeFiles/test_db2025_compatibility.dir/clean
clean: CMakeFiles/tpcc_start.dir/clean
clean: CMakeFiles/tpcc_load.dir/clean

.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/simple_consistency_check.dir

# All Build rule for target.
CMakeFiles/simple_consistency_check.dir/all:
	$(MAKE) -f CMakeFiles/simple_consistency_check.dir/build.make CMakeFiles/simple_consistency_check.dir/depend
	$(MAKE) -f CMakeFiles/simple_consistency_check.dir/build.make CMakeFiles/simple_consistency_check.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build/CMakeFiles --progress-num=1,2,3 "Built target simple_consistency_check"
.PHONY : CMakeFiles/simple_consistency_check.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/simple_consistency_check.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build/CMakeFiles 3
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/simple_consistency_check.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build/CMakeFiles 0
.PHONY : CMakeFiles/simple_consistency_check.dir/rule

# Convenience name for target.
simple_consistency_check: CMakeFiles/simple_consistency_check.dir/rule

.PHONY : simple_consistency_check

# clean rule for target.
CMakeFiles/simple_consistency_check.dir/clean:
	$(MAKE) -f CMakeFiles/simple_consistency_check.dir/build.make CMakeFiles/simple_consistency_check.dir/clean
.PHONY : CMakeFiles/simple_consistency_check.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/test_db2025_compatibility.dir

# All Build rule for target.
CMakeFiles/test_db2025_compatibility.dir/all:
	$(MAKE) -f CMakeFiles/test_db2025_compatibility.dir/build.make CMakeFiles/test_db2025_compatibility.dir/depend
	$(MAKE) -f CMakeFiles/test_db2025_compatibility.dir/build.make CMakeFiles/test_db2025_compatibility.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build/CMakeFiles --progress-num=4,5 "Built target test_db2025_compatibility"
.PHONY : CMakeFiles/test_db2025_compatibility.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/test_db2025_compatibility.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build/CMakeFiles 2
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/test_db2025_compatibility.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build/CMakeFiles 0
.PHONY : CMakeFiles/test_db2025_compatibility.dir/rule

# Convenience name for target.
test_db2025_compatibility: CMakeFiles/test_db2025_compatibility.dir/rule

.PHONY : test_db2025_compatibility

# clean rule for target.
CMakeFiles/test_db2025_compatibility.dir/clean:
	$(MAKE) -f CMakeFiles/test_db2025_compatibility.dir/build.make CMakeFiles/test_db2025_compatibility.dir/clean
.PHONY : CMakeFiles/test_db2025_compatibility.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/tpcc_start.dir

# All Build rule for target.
CMakeFiles/tpcc_start.dir/all:
	$(MAKE) -f CMakeFiles/tpcc_start.dir/build.make CMakeFiles/tpcc_start.dir/depend
	$(MAKE) -f CMakeFiles/tpcc_start.dir/build.make CMakeFiles/tpcc_start.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build/CMakeFiles --progress-num=9,10,11,12,13,14,15,16,17,18,19,20,21 "Built target tpcc_start"
.PHONY : CMakeFiles/tpcc_start.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/tpcc_start.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build/CMakeFiles 13
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/tpcc_start.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build/CMakeFiles 0
.PHONY : CMakeFiles/tpcc_start.dir/rule

# Convenience name for target.
tpcc_start: CMakeFiles/tpcc_start.dir/rule

.PHONY : tpcc_start

# clean rule for target.
CMakeFiles/tpcc_start.dir/clean:
	$(MAKE) -f CMakeFiles/tpcc_start.dir/build.make CMakeFiles/tpcc_start.dir/clean
.PHONY : CMakeFiles/tpcc_start.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/tpcc_load.dir

# All Build rule for target.
CMakeFiles/tpcc_load.dir/all:
	$(MAKE) -f CMakeFiles/tpcc_load.dir/build.make CMakeFiles/tpcc_load.dir/depend
	$(MAKE) -f CMakeFiles/tpcc_load.dir/build.make CMakeFiles/tpcc_load.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build/CMakeFiles --progress-num=6,7,8 "Built target tpcc_load"
.PHONY : CMakeFiles/tpcc_load.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/tpcc_load.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build/CMakeFiles 3
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/tpcc_load.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build/CMakeFiles 0
.PHONY : CMakeFiles/tpcc_load.dir/rule

# Convenience name for target.
tpcc_load: CMakeFiles/tpcc_load.dir/rule

.PHONY : tpcc_load

# clean rule for target.
CMakeFiles/tpcc_load.dir/clean:
	$(MAKE) -f CMakeFiles/tpcc_load.dir/build.make CMakeFiles/tpcc_load.dir/clean
.PHONY : CMakeFiles/tpcc_load.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

