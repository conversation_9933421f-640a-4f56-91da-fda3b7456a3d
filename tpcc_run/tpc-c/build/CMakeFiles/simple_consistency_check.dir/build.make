# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build

# Include any dependencies generated for this target.
include CMakeFiles/simple_consistency_check.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/simple_consistency_check.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/simple_consistency_check.dir/flags.make

CMakeFiles/simple_consistency_check.dir/simple_consistency_check.cpp.o: CMakeFiles/simple_consistency_check.dir/flags.make
CMakeFiles/simple_consistency_check.dir/simple_consistency_check.cpp.o: ../simple_consistency_check.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/simple_consistency_check.dir/simple_consistency_check.cpp.o"
	/usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/simple_consistency_check.dir/simple_consistency_check.cpp.o -c /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/simple_consistency_check.cpp

CMakeFiles/simple_consistency_check.dir/simple_consistency_check.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/simple_consistency_check.dir/simple_consistency_check.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/simple_consistency_check.cpp > CMakeFiles/simple_consistency_check.dir/simple_consistency_check.cpp.i

CMakeFiles/simple_consistency_check.dir/simple_consistency_check.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/simple_consistency_check.dir/simple_consistency_check.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/simple_consistency_check.cpp -o CMakeFiles/simple_consistency_check.dir/simple_consistency_check.cpp.s

CMakeFiles/simple_consistency_check.dir/support.cpp.o: CMakeFiles/simple_consistency_check.dir/flags.make
CMakeFiles/simple_consistency_check.dir/support.cpp.o: ../support.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/simple_consistency_check.dir/support.cpp.o"
	/usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/simple_consistency_check.dir/support.cpp.o -c /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/support.cpp

CMakeFiles/simple_consistency_check.dir/support.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/simple_consistency_check.dir/support.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/support.cpp > CMakeFiles/simple_consistency_check.dir/support.cpp.i

CMakeFiles/simple_consistency_check.dir/support.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/simple_consistency_check.dir/support.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/support.cpp -o CMakeFiles/simple_consistency_check.dir/support.cpp.s

# Object files for target simple_consistency_check
simple_consistency_check_OBJECTS = \
"CMakeFiles/simple_consistency_check.dir/simple_consistency_check.cpp.o" \
"CMakeFiles/simple_consistency_check.dir/support.cpp.o"

# External object files for target simple_consistency_check
simple_consistency_check_EXTERNAL_OBJECTS =

simple_consistency_check: CMakeFiles/simple_consistency_check.dir/simple_consistency_check.cpp.o
simple_consistency_check: CMakeFiles/simple_consistency_check.dir/support.cpp.o
simple_consistency_check: CMakeFiles/simple_consistency_check.dir/build.make
simple_consistency_check: CMakeFiles/simple_consistency_check.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CXX executable simple_consistency_check"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/simple_consistency_check.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/simple_consistency_check.dir/build: simple_consistency_check

.PHONY : CMakeFiles/simple_consistency_check.dir/build

CMakeFiles/simple_consistency_check.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/simple_consistency_check.dir/cmake_clean.cmake
.PHONY : CMakeFiles/simple_consistency_check.dir/clean

CMakeFiles/simple_consistency_check.dir/depend:
	cd /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build/CMakeFiles/simple_consistency_check.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/simple_consistency_check.dir/depend

