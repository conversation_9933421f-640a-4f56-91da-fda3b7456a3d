#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/simple_consistency_check.cpp
stdlib.h
-
string.h
-
stdio.h
-
iostream
-
string
-
vector
-
sstream
-
fstream
-
unistd.h
-
sys/socket.h
-
netinet/in.h
-
netdb.h
-
errno.h
-
tpc.h
/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/tpc.h

/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/support.cpp
stdlib.h
-
stdio.h
-
string.h
-
time.h
-
ctype.h
-
tpc.h
/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/tpc.h

/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/tpc.h
atomic
-
stdio.h
-
string
-
vector
-
pthread.h
/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/pthread.h

