#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/load.cpp
errno.h
-
netdb.h
-
stdio.h
-
string.h
-
sys/select.h
-
sys/socket.h
-
unistd.h
-
stdlib.h
-
fcntl.h
-
string
-
iostream
-
sys/time.h
-
signal.h
-
timers.h
/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/timers.h
load.h
/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/load.h
sys/un.h
-
spt_proc.h
/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/spt_proc.h
tpc.h
/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/tpc.h

/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/load.h

/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/spt_proc.h

/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/support.cpp
stdlib.h
-
stdio.h
-
string.h
-
time.h
-
ctype.h
-
tpc.h
/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/tpc.h

/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/timers.h
atomic
-
cstdint
-
sys/time.h
-
string.h
-
time.h
-

/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/tpc.h
atomic
-
stdio.h
-
string
-
vector
-
pthread.h
/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/pthread.h

