# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build

# Include any dependencies generated for this target.
include CMakeFiles/test_db2025_compatibility.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/test_db2025_compatibility.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/test_db2025_compatibility.dir/flags.make

CMakeFiles/test_db2025_compatibility.dir/test_db2025_compatibility.cpp.o: CMakeFiles/test_db2025_compatibility.dir/flags.make
CMakeFiles/test_db2025_compatibility.dir/test_db2025_compatibility.cpp.o: ../test_db2025_compatibility.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/test_db2025_compatibility.dir/test_db2025_compatibility.cpp.o"
	/usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/test_db2025_compatibility.dir/test_db2025_compatibility.cpp.o -c /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/test_db2025_compatibility.cpp

CMakeFiles/test_db2025_compatibility.dir/test_db2025_compatibility.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/test_db2025_compatibility.dir/test_db2025_compatibility.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/test_db2025_compatibility.cpp > CMakeFiles/test_db2025_compatibility.dir/test_db2025_compatibility.cpp.i

CMakeFiles/test_db2025_compatibility.dir/test_db2025_compatibility.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/test_db2025_compatibility.dir/test_db2025_compatibility.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/test_db2025_compatibility.cpp -o CMakeFiles/test_db2025_compatibility.dir/test_db2025_compatibility.cpp.s

# Object files for target test_db2025_compatibility
test_db2025_compatibility_OBJECTS = \
"CMakeFiles/test_db2025_compatibility.dir/test_db2025_compatibility.cpp.o"

# External object files for target test_db2025_compatibility
test_db2025_compatibility_EXTERNAL_OBJECTS =

test_db2025_compatibility: CMakeFiles/test_db2025_compatibility.dir/test_db2025_compatibility.cpp.o
test_db2025_compatibility: CMakeFiles/test_db2025_compatibility.dir/build.make
test_db2025_compatibility: CMakeFiles/test_db2025_compatibility.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable test_db2025_compatibility"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/test_db2025_compatibility.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/test_db2025_compatibility.dir/build: test_db2025_compatibility

.PHONY : CMakeFiles/test_db2025_compatibility.dir/build

CMakeFiles/test_db2025_compatibility.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/test_db2025_compatibility.dir/cmake_clean.cmake
.PHONY : CMakeFiles/test_db2025_compatibility.dir/clean

CMakeFiles/test_db2025_compatibility.dir/depend:
	cd /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/build/CMakeFiles/test_db2025_compatibility.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/test_db2025_compatibility.dir/depend

