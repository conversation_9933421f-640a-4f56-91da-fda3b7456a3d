create table departments (dept_id int, dept_name char(20));

create table employees (emp_id int, emp_name char(20), dept_id int, salary int);

insert into departments values(1, 'HR');

insert into departments values(2, 'Engineering');

insert into departments values(3, 'Sales');

insert into departments values(4, 'Marketing');

insert into employees values(101, '<PERSON>', 1, 70000);

insert into employees values(102, '<PERSON>', 2, 80000);

insert into employees values(103, '<PERSON>', 2, 90000);

insert into employees values(104, '<PERSON>', 1, 75000);

select dept_id, dept_name from departments ANTI JOIN employees ON departments.dept_id = employees.dept_id;