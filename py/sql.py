import pexpect
import sys
import re
import time  # 导入时间模块


def batch_execute_sql(sql_file_path, client_path):
    """
    批量执行SQL语句到rmdb_client，统计每条SQL执行时间，最后统计总执行时间
    :param sql_file_path: SQL脚本文件路径（每行一条SQL，以分号结尾）
    :param client_path: rmdb_client可执行文件路径
    """
    # 记录开始时间（总耗时）
    total_start_time = time.time()
    
    # 1. 读取SQL脚本并过滤注释和空行
    with open(sql_file_path, 'r') as f:
        sql_commands = []
        in_multiline_comment = False

        for line in f:
            line = line.strip()

            # 跳过空行
            if not line:
                continue

            # 处理多行注释
            if in_multiline_comment:
                # 检查多行注释结束
                if '*/' in line:
                    line = line.split('*/', 1)[1].strip()
                    in_multiline_comment = False
                else:
                    # 仍在多行注释中，跳过当前行
                    continue

            # 如果不在多行注释中，处理单行注释和多行注释开始
            if not in_multiline_comment:
                # 检查单行注释
                if '--' in line:
                    line = line.split('--', 1)[0].strip()

                # 检查多行注释开始
                if '/*' in line:
                    # 检查是否同时有注释结束
                    if '*/' in line:
                        line = line.split('/*', 1)[1].split('*/', 1)[1].strip()
                    else:
                        line = line.split('/*', 1)[0].strip()
                        in_multiline_comment = True

                # 如果处理后不为空，则添加到命令列表
                if line:
                    sql_commands.append(line)

    if not sql_commands:
        print("错误：SQL脚本为空或所有行都是注释")
        return

    # 2. 定义错误模式（可根据实际数据库错误信息调整）
    error_patterns = [
        re.compile(r'ERROR', re.IGNORECASE),
        re.compile(r'ERR:', re.IGNORECASE),
        re.compile(r'failed', re.IGNORECASE),
        re.compile(r'exception', re.IGNORECASE),
        re.compile(r'traceback', re.IGNORECASE)
    ]

    # 3. 启动客户端进程
    child = pexpect.spawn(client_path, encoding='utf-8')
    child.logfile = sys.stdout  # 实时打印客户端输出（可选，方便调试）

    try:
        # 4. 等待初始提示符
        child.expect('Rucbase> ', timeout=5)
        print("成功连接到数据库客户端")

        # 5. 逐行执行SQL并记录每条的执行时间
        for idx, sql in enumerate(sql_commands, 1):
            print(f"\n===== 执行第 {idx} 条SQL =====")
            print(f"SQL: {sql}")
            
            # 记录单条SQL开始时间
            sql_start_time = time.time()

            # 发送SQL命令
            child.sendline(sql)

            # 等待响应，设置较长超时时间
            try:
                child.expect('Rucbase> ', timeout=300)
            except pexpect.TIMEOUT:
                print(f"[错误] 第 {idx} 条SQL执行超时: {sql}")
                continue

            # 计算单条SQL执行时间
            sql_end_time = time.time()
            sql_exec_time = sql_end_time - sql_start_time

            # 获取客户端输出并检查是否有错误
            output = child.before
            has_error = False

            for pattern in error_patterns:
                if pattern.search(output):
                    has_error = True
                    break

            if has_error:
                print(f"[错误] 第 {idx} 条SQL执行失败: {sql}")
                print(f"错误输出: {output}")
                print(f"执行时间: {sql_exec_time:.4f} 秒")  # 即使失败也记录时间
            else:
                print(f"[成功] 第 {idx} 条SQL执行完成")
                print(f"执行时间: {sql_exec_time:.4f} 秒")  # 成功时记录时间

    except pexpect.TIMEOUT:
        print("全局超时错误：客户端无响应或连接建立失败！")
    except pexpect.EOF:
        print("客户端意外退出！最后执行的SQL可能存在问题")
    finally:
        child.close()
        # 计算并打印总执行时间
        total_end_time = time.time()
        total_exec_time = total_end_time - total_start_time
        print(f"\n===== SQL执行批次完成 =====")
        print(f"总执行时间: {total_exec_time:.2f} 秒")  # 保留两位小数
        print(f"执行SQL总数: {len(sql_commands)} 条")
        # 计算平均执行时间
        if len(sql_commands) > 0:
            avg_exec_time = total_exec_time / len(sql_commands)
            print(f"平均每条SQL执行时间: {avg_exec_time:.4f} 秒")


if __name__ == "__main__":
    # 配置参数（请根据实际情况修改）
    SQL_FILE = "dbms_load/dbms_load/load_data.sql"  # SQL脚本路径
    CLIENT_EXEC = "./rmdb_client/build/rmdb_client"  # 客户端可执行文件路径

    if len(sys.argv) > 1:
        SQL_FILE = sys.argv[1]
    if len(sys.argv) > 2:
        CLIENT_EXEC = sys.argv[2]

    batch_execute_sql(SQL_FILE, CLIENT_EXEC)
