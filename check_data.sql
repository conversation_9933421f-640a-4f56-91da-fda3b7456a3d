-- 检查数据库中的数据
select COUNT(*) as count_order_line from order_line;

-- 检查order_line表中的数据分布
select ol_w_id, ol_d_id, ol_o_id, COUNT(*) as count 
from order_line 
group by ol_w_id, ol_d_id, ol_o_id 
order by ol_w_id, ol_d_id, ol_o_id 
limit 10;

-- 检查是否有ol_o_id=1 and ol_d_id=2的数据
select * from order_line where ol_o_id=1 and ol_d_id=2 limit 5;

-- 检查ol_o_id和ol_d_id的范围
select MIN(ol_o_id) as min_o_id, MAX(ol_o_id) as max_o_id from order_line;
select MIN(ol_d_id) as min_d_id, MAX(ol_d_id) as max_d_id from order_line;
select MIN(ol_w_id) as min_w_id, MAX(ol_w_id) as max_w_id from order_line;
