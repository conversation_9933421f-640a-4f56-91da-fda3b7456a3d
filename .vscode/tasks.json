{"tasks": [{"type": "cppbuild", "label": "C/C++: g++ 生成活动文件", "command": "/usr/bin/g++", "args": ["-fdiagnostics-color=always", "-g", "${file}", "-o", "${fileDirname}/${fileBasenameNoExtension}"], "options": {"cwd": "${fileDirname}"}, "problemMatcher": ["$gcc"], "group": {"kind": "build", "isDefault": true}, "detail": "调试器生成的任务。"}, {"label": "build tpcc_start", "type": "shell", "command": "cmake", "args": ["--build", "${workspaceFolder}/build", "--target", "tpcc_start"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": "$gcc"}], "version": "2.0.0"}