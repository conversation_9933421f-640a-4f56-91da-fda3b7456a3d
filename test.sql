create table warehouse (w_id int, name char(8)); 
insert into warehouse values (10, 'qweruiop'); 
insert into warehouse values (534, 'asdfhjkl'); 
insert into warehouse values (100,'qwerghjk'); 
insert into warehouse values (500,'bgtyhnmj'); 
create index warehouse(w_id); 
select * from warehouse where w_id = 10; 
select * from warehouse where w_id < 534 and w_id > 100; 
drop index warehouse(w_id); 
create index warehouse(name); 
select * from warehouse where name = 'qweruiop'; 
select * from warehouse where name > 'qwerghjk'; 
select * from warehouse where name > 'aszdefgh' and name < 'qweraaaa'; 
drop index warehouse(name); 
create index warehouse(w_id,name); 
select * from warehouse where w_id = 100 and name = 'qwerghjk'; 
select * from warehouse where w_id < 600 and name > 'bztyhnmj';
drop index warehouse(w_id,name);
drop table warehouse;


create table warehouse (w_id int, name char(8)); 
insert into warehouse values (10 , 'qweruiop'); 
insert into warehouse values (534, 'asdfhjkl'); 
select * from warehouse where w_id = 10; 
select * from warehouse where w_id < 534 and w_id > 100; 
create index warehouse(w_id); 
select * from warehouse where w_id = 10; 
select * from warehouse where w_id < 534 and w_id > 100; 
drop index warehouse(w_id); 
create index warehouse(w_id,name); 
select * from warehouse;
drop index warehouse(w_id,name);
drop table warehouse;



create table warehouse (w_id int, name char(8)); 
insert into warehouse values (10, 'qweruiop'); 
insert into warehouse values (534, 'asdfhjkl'); 
insert into warehouse values (100, 'qwerghjk'); 
insert into warehouse values (500, 'bgtyhnmj'); 
insert into warehouse values (200, 'zxcvbnml'); 
insert into warehouse values (300, 'tyuiopas'); 
insert into warehouse values (150, 'kijuhygt');
insert into warehouse values (250, 'plmnbvcx');
insert into warehouse values (350, 'mnbvcxza');
insert into warehouse values (450, 'lkjhgdsa');


create index warehouse(w_id); 
select * from warehouse where w_id = 200; 
select * from warehouse where w_id > 100 and w_id < 300; 
select * from warehouse where w_id >= 300 and w_id <= 500; 
select * from warehouse where w_id > 400; 
select * from warehouse where w_id < 150; 
select * from warehouse where w_id = 100 and w_id > 500; 
select * from warehouse where w_id > 200 and name = 'plmnbvcx';
-- insert into warehouse values (10, 'asdfghjk'); -- 触发唯一冲突，向output.txt输入failure
-- update warehouse set w_id = 100 where w_id = 500; -- 触发唯一冲突，向output.txt输入failure
-- update warehouse set w_id = 400 where w_id = 300; 
drop index warehouse(w_id); 



create index warehouse(name); 


select * from warehouse where name = 'tyuiopas'; 
select * from warehouse where name > 'asdfhjkl' and name < 'qwerghjk'; 
select * from warehouse where name >= 'qwerghjk' and name <= 'zxcvbnml'; 
select * from warehouse where name > 'qweruiop'; 
select * from warehouse where name < 'bgtyhnmj'; 
select * from warehouse where name > 'a' and name < 'd'; 
select * from warehouse where name > 'mnbvcxza' and w_id < 400;
-- insert into warehouse values (600, 'qweruiop'); -- 触发唯一冲突，向output.txt输入failure
-- update warehouse set name = 'asdfhjkl' where w_id = 200; -- 触发唯一冲突，向output.txt输入failure
-- update warehouse set name = 'hjklqwer' where w_id = 200; 
drop index warehouse(name); 

create index warehouse(w_id,name); 
select * from warehouse where w_id = 100 and name = 'qwerghjk'; 
select * from warehouse where w_id = 534 and name > 'asdfhjkl' and name < 'zxcvbnml'; 
select * from warehouse where w_id > 10 and w_id < 200 and name = 'hjklqwer'; 
select * from warehouse where w_id > 100 and w_id < 500 and name > 'kijuhygt' and name < 'mnbvcxza';
select * from warehouse where w_id = 250 and name > 'a' and name < 'z';
select * from warehouse where w_id > 300 and w_id < 400 and name = 'mnbvcxza';
-- insert into warehouse values (10, 'qweruiop'); -- 触发唯一冲突，向output.txt输入failure
-- insert into warehouse values (600, 'qazwsxed'); 
-- update warehouse set w_id = 10, name = 'qweruiop' where w_id = 600; -- 触发唯一冲突，向output.txt输入failure
-- update warehouse set w_id = 650, name = 'edcrfvbg' where w_id = 600; 
drop index warehouse(w_id,name);