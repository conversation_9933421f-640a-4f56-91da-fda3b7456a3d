# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/db2025/db2025-x1

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/db2025/db2025-x1/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	/usr/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test

.PHONY : test/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/db2025/db2025-x1/build && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles /home/<USER>/db2025/db2025-x1/build/src/execution/CMakeFiles/progress.marks
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f CMakeFiles/Makefile2 src/execution/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f CMakeFiles/Makefile2 src/execution/clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f CMakeFiles/Makefile2 src/execution/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f CMakeFiles/Makefile2 src/execution/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/db2025/db2025-x1/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
src/execution/CMakeFiles/execution.dir/rule:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f CMakeFiles/Makefile2 src/execution/CMakeFiles/execution.dir/rule
.PHONY : src/execution/CMakeFiles/execution.dir/rule

# Convenience name for target.
execution: src/execution/CMakeFiles/execution.dir/rule

.PHONY : execution

# fast build rule for target.
execution/fast:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f src/execution/CMakeFiles/execution.dir/build.make src/execution/CMakeFiles/execution.dir/build
.PHONY : execution/fast

execution_common.o: execution_common.cpp.o

.PHONY : execution_common.o

# target to build an object file
execution_common.cpp.o:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f src/execution/CMakeFiles/execution.dir/build.make src/execution/CMakeFiles/execution.dir/execution_common.cpp.o
.PHONY : execution_common.cpp.o

execution_common.i: execution_common.cpp.i

.PHONY : execution_common.i

# target to preprocess a source file
execution_common.cpp.i:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f src/execution/CMakeFiles/execution.dir/build.make src/execution/CMakeFiles/execution.dir/execution_common.cpp.i
.PHONY : execution_common.cpp.i

execution_common.s: execution_common.cpp.s

.PHONY : execution_common.s

# target to generate assembly for a file
execution_common.cpp.s:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f src/execution/CMakeFiles/execution.dir/build.make src/execution/CMakeFiles/execution.dir/execution_common.cpp.s
.PHONY : execution_common.cpp.s

execution_explain.o: execution_explain.cpp.o

.PHONY : execution_explain.o

# target to build an object file
execution_explain.cpp.o:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f src/execution/CMakeFiles/execution.dir/build.make src/execution/CMakeFiles/execution.dir/execution_explain.cpp.o
.PHONY : execution_explain.cpp.o

execution_explain.i: execution_explain.cpp.i

.PHONY : execution_explain.i

# target to preprocess a source file
execution_explain.cpp.i:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f src/execution/CMakeFiles/execution.dir/build.make src/execution/CMakeFiles/execution.dir/execution_explain.cpp.i
.PHONY : execution_explain.cpp.i

execution_explain.s: execution_explain.cpp.s

.PHONY : execution_explain.s

# target to generate assembly for a file
execution_explain.cpp.s:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f src/execution/CMakeFiles/execution.dir/build.make src/execution/CMakeFiles/execution.dir/execution_explain.cpp.s
.PHONY : execution_explain.cpp.s

execution_manager.o: execution_manager.cpp.o

.PHONY : execution_manager.o

# target to build an object file
execution_manager.cpp.o:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f src/execution/CMakeFiles/execution.dir/build.make src/execution/CMakeFiles/execution.dir/execution_manager.cpp.o
.PHONY : execution_manager.cpp.o

execution_manager.i: execution_manager.cpp.i

.PHONY : execution_manager.i

# target to preprocess a source file
execution_manager.cpp.i:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f src/execution/CMakeFiles/execution.dir/build.make src/execution/CMakeFiles/execution.dir/execution_manager.cpp.i
.PHONY : execution_manager.cpp.i

execution_manager.s: execution_manager.cpp.s

.PHONY : execution_manager.s

# target to generate assembly for a file
execution_manager.cpp.s:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f src/execution/CMakeFiles/execution.dir/build.make src/execution/CMakeFiles/execution.dir/execution_manager.cpp.s
.PHONY : execution_manager.cpp.s

executor_aggregation.o: executor_aggregation.cpp.o

.PHONY : executor_aggregation.o

# target to build an object file
executor_aggregation.cpp.o:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f src/execution/CMakeFiles/execution.dir/build.make src/execution/CMakeFiles/execution.dir/executor_aggregation.cpp.o
.PHONY : executor_aggregation.cpp.o

executor_aggregation.i: executor_aggregation.cpp.i

.PHONY : executor_aggregation.i

# target to preprocess a source file
executor_aggregation.cpp.i:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f src/execution/CMakeFiles/execution.dir/build.make src/execution/CMakeFiles/execution.dir/executor_aggregation.cpp.i
.PHONY : executor_aggregation.cpp.i

executor_aggregation.s: executor_aggregation.cpp.s

.PHONY : executor_aggregation.s

# target to generate assembly for a file
executor_aggregation.cpp.s:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f src/execution/CMakeFiles/execution.dir/build.make src/execution/CMakeFiles/execution.dir/executor_aggregation.cpp.s
.PHONY : executor_aggregation.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... install/local"
	@echo "... install"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... edit_cache"
	@echo "... test"
	@echo "... install/strip"
	@echo "... execution"
	@echo "... execution_common.o"
	@echo "... execution_common.i"
	@echo "... execution_common.s"
	@echo "... execution_explain.o"
	@echo "... execution_explain.i"
	@echo "... execution_explain.s"
	@echo "... execution_manager.o"
	@echo "... execution_manager.i"
	@echo "... execution_manager.s"
	@echo "... executor_aggregation.o"
	@echo "... executor_aggregation.i"
	@echo "... executor_aggregation.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/db2025/db2025-x1/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

