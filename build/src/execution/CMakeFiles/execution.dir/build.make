# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/db2025/db2025-x1

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/db2025/db2025-x1/build

# Include any dependencies generated for this target.
include src/execution/CMakeFiles/execution.dir/depend.make

# Include the progress variables for this target.
include src/execution/CMakeFiles/execution.dir/progress.make

# Include the compile flags for this target's objects.
include src/execution/CMakeFiles/execution.dir/flags.make

src/execution/CMakeFiles/execution.dir/execution_manager.cpp.o: src/execution/CMakeFiles/execution.dir/flags.make
src/execution/CMakeFiles/execution.dir/execution_manager.cpp.o: ../src/execution/execution_manager.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object src/execution/CMakeFiles/execution.dir/execution_manager.cpp.o"
	cd /home/<USER>/db2025/db2025-x1/build/src/execution && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/execution.dir/execution_manager.cpp.o -c /home/<USER>/db2025/db2025-x1/src/execution/execution_manager.cpp

src/execution/CMakeFiles/execution.dir/execution_manager.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/execution.dir/execution_manager.cpp.i"
	cd /home/<USER>/db2025/db2025-x1/build/src/execution && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/db2025/db2025-x1/src/execution/execution_manager.cpp > CMakeFiles/execution.dir/execution_manager.cpp.i

src/execution/CMakeFiles/execution.dir/execution_manager.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/execution.dir/execution_manager.cpp.s"
	cd /home/<USER>/db2025/db2025-x1/build/src/execution && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/db2025/db2025-x1/src/execution/execution_manager.cpp -o CMakeFiles/execution.dir/execution_manager.cpp.s

src/execution/CMakeFiles/execution.dir/execution_explain.cpp.o: src/execution/CMakeFiles/execution.dir/flags.make
src/execution/CMakeFiles/execution.dir/execution_explain.cpp.o: ../src/execution/execution_explain.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object src/execution/CMakeFiles/execution.dir/execution_explain.cpp.o"
	cd /home/<USER>/db2025/db2025-x1/build/src/execution && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/execution.dir/execution_explain.cpp.o -c /home/<USER>/db2025/db2025-x1/src/execution/execution_explain.cpp

src/execution/CMakeFiles/execution.dir/execution_explain.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/execution.dir/execution_explain.cpp.i"
	cd /home/<USER>/db2025/db2025-x1/build/src/execution && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/db2025/db2025-x1/src/execution/execution_explain.cpp > CMakeFiles/execution.dir/execution_explain.cpp.i

src/execution/CMakeFiles/execution.dir/execution_explain.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/execution.dir/execution_explain.cpp.s"
	cd /home/<USER>/db2025/db2025-x1/build/src/execution && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/db2025/db2025-x1/src/execution/execution_explain.cpp -o CMakeFiles/execution.dir/execution_explain.cpp.s

src/execution/CMakeFiles/execution.dir/executor_aggregation.cpp.o: src/execution/CMakeFiles/execution.dir/flags.make
src/execution/CMakeFiles/execution.dir/executor_aggregation.cpp.o: ../src/execution/executor_aggregation.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object src/execution/CMakeFiles/execution.dir/executor_aggregation.cpp.o"
	cd /home/<USER>/db2025/db2025-x1/build/src/execution && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/execution.dir/executor_aggregation.cpp.o -c /home/<USER>/db2025/db2025-x1/src/execution/executor_aggregation.cpp

src/execution/CMakeFiles/execution.dir/executor_aggregation.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/execution.dir/executor_aggregation.cpp.i"
	cd /home/<USER>/db2025/db2025-x1/build/src/execution && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/db2025/db2025-x1/src/execution/executor_aggregation.cpp > CMakeFiles/execution.dir/executor_aggregation.cpp.i

src/execution/CMakeFiles/execution.dir/executor_aggregation.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/execution.dir/executor_aggregation.cpp.s"
	cd /home/<USER>/db2025/db2025-x1/build/src/execution && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/db2025/db2025-x1/src/execution/executor_aggregation.cpp -o CMakeFiles/execution.dir/executor_aggregation.cpp.s

src/execution/CMakeFiles/execution.dir/execution_common.cpp.o: src/execution/CMakeFiles/execution.dir/flags.make
src/execution/CMakeFiles/execution.dir/execution_common.cpp.o: ../src/execution/execution_common.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object src/execution/CMakeFiles/execution.dir/execution_common.cpp.o"
	cd /home/<USER>/db2025/db2025-x1/build/src/execution && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/execution.dir/execution_common.cpp.o -c /home/<USER>/db2025/db2025-x1/src/execution/execution_common.cpp

src/execution/CMakeFiles/execution.dir/execution_common.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/execution.dir/execution_common.cpp.i"
	cd /home/<USER>/db2025/db2025-x1/build/src/execution && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/db2025/db2025-x1/src/execution/execution_common.cpp > CMakeFiles/execution.dir/execution_common.cpp.i

src/execution/CMakeFiles/execution.dir/execution_common.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/execution.dir/execution_common.cpp.s"
	cd /home/<USER>/db2025/db2025-x1/build/src/execution && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/db2025/db2025-x1/src/execution/execution_common.cpp -o CMakeFiles/execution.dir/execution_common.cpp.s

# Object files for target execution
execution_OBJECTS = \
"CMakeFiles/execution.dir/execution_manager.cpp.o" \
"CMakeFiles/execution.dir/execution_explain.cpp.o" \
"CMakeFiles/execution.dir/executor_aggregation.cpp.o" \
"CMakeFiles/execution.dir/execution_common.cpp.o"

# External object files for target execution
execution_EXTERNAL_OBJECTS =

lib/libexecution.a: src/execution/CMakeFiles/execution.dir/execution_manager.cpp.o
lib/libexecution.a: src/execution/CMakeFiles/execution.dir/execution_explain.cpp.o
lib/libexecution.a: src/execution/CMakeFiles/execution.dir/executor_aggregation.cpp.o
lib/libexecution.a: src/execution/CMakeFiles/execution.dir/execution_common.cpp.o
lib/libexecution.a: src/execution/CMakeFiles/execution.dir/build.make
lib/libexecution.a: src/execution/CMakeFiles/execution.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Linking CXX static library ../../lib/libexecution.a"
	cd /home/<USER>/db2025/db2025-x1/build/src/execution && $(CMAKE_COMMAND) -P CMakeFiles/execution.dir/cmake_clean_target.cmake
	cd /home/<USER>/db2025/db2025-x1/build/src/execution && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/execution.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
src/execution/CMakeFiles/execution.dir/build: lib/libexecution.a

.PHONY : src/execution/CMakeFiles/execution.dir/build

src/execution/CMakeFiles/execution.dir/clean:
	cd /home/<USER>/db2025/db2025-x1/build/src/execution && $(CMAKE_COMMAND) -P CMakeFiles/execution.dir/cmake_clean.cmake
.PHONY : src/execution/CMakeFiles/execution.dir/clean

src/execution/CMakeFiles/execution.dir/depend:
	cd /home/<USER>/db2025/db2025-x1/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/db2025/db2025-x1 /home/<USER>/db2025/db2025-x1/src/execution /home/<USER>/db2025/db2025-x1/build /home/<USER>/db2025/db2025-x1/build/src/execution /home/<USER>/db2025/db2025-x1/build/src/execution/CMakeFiles/execution.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : src/execution/CMakeFiles/execution.dir/depend

