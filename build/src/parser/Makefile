# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/db2025/db2025-x1

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/db2025/db2025-x1/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	/usr/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test

.PHONY : test/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/db2025/db2025-x1/build && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles /home/<USER>/db2025/db2025-x1/build/src/parser/CMakeFiles/progress.marks
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f CMakeFiles/Makefile2 src/parser/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f CMakeFiles/Makefile2 src/parser/clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f CMakeFiles/Makefile2 src/parser/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f CMakeFiles/Makefile2 src/parser/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/db2025/db2025-x1/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
src/parser/CMakeFiles/test_parser.dir/rule:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f CMakeFiles/Makefile2 src/parser/CMakeFiles/test_parser.dir/rule
.PHONY : src/parser/CMakeFiles/test_parser.dir/rule

# Convenience name for target.
test_parser: src/parser/CMakeFiles/test_parser.dir/rule

.PHONY : test_parser

# fast build rule for target.
test_parser/fast:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f src/parser/CMakeFiles/test_parser.dir/build.make src/parser/CMakeFiles/test_parser.dir/build
.PHONY : test_parser/fast

# Convenience name for target.
src/parser/CMakeFiles/parser.dir/rule:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f CMakeFiles/Makefile2 src/parser/CMakeFiles/parser.dir/rule
.PHONY : src/parser/CMakeFiles/parser.dir/rule

# Convenience name for target.
parser: src/parser/CMakeFiles/parser.dir/rule

.PHONY : parser

# fast build rule for target.
parser/fast:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f src/parser/CMakeFiles/parser.dir/build.make src/parser/CMakeFiles/parser.dir/build
.PHONY : parser/fast

ast.o: ast.cpp.o

.PHONY : ast.o

# target to build an object file
ast.cpp.o:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f src/parser/CMakeFiles/parser.dir/build.make src/parser/CMakeFiles/parser.dir/ast.cpp.o
.PHONY : ast.cpp.o

ast.i: ast.cpp.i

.PHONY : ast.i

# target to preprocess a source file
ast.cpp.i:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f src/parser/CMakeFiles/parser.dir/build.make src/parser/CMakeFiles/parser.dir/ast.cpp.i
.PHONY : ast.cpp.i

ast.s: ast.cpp.s

.PHONY : ast.s

# target to generate assembly for a file
ast.cpp.s:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f src/parser/CMakeFiles/parser.dir/build.make src/parser/CMakeFiles/parser.dir/ast.cpp.s
.PHONY : ast.cpp.s

lex.yy.o: lex.yy.cpp.o

.PHONY : lex.yy.o

# target to build an object file
lex.yy.cpp.o:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f src/parser/CMakeFiles/parser.dir/build.make src/parser/CMakeFiles/parser.dir/lex.yy.cpp.o
.PHONY : lex.yy.cpp.o

lex.yy.i: lex.yy.cpp.i

.PHONY : lex.yy.i

# target to preprocess a source file
lex.yy.cpp.i:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f src/parser/CMakeFiles/parser.dir/build.make src/parser/CMakeFiles/parser.dir/lex.yy.cpp.i
.PHONY : lex.yy.cpp.i

lex.yy.s: lex.yy.cpp.s

.PHONY : lex.yy.s

# target to generate assembly for a file
lex.yy.cpp.s:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f src/parser/CMakeFiles/parser.dir/build.make src/parser/CMakeFiles/parser.dir/lex.yy.cpp.s
.PHONY : lex.yy.cpp.s

test_parser.o: test_parser.cpp.o

.PHONY : test_parser.o

# target to build an object file
test_parser.cpp.o:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f src/parser/CMakeFiles/test_parser.dir/build.make src/parser/CMakeFiles/test_parser.dir/test_parser.cpp.o
.PHONY : test_parser.cpp.o

test_parser.i: test_parser.cpp.i

.PHONY : test_parser.i

# target to preprocess a source file
test_parser.cpp.i:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f src/parser/CMakeFiles/test_parser.dir/build.make src/parser/CMakeFiles/test_parser.dir/test_parser.cpp.i
.PHONY : test_parser.cpp.i

test_parser.s: test_parser.cpp.s

.PHONY : test_parser.s

# target to generate assembly for a file
test_parser.cpp.s:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f src/parser/CMakeFiles/test_parser.dir/build.make src/parser/CMakeFiles/test_parser.dir/test_parser.cpp.s
.PHONY : test_parser.cpp.s

yacc.tab.o: yacc.tab.cpp.o

.PHONY : yacc.tab.o

# target to build an object file
yacc.tab.cpp.o:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f src/parser/CMakeFiles/parser.dir/build.make src/parser/CMakeFiles/parser.dir/yacc.tab.cpp.o
.PHONY : yacc.tab.cpp.o

yacc.tab.i: yacc.tab.cpp.i

.PHONY : yacc.tab.i

# target to preprocess a source file
yacc.tab.cpp.i:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f src/parser/CMakeFiles/parser.dir/build.make src/parser/CMakeFiles/parser.dir/yacc.tab.cpp.i
.PHONY : yacc.tab.cpp.i

yacc.tab.s: yacc.tab.cpp.s

.PHONY : yacc.tab.s

# target to generate assembly for a file
yacc.tab.cpp.s:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f src/parser/CMakeFiles/parser.dir/build.make src/parser/CMakeFiles/parser.dir/yacc.tab.cpp.s
.PHONY : yacc.tab.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... install/strip"
	@echo "... install/local"
	@echo "... edit_cache"
	@echo "... test"
	@echo "... test_parser"
	@echo "... install"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... parser"
	@echo "... ast.o"
	@echo "... ast.i"
	@echo "... ast.s"
	@echo "... lex.yy.o"
	@echo "... lex.yy.i"
	@echo "... lex.yy.s"
	@echo "... test_parser.o"
	@echo "... test_parser.i"
	@echo "... test_parser.s"
	@echo "... yacc.tab.o"
	@echo "... yacc.tab.i"
	@echo "... yacc.tab.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/db2025/db2025-x1/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

