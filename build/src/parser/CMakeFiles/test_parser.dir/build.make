# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/db2025/db2025-x1

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/db2025/db2025-x1/build

# Include any dependencies generated for this target.
include src/parser/CMakeFiles/test_parser.dir/depend.make

# Include the progress variables for this target.
include src/parser/CMakeFiles/test_parser.dir/progress.make

# Include the compile flags for this target's objects.
include src/parser/CMakeFiles/test_parser.dir/flags.make

src/parser/CMakeFiles/test_parser.dir/test_parser.cpp.o: src/parser/CMakeFiles/test_parser.dir/flags.make
src/parser/CMakeFiles/test_parser.dir/test_parser.cpp.o: ../src/parser/test_parser.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object src/parser/CMakeFiles/test_parser.dir/test_parser.cpp.o"
	cd /home/<USER>/db2025/db2025-x1/build/src/parser && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/test_parser.dir/test_parser.cpp.o -c /home/<USER>/db2025/db2025-x1/src/parser/test_parser.cpp

src/parser/CMakeFiles/test_parser.dir/test_parser.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/test_parser.dir/test_parser.cpp.i"
	cd /home/<USER>/db2025/db2025-x1/build/src/parser && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/db2025/db2025-x1/src/parser/test_parser.cpp > CMakeFiles/test_parser.dir/test_parser.cpp.i

src/parser/CMakeFiles/test_parser.dir/test_parser.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/test_parser.dir/test_parser.cpp.s"
	cd /home/<USER>/db2025/db2025-x1/build/src/parser && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/db2025/db2025-x1/src/parser/test_parser.cpp -o CMakeFiles/test_parser.dir/test_parser.cpp.s

# Object files for target test_parser
test_parser_OBJECTS = \
"CMakeFiles/test_parser.dir/test_parser.cpp.o"

# External object files for target test_parser
test_parser_EXTERNAL_OBJECTS =

bin/test_parser: src/parser/CMakeFiles/test_parser.dir/test_parser.cpp.o
bin/test_parser: src/parser/CMakeFiles/test_parser.dir/build.make
bin/test_parser: lib/libparser.a
bin/test_parser: lib/libexecution.a
bin/test_parser: lib/libsystem.a
bin/test_parser: lib/librecord.a
bin/test_parser: lib/libtransaction.a
bin/test_parser: lib/librecovery.a
bin/test_parser: lib/libsystem.a
bin/test_parser: lib/librecord.a
bin/test_parser: lib/libtransaction.a
bin/test_parser: lib/librecovery.a
bin/test_parser: lib/libindex.a
bin/test_parser: lib/libstorage.a
bin/test_parser: lib/libplanner.a
bin/test_parser: src/parser/CMakeFiles/test_parser.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable ../../bin/test_parser"
	cd /home/<USER>/db2025/db2025-x1/build/src/parser && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/test_parser.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
src/parser/CMakeFiles/test_parser.dir/build: bin/test_parser

.PHONY : src/parser/CMakeFiles/test_parser.dir/build

src/parser/CMakeFiles/test_parser.dir/clean:
	cd /home/<USER>/db2025/db2025-x1/build/src/parser && $(CMAKE_COMMAND) -P CMakeFiles/test_parser.dir/cmake_clean.cmake
.PHONY : src/parser/CMakeFiles/test_parser.dir/clean

src/parser/CMakeFiles/test_parser.dir/depend:
	cd /home/<USER>/db2025/db2025-x1/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/db2025/db2025-x1 /home/<USER>/db2025/db2025-x1/src/parser /home/<USER>/db2025/db2025-x1/build /home/<USER>/db2025/db2025-x1/build/src/parser /home/<USER>/db2025/db2025-x1/build/src/parser/CMakeFiles/test_parser.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : src/parser/CMakeFiles/test_parser.dir/depend

