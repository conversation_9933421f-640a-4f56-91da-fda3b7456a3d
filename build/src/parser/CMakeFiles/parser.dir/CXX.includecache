#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

/home/<USER>/db2025/db2025-x1/src/parser/ast.cpp
ast.h
/home/<USER>/db2025/db2025-x1/src/parser/ast.h

/home/<USER>/db2025/db2025-x1/src/parser/ast.h
vector
-
string
-
memory
-

/home/<USER>/db2025/db2025-x1/src/parser/lex.yy.cpp
stdio.h
-
string.h
-
errno.h
-
stdlib.h
-
inttypes.h
-
ast.h
/home/<USER>/db2025/db2025-x1/src/parser/ast.h
yacc.tab.h
/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.h
iostream
-
unistd.h
-

/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp
ast.h
/home/<USER>/db2025/db2025-x1/src/parser/ast.h
yacc.tab.h
/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.h
iostream
-
memory
-
yacc.tab.h
/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.h
limits.h
-
stdint.h
-
stddef.h
-
stddef.h
-
libintl.h
-
alloca.h
-
malloc.h
-
stdlib.h
-
stdlib.h
-
stdio.h
-

/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.h

