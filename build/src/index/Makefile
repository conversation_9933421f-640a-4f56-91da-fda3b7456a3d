# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/db2025/db2025-x1

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/db2025/db2025-x1/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	/usr/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test

.PHONY : test/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/db2025/db2025-x1/build && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles /home/<USER>/db2025/db2025-x1/build/src/index/CMakeFiles/progress.marks
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f CMakeFiles/Makefile2 src/index/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f CMakeFiles/Makefile2 src/index/clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f CMakeFiles/Makefile2 src/index/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f CMakeFiles/Makefile2 src/index/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/db2025/db2025-x1/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
src/index/CMakeFiles/index.dir/rule:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f CMakeFiles/Makefile2 src/index/CMakeFiles/index.dir/rule
.PHONY : src/index/CMakeFiles/index.dir/rule

# Convenience name for target.
index: src/index/CMakeFiles/index.dir/rule

.PHONY : index

# fast build rule for target.
index/fast:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f src/index/CMakeFiles/index.dir/build.make src/index/CMakeFiles/index.dir/build
.PHONY : index/fast

ix_index_handle.o: ix_index_handle.cpp.o

.PHONY : ix_index_handle.o

# target to build an object file
ix_index_handle.cpp.o:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f src/index/CMakeFiles/index.dir/build.make src/index/CMakeFiles/index.dir/ix_index_handle.cpp.o
.PHONY : ix_index_handle.cpp.o

ix_index_handle.i: ix_index_handle.cpp.i

.PHONY : ix_index_handle.i

# target to preprocess a source file
ix_index_handle.cpp.i:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f src/index/CMakeFiles/index.dir/build.make src/index/CMakeFiles/index.dir/ix_index_handle.cpp.i
.PHONY : ix_index_handle.cpp.i

ix_index_handle.s: ix_index_handle.cpp.s

.PHONY : ix_index_handle.s

# target to generate assembly for a file
ix_index_handle.cpp.s:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f src/index/CMakeFiles/index.dir/build.make src/index/CMakeFiles/index.dir/ix_index_handle.cpp.s
.PHONY : ix_index_handle.cpp.s

ix_scan.o: ix_scan.cpp.o

.PHONY : ix_scan.o

# target to build an object file
ix_scan.cpp.o:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f src/index/CMakeFiles/index.dir/build.make src/index/CMakeFiles/index.dir/ix_scan.cpp.o
.PHONY : ix_scan.cpp.o

ix_scan.i: ix_scan.cpp.i

.PHONY : ix_scan.i

# target to preprocess a source file
ix_scan.cpp.i:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f src/index/CMakeFiles/index.dir/build.make src/index/CMakeFiles/index.dir/ix_scan.cpp.i
.PHONY : ix_scan.cpp.i

ix_scan.s: ix_scan.cpp.s

.PHONY : ix_scan.s

# target to generate assembly for a file
ix_scan.cpp.s:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f src/index/CMakeFiles/index.dir/build.make src/index/CMakeFiles/index.dir/ix_scan.cpp.s
.PHONY : ix_scan.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... install/strip"
	@echo "... install"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... edit_cache"
	@echo "... test"
	@echo "... install/local"
	@echo "... index"
	@echo "... ix_index_handle.o"
	@echo "... ix_index_handle.i"
	@echo "... ix_index_handle.s"
	@echo "... ix_scan.o"
	@echo "... ix_scan.i"
	@echo "... ix_scan.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/db2025/db2025-x1/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

