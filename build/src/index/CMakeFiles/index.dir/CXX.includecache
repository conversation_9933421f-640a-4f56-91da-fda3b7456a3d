#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

../src/common/common.h
cassert
-
cstring
-
memory
-
string
-
vector
-
defs.h
../src/common/defs.h
record/rm_defs.h
../src/common/record/rm_defs.h
system/sm_meta.h
../src/common/system/sm_meta.h
parser/ast.h
../src/common/parser/ast.h

../src/common/config.h
atomic
-
chrono
-
cstdint
-

../src/defs.h
iostream
-
map
-

../src/errors.h
cerrno
-
cstring
-
string
-
vector
-

../src/parser/ast.h
vector
-
string
-
memory
-

../src/record/rm_defs.h
defs.h
../src/record/defs.h
storage/buffer_pool_manager.h
../src/record/storage/buffer_pool_manager.h
system/sm_meta.h
../src/record/system/sm_meta.h

../src/replacer/lru_replacer.h
list
-
mutex
-
vector
-
common/config.h
../src/replacer/common/config.h
replacer/replacer.h
../src/replacer/replacer/replacer.h
unordered_map
../src/replacer/unordered_map

../src/replacer/replacer.h
common/config.h
../src/replacer/common/config.h

../src/storage/buffer_pool_manager.h
fcntl.h
-
unistd.h
-
cassert
-
list
-
unordered_map
-
vector
-
disk_manager.h
../src/storage/disk_manager.h
errors.h
../src/storage/errors.h
page.h
../src/storage/page.h
replacer/lru_replacer.h
../src/storage/replacer/lru_replacer.h
replacer/replacer.h
../src/storage/replacer/replacer.h

../src/storage/disk_manager.h
fcntl.h
-
sys/stat.h
-
unistd.h
-
atomic
-
fstream
-
iostream
-
string
-
unordered_map
-
common/config.h
../src/storage/common/config.h
errors.h
../src/storage/errors.h

../src/storage/page.h
common/config.h
../src/storage/common/config.h
shared_mutex
-

../src/system/sm_defs.h
defs.h
../src/system/defs.h
string
-

../src/system/sm_meta.h
algorithm
-
iostream
-
map
-
string
-
vector
-
errors.h
../src/system/errors.h
sm_defs.h
../src/system/sm_defs.h

../src/transaction/transaction.h
atomic
-
deque
-
memory
-
string
-
thread
-
unordered_set
-
vector
-
common/common.h
../src/transaction/common/common.h
transaction/txn_defs.h
../src/transaction/transaction/txn_defs.h
record/rm_defs.h
../src/transaction/record/rm_defs.h

../src/transaction/txn_defs.h
atomic
-
common/config.h
../src/transaction/common/config.h
defs.h
../src/transaction/defs.h
record/rm_defs.h
../src/transaction/record/rm_defs.h

/home/<USER>/db2025/db2025-x1/src/index/ix_defs.h
vector
-
defs.h
/home/<USER>/db2025/db2025-x1/src/index/defs.h
storage/buffer_pool_manager.h
/home/<USER>/db2025/db2025-x1/src/index/storage/buffer_pool_manager.h

/home/<USER>/db2025/db2025-x1/src/index/ix_index_handle.cpp
ix_index_handle.h
/home/<USER>/db2025/db2025-x1/src/index/ix_index_handle.h
ix_scan.h
/home/<USER>/db2025/db2025-x1/src/index/ix_scan.h

/home/<USER>/db2025/db2025-x1/src/index/ix_index_handle.h
ix_defs.h
/home/<USER>/db2025/db2025-x1/src/index/ix_defs.h
transaction/transaction.h
/home/<USER>/db2025/db2025-x1/src/index/transaction/transaction.h

/home/<USER>/db2025/db2025-x1/src/index/ix_scan.cpp
ix_scan.h
/home/<USER>/db2025/db2025-x1/src/index/ix_scan.h

/home/<USER>/db2025/db2025-x1/src/index/ix_scan.h
ix_defs.h
/home/<USER>/db2025/db2025-x1/src/index/ix_defs.h
ix_index_handle.h
/home/<USER>/db2025/db2025-x1/src/index/ix_index_handle.h

