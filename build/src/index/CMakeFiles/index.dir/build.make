# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/db2025/db2025-x1

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/db2025/db2025-x1/build

# Include any dependencies generated for this target.
include src/index/CMakeFiles/index.dir/depend.make

# Include the progress variables for this target.
include src/index/CMakeFiles/index.dir/progress.make

# Include the compile flags for this target's objects.
include src/index/CMakeFiles/index.dir/flags.make

src/index/CMakeFiles/index.dir/ix_index_handle.cpp.o: src/index/CMakeFiles/index.dir/flags.make
src/index/CMakeFiles/index.dir/ix_index_handle.cpp.o: ../src/index/ix_index_handle.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object src/index/CMakeFiles/index.dir/ix_index_handle.cpp.o"
	cd /home/<USER>/db2025/db2025-x1/build/src/index && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/index.dir/ix_index_handle.cpp.o -c /home/<USER>/db2025/db2025-x1/src/index/ix_index_handle.cpp

src/index/CMakeFiles/index.dir/ix_index_handle.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/index.dir/ix_index_handle.cpp.i"
	cd /home/<USER>/db2025/db2025-x1/build/src/index && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/db2025/db2025-x1/src/index/ix_index_handle.cpp > CMakeFiles/index.dir/ix_index_handle.cpp.i

src/index/CMakeFiles/index.dir/ix_index_handle.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/index.dir/ix_index_handle.cpp.s"
	cd /home/<USER>/db2025/db2025-x1/build/src/index && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/db2025/db2025-x1/src/index/ix_index_handle.cpp -o CMakeFiles/index.dir/ix_index_handle.cpp.s

src/index/CMakeFiles/index.dir/ix_scan.cpp.o: src/index/CMakeFiles/index.dir/flags.make
src/index/CMakeFiles/index.dir/ix_scan.cpp.o: ../src/index/ix_scan.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object src/index/CMakeFiles/index.dir/ix_scan.cpp.o"
	cd /home/<USER>/db2025/db2025-x1/build/src/index && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/index.dir/ix_scan.cpp.o -c /home/<USER>/db2025/db2025-x1/src/index/ix_scan.cpp

src/index/CMakeFiles/index.dir/ix_scan.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/index.dir/ix_scan.cpp.i"
	cd /home/<USER>/db2025/db2025-x1/build/src/index && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/db2025/db2025-x1/src/index/ix_scan.cpp > CMakeFiles/index.dir/ix_scan.cpp.i

src/index/CMakeFiles/index.dir/ix_scan.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/index.dir/ix_scan.cpp.s"
	cd /home/<USER>/db2025/db2025-x1/build/src/index && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/db2025/db2025-x1/src/index/ix_scan.cpp -o CMakeFiles/index.dir/ix_scan.cpp.s

# Object files for target index
index_OBJECTS = \
"CMakeFiles/index.dir/ix_index_handle.cpp.o" \
"CMakeFiles/index.dir/ix_scan.cpp.o"

# External object files for target index
index_EXTERNAL_OBJECTS =

lib/libindex.a: src/index/CMakeFiles/index.dir/ix_index_handle.cpp.o
lib/libindex.a: src/index/CMakeFiles/index.dir/ix_scan.cpp.o
lib/libindex.a: src/index/CMakeFiles/index.dir/build.make
lib/libindex.a: src/index/CMakeFiles/index.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CXX static library ../../lib/libindex.a"
	cd /home/<USER>/db2025/db2025-x1/build/src/index && $(CMAKE_COMMAND) -P CMakeFiles/index.dir/cmake_clean_target.cmake
	cd /home/<USER>/db2025/db2025-x1/build/src/index && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/index.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
src/index/CMakeFiles/index.dir/build: lib/libindex.a

.PHONY : src/index/CMakeFiles/index.dir/build

src/index/CMakeFiles/index.dir/clean:
	cd /home/<USER>/db2025/db2025-x1/build/src/index && $(CMAKE_COMMAND) -P CMakeFiles/index.dir/cmake_clean.cmake
.PHONY : src/index/CMakeFiles/index.dir/clean

src/index/CMakeFiles/index.dir/depend:
	cd /home/<USER>/db2025/db2025-x1/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/db2025/db2025-x1 /home/<USER>/db2025/db2025-x1/src/index /home/<USER>/db2025/db2025-x1/build /home/<USER>/db2025/db2025-x1/build/src/index /home/<USER>/db2025/db2025-x1/build/src/index/CMakeFiles/index.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : src/index/CMakeFiles/index.dir/depend

