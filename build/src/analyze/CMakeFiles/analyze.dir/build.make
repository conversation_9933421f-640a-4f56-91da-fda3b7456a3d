# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/db2025/db2025-x1

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/db2025/db2025-x1/build

# Include any dependencies generated for this target.
include src/analyze/CMakeFiles/analyze.dir/depend.make

# Include the progress variables for this target.
include src/analyze/CMakeFiles/analyze.dir/progress.make

# Include the compile flags for this target's objects.
include src/analyze/CMakeFiles/analyze.dir/flags.make

src/analyze/CMakeFiles/analyze.dir/analyze.cpp.o: src/analyze/CMakeFiles/analyze.dir/flags.make
src/analyze/CMakeFiles/analyze.dir/analyze.cpp.o: ../src/analyze/analyze.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object src/analyze/CMakeFiles/analyze.dir/analyze.cpp.o"
	cd /home/<USER>/db2025/db2025-x1/build/src/analyze && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/analyze.dir/analyze.cpp.o -c /home/<USER>/db2025/db2025-x1/src/analyze/analyze.cpp

src/analyze/CMakeFiles/analyze.dir/analyze.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/analyze.dir/analyze.cpp.i"
	cd /home/<USER>/db2025/db2025-x1/build/src/analyze && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/db2025/db2025-x1/src/analyze/analyze.cpp > CMakeFiles/analyze.dir/analyze.cpp.i

src/analyze/CMakeFiles/analyze.dir/analyze.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/analyze.dir/analyze.cpp.s"
	cd /home/<USER>/db2025/db2025-x1/build/src/analyze && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/db2025/db2025-x1/src/analyze/analyze.cpp -o CMakeFiles/analyze.dir/analyze.cpp.s

# Object files for target analyze
analyze_OBJECTS = \
"CMakeFiles/analyze.dir/analyze.cpp.o"

# External object files for target analyze
analyze_EXTERNAL_OBJECTS =

lib/libanalyze.a: src/analyze/CMakeFiles/analyze.dir/analyze.cpp.o
lib/libanalyze.a: src/analyze/CMakeFiles/analyze.dir/build.make
lib/libanalyze.a: src/analyze/CMakeFiles/analyze.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX static library ../../lib/libanalyze.a"
	cd /home/<USER>/db2025/db2025-x1/build/src/analyze && $(CMAKE_COMMAND) -P CMakeFiles/analyze.dir/cmake_clean_target.cmake
	cd /home/<USER>/db2025/db2025-x1/build/src/analyze && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/analyze.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
src/analyze/CMakeFiles/analyze.dir/build: lib/libanalyze.a

.PHONY : src/analyze/CMakeFiles/analyze.dir/build

src/analyze/CMakeFiles/analyze.dir/clean:
	cd /home/<USER>/db2025/db2025-x1/build/src/analyze && $(CMAKE_COMMAND) -P CMakeFiles/analyze.dir/cmake_clean.cmake
.PHONY : src/analyze/CMakeFiles/analyze.dir/clean

src/analyze/CMakeFiles/analyze.dir/depend:
	cd /home/<USER>/db2025/db2025-x1/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/db2025/db2025-x1 /home/<USER>/db2025/db2025-x1/src/analyze /home/<USER>/db2025/db2025-x1/build /home/<USER>/db2025/db2025-x1/build/src/analyze /home/<USER>/db2025/db2025-x1/build/src/analyze/CMakeFiles/analyze.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : src/analyze/CMakeFiles/analyze.dir/depend

