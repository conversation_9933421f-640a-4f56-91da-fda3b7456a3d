# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/db2025/db2025-x1

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/db2025/db2025-x1/build

# Include any dependencies generated for this target.
include src/optimizer/CMakeFiles/planner.dir/depend.make

# Include the progress variables for this target.
include src/optimizer/CMakeFiles/planner.dir/progress.make

# Include the compile flags for this target's objects.
include src/optimizer/CMakeFiles/planner.dir/flags.make

src/optimizer/CMakeFiles/planner.dir/planner.cpp.o: src/optimizer/CMakeFiles/planner.dir/flags.make
src/optimizer/CMakeFiles/planner.dir/planner.cpp.o: ../src/optimizer/planner.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object src/optimizer/CMakeFiles/planner.dir/planner.cpp.o"
	cd /home/<USER>/db2025/db2025-x1/build/src/optimizer && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/planner.dir/planner.cpp.o -c /home/<USER>/db2025/db2025-x1/src/optimizer/planner.cpp

src/optimizer/CMakeFiles/planner.dir/planner.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/planner.dir/planner.cpp.i"
	cd /home/<USER>/db2025/db2025-x1/build/src/optimizer && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/db2025/db2025-x1/src/optimizer/planner.cpp > CMakeFiles/planner.dir/planner.cpp.i

src/optimizer/CMakeFiles/planner.dir/planner.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/planner.dir/planner.cpp.s"
	cd /home/<USER>/db2025/db2025-x1/build/src/optimizer && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/db2025/db2025-x1/src/optimizer/planner.cpp -o CMakeFiles/planner.dir/planner.cpp.s

# Object files for target planner
planner_OBJECTS = \
"CMakeFiles/planner.dir/planner.cpp.o"

# External object files for target planner
planner_EXTERNAL_OBJECTS =

lib/libplanner.a: src/optimizer/CMakeFiles/planner.dir/planner.cpp.o
lib/libplanner.a: src/optimizer/CMakeFiles/planner.dir/build.make
lib/libplanner.a: src/optimizer/CMakeFiles/planner.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX static library ../../lib/libplanner.a"
	cd /home/<USER>/db2025/db2025-x1/build/src/optimizer && $(CMAKE_COMMAND) -P CMakeFiles/planner.dir/cmake_clean_target.cmake
	cd /home/<USER>/db2025/db2025-x1/build/src/optimizer && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/planner.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
src/optimizer/CMakeFiles/planner.dir/build: lib/libplanner.a

.PHONY : src/optimizer/CMakeFiles/planner.dir/build

src/optimizer/CMakeFiles/planner.dir/clean:
	cd /home/<USER>/db2025/db2025-x1/build/src/optimizer && $(CMAKE_COMMAND) -P CMakeFiles/planner.dir/cmake_clean.cmake
.PHONY : src/optimizer/CMakeFiles/planner.dir/clean

src/optimizer/CMakeFiles/planner.dir/depend:
	cd /home/<USER>/db2025/db2025-x1/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/db2025/db2025-x1 /home/<USER>/db2025/db2025-x1/src/optimizer /home/<USER>/db2025/db2025-x1/build /home/<USER>/db2025/db2025-x1/build/src/optimizer /home/<USER>/db2025/db2025-x1/build/src/optimizer/CMakeFiles/planner.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : src/optimizer/CMakeFiles/planner.dir/depend

