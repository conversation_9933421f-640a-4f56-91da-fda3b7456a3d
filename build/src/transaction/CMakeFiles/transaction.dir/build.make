# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/db2025/db2025-x1

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/db2025/db2025-x1/build

# Include any dependencies generated for this target.
include src/transaction/CMakeFiles/transaction.dir/depend.make

# Include the progress variables for this target.
include src/transaction/CMakeFiles/transaction.dir/progress.make

# Include the compile flags for this target's objects.
include src/transaction/CMakeFiles/transaction.dir/flags.make

src/transaction/CMakeFiles/transaction.dir/concurrency/lock_manager.cpp.o: src/transaction/CMakeFiles/transaction.dir/flags.make
src/transaction/CMakeFiles/transaction.dir/concurrency/lock_manager.cpp.o: ../src/transaction/concurrency/lock_manager.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object src/transaction/CMakeFiles/transaction.dir/concurrency/lock_manager.cpp.o"
	cd /home/<USER>/db2025/db2025-x1/build/src/transaction && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/transaction.dir/concurrency/lock_manager.cpp.o -c /home/<USER>/db2025/db2025-x1/src/transaction/concurrency/lock_manager.cpp

src/transaction/CMakeFiles/transaction.dir/concurrency/lock_manager.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/transaction.dir/concurrency/lock_manager.cpp.i"
	cd /home/<USER>/db2025/db2025-x1/build/src/transaction && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/db2025/db2025-x1/src/transaction/concurrency/lock_manager.cpp > CMakeFiles/transaction.dir/concurrency/lock_manager.cpp.i

src/transaction/CMakeFiles/transaction.dir/concurrency/lock_manager.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/transaction.dir/concurrency/lock_manager.cpp.s"
	cd /home/<USER>/db2025/db2025-x1/build/src/transaction && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/db2025/db2025-x1/src/transaction/concurrency/lock_manager.cpp -o CMakeFiles/transaction.dir/concurrency/lock_manager.cpp.s

src/transaction/CMakeFiles/transaction.dir/transaction_manager.cpp.o: src/transaction/CMakeFiles/transaction.dir/flags.make
src/transaction/CMakeFiles/transaction.dir/transaction_manager.cpp.o: ../src/transaction/transaction_manager.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object src/transaction/CMakeFiles/transaction.dir/transaction_manager.cpp.o"
	cd /home/<USER>/db2025/db2025-x1/build/src/transaction && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/transaction.dir/transaction_manager.cpp.o -c /home/<USER>/db2025/db2025-x1/src/transaction/transaction_manager.cpp

src/transaction/CMakeFiles/transaction.dir/transaction_manager.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/transaction.dir/transaction_manager.cpp.i"
	cd /home/<USER>/db2025/db2025-x1/build/src/transaction && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/db2025/db2025-x1/src/transaction/transaction_manager.cpp > CMakeFiles/transaction.dir/transaction_manager.cpp.i

src/transaction/CMakeFiles/transaction.dir/transaction_manager.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/transaction.dir/transaction_manager.cpp.s"
	cd /home/<USER>/db2025/db2025-x1/build/src/transaction && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/db2025/db2025-x1/src/transaction/transaction_manager.cpp -o CMakeFiles/transaction.dir/transaction_manager.cpp.s

src/transaction/CMakeFiles/transaction.dir/watermark.cpp.o: src/transaction/CMakeFiles/transaction.dir/flags.make
src/transaction/CMakeFiles/transaction.dir/watermark.cpp.o: ../src/transaction/watermark.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object src/transaction/CMakeFiles/transaction.dir/watermark.cpp.o"
	cd /home/<USER>/db2025/db2025-x1/build/src/transaction && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/transaction.dir/watermark.cpp.o -c /home/<USER>/db2025/db2025-x1/src/transaction/watermark.cpp

src/transaction/CMakeFiles/transaction.dir/watermark.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/transaction.dir/watermark.cpp.i"
	cd /home/<USER>/db2025/db2025-x1/build/src/transaction && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/db2025/db2025-x1/src/transaction/watermark.cpp > CMakeFiles/transaction.dir/watermark.cpp.i

src/transaction/CMakeFiles/transaction.dir/watermark.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/transaction.dir/watermark.cpp.s"
	cd /home/<USER>/db2025/db2025-x1/build/src/transaction && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/db2025/db2025-x1/src/transaction/watermark.cpp -o CMakeFiles/transaction.dir/watermark.cpp.s

# Object files for target transaction
transaction_OBJECTS = \
"CMakeFiles/transaction.dir/concurrency/lock_manager.cpp.o" \
"CMakeFiles/transaction.dir/transaction_manager.cpp.o" \
"CMakeFiles/transaction.dir/watermark.cpp.o"

# External object files for target transaction
transaction_EXTERNAL_OBJECTS =

lib/libtransaction.a: src/transaction/CMakeFiles/transaction.dir/concurrency/lock_manager.cpp.o
lib/libtransaction.a: src/transaction/CMakeFiles/transaction.dir/transaction_manager.cpp.o
lib/libtransaction.a: src/transaction/CMakeFiles/transaction.dir/watermark.cpp.o
lib/libtransaction.a: src/transaction/CMakeFiles/transaction.dir/build.make
lib/libtransaction.a: src/transaction/CMakeFiles/transaction.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Linking CXX static library ../../lib/libtransaction.a"
	cd /home/<USER>/db2025/db2025-x1/build/src/transaction && $(CMAKE_COMMAND) -P CMakeFiles/transaction.dir/cmake_clean_target.cmake
	cd /home/<USER>/db2025/db2025-x1/build/src/transaction && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/transaction.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
src/transaction/CMakeFiles/transaction.dir/build: lib/libtransaction.a

.PHONY : src/transaction/CMakeFiles/transaction.dir/build

src/transaction/CMakeFiles/transaction.dir/clean:
	cd /home/<USER>/db2025/db2025-x1/build/src/transaction && $(CMAKE_COMMAND) -P CMakeFiles/transaction.dir/cmake_clean.cmake
.PHONY : src/transaction/CMakeFiles/transaction.dir/clean

src/transaction/CMakeFiles/transaction.dir/depend:
	cd /home/<USER>/db2025/db2025-x1/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/db2025/db2025-x1 /home/<USER>/db2025/db2025-x1/src/transaction /home/<USER>/db2025/db2025-x1/build /home/<USER>/db2025/db2025-x1/build/src/transaction /home/<USER>/db2025/db2025-x1/build/src/transaction/CMakeFiles/transaction.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : src/transaction/CMakeFiles/transaction.dir/depend

