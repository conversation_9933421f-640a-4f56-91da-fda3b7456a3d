# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/db2025/db2025-x1

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/db2025/db2025-x1/build

# Include any dependencies generated for this target.
include src/CMakeFiles/reconstructuple_test.dir/depend.make

# Include the progress variables for this target.
include src/CMakeFiles/reconstructuple_test.dir/progress.make

# Include the compile flags for this target's objects.
include src/CMakeFiles/reconstructuple_test.dir/flags.make

src/CMakeFiles/reconstructuple_test.dir/reconstructTuple.cpp.o: src/CMakeFiles/reconstructuple_test.dir/flags.make
src/CMakeFiles/reconstructuple_test.dir/reconstructTuple.cpp.o: ../src/reconstructTuple.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object src/CMakeFiles/reconstructuple_test.dir/reconstructTuple.cpp.o"
	cd /home/<USER>/db2025/db2025-x1/build/src && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/reconstructuple_test.dir/reconstructTuple.cpp.o -c /home/<USER>/db2025/db2025-x1/src/reconstructTuple.cpp

src/CMakeFiles/reconstructuple_test.dir/reconstructTuple.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/reconstructuple_test.dir/reconstructTuple.cpp.i"
	cd /home/<USER>/db2025/db2025-x1/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/db2025/db2025-x1/src/reconstructTuple.cpp > CMakeFiles/reconstructuple_test.dir/reconstructTuple.cpp.i

src/CMakeFiles/reconstructuple_test.dir/reconstructTuple.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/reconstructuple_test.dir/reconstructTuple.cpp.s"
	cd /home/<USER>/db2025/db2025-x1/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/db2025/db2025-x1/src/reconstructTuple.cpp -o CMakeFiles/reconstructuple_test.dir/reconstructTuple.cpp.s

# Object files for target reconstructuple_test
reconstructuple_test_OBJECTS = \
"CMakeFiles/reconstructuple_test.dir/reconstructTuple.cpp.o"

# External object files for target reconstructuple_test
reconstructuple_test_EXTERNAL_OBJECTS =

bin/reconstructuple_test: src/CMakeFiles/reconstructuple_test.dir/reconstructTuple.cpp.o
bin/reconstructuple_test: src/CMakeFiles/reconstructuple_test.dir/build.make
bin/reconstructuple_test: lib/libparser.a
bin/reconstructuple_test: lib/libexecution.a
bin/reconstructuple_test: lib/libplanner.a
bin/reconstructuple_test: lib/libanalyze.a
bin/reconstructuple_test: lib/libgtest_main.a
bin/reconstructuple_test: lib/libsystem.a
bin/reconstructuple_test: lib/librecord.a
bin/reconstructuple_test: lib/libtransaction.a
bin/reconstructuple_test: lib/librecovery.a
bin/reconstructuple_test: lib/libsystem.a
bin/reconstructuple_test: lib/librecord.a
bin/reconstructuple_test: lib/libtransaction.a
bin/reconstructuple_test: lib/librecovery.a
bin/reconstructuple_test: lib/libindex.a
bin/reconstructuple_test: lib/libstorage.a
bin/reconstructuple_test: lib/libgtest.a
bin/reconstructuple_test: src/CMakeFiles/reconstructuple_test.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable ../bin/reconstructuple_test"
	cd /home/<USER>/db2025/db2025-x1/build/src && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/reconstructuple_test.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
src/CMakeFiles/reconstructuple_test.dir/build: bin/reconstructuple_test

.PHONY : src/CMakeFiles/reconstructuple_test.dir/build

src/CMakeFiles/reconstructuple_test.dir/clean:
	cd /home/<USER>/db2025/db2025-x1/build/src && $(CMAKE_COMMAND) -P CMakeFiles/reconstructuple_test.dir/cmake_clean.cmake
.PHONY : src/CMakeFiles/reconstructuple_test.dir/clean

src/CMakeFiles/reconstructuple_test.dir/depend:
	cd /home/<USER>/db2025/db2025-x1/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/db2025/db2025-x1 /home/<USER>/db2025/db2025-x1/src /home/<USER>/db2025/db2025-x1/build /home/<USER>/db2025/db2025-x1/build/src /home/<USER>/db2025/db2025-x1/build/src/CMakeFiles/reconstructuple_test.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : src/CMakeFiles/reconstructuple_test.dir/depend

