# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/db2025/db2025-x1

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/db2025/db2025-x1/build

# Include any dependencies generated for this target.
include src/CMakeFiles/rmdb.dir/depend.make

# Include the progress variables for this target.
include src/CMakeFiles/rmdb.dir/progress.make

# Include the compile flags for this target's objects.
include src/CMakeFiles/rmdb.dir/flags.make

src/CMakeFiles/rmdb.dir/rmdb.cpp.o: src/CMakeFiles/rmdb.dir/flags.make
src/CMakeFiles/rmdb.dir/rmdb.cpp.o: ../src/rmdb.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object src/CMakeFiles/rmdb.dir/rmdb.cpp.o"
	cd /home/<USER>/db2025/db2025-x1/build/src && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/rmdb.dir/rmdb.cpp.o -c /home/<USER>/db2025/db2025-x1/src/rmdb.cpp

src/CMakeFiles/rmdb.dir/rmdb.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/rmdb.dir/rmdb.cpp.i"
	cd /home/<USER>/db2025/db2025-x1/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/db2025/db2025-x1/src/rmdb.cpp > CMakeFiles/rmdb.dir/rmdb.cpp.i

src/CMakeFiles/rmdb.dir/rmdb.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/rmdb.dir/rmdb.cpp.s"
	cd /home/<USER>/db2025/db2025-x1/build/src && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/db2025/db2025-x1/src/rmdb.cpp -o CMakeFiles/rmdb.dir/rmdb.cpp.s

# Object files for target rmdb
rmdb_OBJECTS = \
"CMakeFiles/rmdb.dir/rmdb.cpp.o"

# External object files for target rmdb
rmdb_EXTERNAL_OBJECTS =

bin/rmdb: src/CMakeFiles/rmdb.dir/rmdb.cpp.o
bin/rmdb: src/CMakeFiles/rmdb.dir/build.make
bin/rmdb: lib/libparser.a
bin/rmdb: lib/libexecution.a
bin/rmdb: lib/libplanner.a
bin/rmdb: lib/libanalyze.a
bin/rmdb: lib/libsystem.a
bin/rmdb: lib/librecord.a
bin/rmdb: lib/libtransaction.a
bin/rmdb: lib/librecovery.a
bin/rmdb: lib/libsystem.a
bin/rmdb: lib/librecord.a
bin/rmdb: lib/libtransaction.a
bin/rmdb: lib/librecovery.a
bin/rmdb: lib/libindex.a
bin/rmdb: lib/libstorage.a
bin/rmdb: src/CMakeFiles/rmdb.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable ../bin/rmdb"
	cd /home/<USER>/db2025/db2025-x1/build/src && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/rmdb.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
src/CMakeFiles/rmdb.dir/build: bin/rmdb

.PHONY : src/CMakeFiles/rmdb.dir/build

src/CMakeFiles/rmdb.dir/clean:
	cd /home/<USER>/db2025/db2025-x1/build/src && $(CMAKE_COMMAND) -P CMakeFiles/rmdb.dir/cmake_clean.cmake
.PHONY : src/CMakeFiles/rmdb.dir/clean

src/CMakeFiles/rmdb.dir/depend:
	cd /home/<USER>/db2025/db2025-x1/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/db2025/db2025-x1 /home/<USER>/db2025/db2025-x1/src /home/<USER>/db2025/db2025-x1/build /home/<USER>/db2025/db2025-x1/build/src /home/<USER>/db2025/db2025-x1/build/src/CMakeFiles/rmdb.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : src/CMakeFiles/rmdb.dir/depend

