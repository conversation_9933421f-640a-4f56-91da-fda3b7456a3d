/usr/bin/c++  -Wall -O0 -g -ggdb3 -g -O0 -g   CMakeFiles/rmdb.dir/rmdb.cpp.o  -o ../bin/rmdb  ../lib/libparser.a ../lib/libexecution.a -lreadline -lpthread ../lib/libplanner.a ../lib/libanalyze.a ../lib/libsystem.a ../lib/librecord.a ../lib/libtransaction.a ../lib/librecovery.a ../lib/libsystem.a ../lib/librecord.a ../lib/libtransaction.a ../lib/librecovery.a ../lib/libindex.a ../lib/libstorage.a -lpthread 
