#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

../src/common/config.h
atomic
-
chrono
-
cstdint
-

../src/defs.h
iostream
-
map
-

../src/errors.h
cerrno
-
cstring
-
string
-
vector
-

../src/record/rm_defs.h
defs.h
../src/record/defs.h
storage/buffer_pool_manager.h
../src/record/storage/buffer_pool_manager.h
system/sm_meta.h
../src/record/system/sm_meta.h

../src/recovery/log_defs.h
defs.h
../src/recovery/defs.h
storage/disk_manager.h
../src/recovery/storage/disk_manager.h
common/config.h
../src/recovery/common/config.h
atomic
-
chrono
-

../src/recovery/log_manager.h
mutex
-
vector
-
iostream
-
log_defs.h
../src/recovery/log_defs.h
common/config.h
../src/recovery/common/config.h
record/rm_defs.h
../src/recovery/record/rm_defs.h

../src/replacer/lru_replacer.h
list
-
mutex
-
vector
-
common/config.h
../src/replacer/common/config.h
replacer/replacer.h
../src/replacer/replacer/replacer.h
unordered_map
../src/replacer/unordered_map

../src/replacer/replacer.h
common/config.h
../src/replacer/common/config.h

../src/storage/buffer_pool_manager.h
fcntl.h
-
unistd.h
-
cassert
-
list
-
unordered_map
-
vector
-
disk_manager.h
../src/storage/disk_manager.h
errors.h
../src/storage/errors.h
page.h
../src/storage/page.h
replacer/lru_replacer.h
../src/storage/replacer/lru_replacer.h
replacer/replacer.h
../src/storage/replacer/replacer.h

../src/storage/disk_manager.h
fcntl.h
-
sys/stat.h
-
unistd.h
-
atomic
-
fstream
-
iostream
-
string
-
unordered_map
-
common/config.h
../src/storage/common/config.h
errors.h
../src/storage/errors.h

../src/system/sm_defs.h
defs.h
../src/system/defs.h
string
-

../src/system/sm_meta.h
algorithm
-
iostream
-
map
-
string
-
vector
-
errors.h
../src/system/errors.h
sm_defs.h
../src/system/sm_defs.h

/home/<USER>/db2025/db2025-x1/src/replacer/lru_replacer.cpp
lru_replacer.h
/home/<USER>/db2025/db2025-x1/src/replacer/lru_replacer.h

/home/<USER>/db2025/db2025-x1/src/replacer/lru_replacer.h
list
-
mutex
-
vector
-
common/config.h
/home/<USER>/db2025/db2025-x1/src/replacer/common/config.h
replacer/replacer.h
/home/<USER>/db2025/db2025-x1/src/replacer/replacer/replacer.h
unordered_map
/home/<USER>/db2025/db2025-x1/src/replacer/unordered_map

/home/<USER>/db2025/db2025-x1/src/storage/buffer_pool_manager.cpp
buffer_pool_manager.h
/home/<USER>/db2025/db2025-x1/src/storage/buffer_pool_manager.h
recovery/log_manager.h
/home/<USER>/db2025/db2025-x1/src/storage/recovery/log_manager.h

/home/<USER>/db2025/db2025-x1/src/storage/buffer_pool_manager.h
fcntl.h
-
unistd.h
-
cassert
-
list
-
unordered_map
-
vector
-
disk_manager.h
/home/<USER>/db2025/db2025-x1/src/storage/disk_manager.h
errors.h
/home/<USER>/db2025/db2025-x1/src/storage/errors.h
page.h
/home/<USER>/db2025/db2025-x1/src/storage/page.h
replacer/lru_replacer.h
/home/<USER>/db2025/db2025-x1/src/storage/replacer/lru_replacer.h
replacer/replacer.h
/home/<USER>/db2025/db2025-x1/src/storage/replacer/replacer.h

/home/<USER>/db2025/db2025-x1/src/storage/disk_manager.cpp
storage/disk_manager.h
/home/<USER>/db2025/db2025-x1/src/storage/storage/disk_manager.h
assert.h
-
string.h
-
sys/stat.h
-
unistd.h
-
defs.h
/home/<USER>/db2025/db2025-x1/src/storage/defs.h

/home/<USER>/db2025/db2025-x1/src/storage/disk_manager.h
fcntl.h
-
sys/stat.h
-
unistd.h
-
atomic
-
fstream
-
iostream
-
string
-
unordered_map
-
common/config.h
/home/<USER>/db2025/db2025-x1/src/storage/common/config.h
errors.h
/home/<USER>/db2025/db2025-x1/src/storage/errors.h

/home/<USER>/db2025/db2025-x1/src/storage/page.h
common/config.h
/home/<USER>/db2025/db2025-x1/src/storage/common/config.h
shared_mutex
-

