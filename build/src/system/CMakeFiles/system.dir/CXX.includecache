#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

../src/common/common.h
cassert
-
cstring
-
memory
-
string
-
vector
-
defs.h
../src/common/defs.h
record/rm_defs.h
../src/common/record/rm_defs.h
system/sm_meta.h
../src/common/system/sm_meta.h
parser/ast.h
../src/common/parser/ast.h

../src/common/config.h
atomic
-
chrono
-
cstdint
-

../src/common/context.h
transaction/transaction.h
../src/common/transaction/transaction.h
transaction/concurrency/lock_manager.h
../src/common/transaction/concurrency/lock_manager.h
recovery/log_manager.h
../src/common/recovery/log_manager.h

../src/common/exception.h
atomic
-
cstdio
-
cstdlib
-
iostream
-
memory
-
stdexcept
-
string
-

../src/defs.h
iostream
-
map
-

../src/errors.h
cerrno
-
cstring
-
string
-
vector
-

../src/index/ix.h
ix_scan.h
../src/index/ix_scan.h
ix_manager.h
../src/index/ix_manager.h

../src/index/ix_defs.h
vector
-
defs.h
../src/index/defs.h
storage/buffer_pool_manager.h
../src/index/storage/buffer_pool_manager.h

../src/index/ix_index_handle.h
ix_defs.h
../src/index/ix_defs.h
transaction/transaction.h
../src/index/transaction/transaction.h

../src/index/ix_manager.h
memory
-
string
-
system/sm_meta.h
../src/index/system/sm_meta.h
ix_defs.h
../src/index/ix_defs.h
ix_index_handle.h
../src/index/ix_index_handle.h

../src/index/ix_scan.h
ix_defs.h
../src/index/ix_defs.h
ix_index_handle.h
../src/index/ix_index_handle.h

../src/parser/ast.h
vector
-
string
-
memory
-

../src/record/bitmap.h
cinttypes
-
cstring
-

../src/record/rm.h
rm_scan.h
../src/record/rm_scan.h
rm_manager.h
../src/record/rm_manager.h
rm_defs.h
../src/record/rm_defs.h

../src/record/rm_defs.h
defs.h
../src/record/defs.h
storage/buffer_pool_manager.h
../src/record/storage/buffer_pool_manager.h
system/sm_meta.h
../src/record/system/sm_meta.h

../src/record/rm_file_handle.h
assert.h
-
memory
-
bitmap.h
../src/record/bitmap.h
common/context.h
../src/record/common/context.h
rm_defs.h
../src/record/rm_defs.h

../src/record/rm_manager.h
assert.h
-
bitmap.h
../src/record/bitmap.h
rm_defs.h
../src/record/rm_defs.h
rm_file_handle.h
../src/record/rm_file_handle.h

../src/record/rm_scan.h
rm_defs.h
../src/record/rm_defs.h

../src/record_printer.h
cassert
-
iostream
-
iomanip
-
string
-
sstream
-
common/context.h
../src/common/context.h
common/config.h
../src/common/config.h

../src/recovery/log_defs.h
defs.h
../src/recovery/defs.h
storage/disk_manager.h
../src/recovery/storage/disk_manager.h
common/config.h
../src/recovery/common/config.h
atomic
-
chrono
-

../src/recovery/log_manager.h
mutex
-
vector
-
iostream
-
log_defs.h
../src/recovery/log_defs.h
common/config.h
../src/recovery/common/config.h
record/rm_defs.h
../src/recovery/record/rm_defs.h

../src/replacer/lru_replacer.h
list
-
mutex
-
vector
-
common/config.h
../src/replacer/common/config.h
replacer/replacer.h
../src/replacer/replacer/replacer.h
unordered_map
../src/replacer/unordered_map

../src/replacer/replacer.h
common/config.h
../src/replacer/common/config.h

../src/storage/buffer_pool_manager.h
fcntl.h
-
unistd.h
-
cassert
-
list
-
unordered_map
-
vector
-
disk_manager.h
../src/storage/disk_manager.h
errors.h
../src/storage/errors.h
page.h
../src/storage/page.h
replacer/lru_replacer.h
../src/storage/replacer/lru_replacer.h
replacer/replacer.h
../src/storage/replacer/replacer.h

../src/storage/disk_manager.h
fcntl.h
-
sys/stat.h
-
unistd.h
-
atomic
-
fstream
-
iostream
-
string
-
unordered_map
-
common/config.h
../src/storage/common/config.h
errors.h
../src/storage/errors.h

../src/storage/page.h
common/config.h
../src/storage/common/config.h
shared_mutex
-

../src/system/sm_manager.h
index/ix.h
../src/system/index/ix.h
record/rm_file_handle.h
../src/system/record/rm_file_handle.h
sm_defs.h
../src/system/sm_defs.h
sm_meta.h
../src/system/sm_meta.h
common/context.h
../src/system/common/context.h

../src/system/sm_meta.h
algorithm
-
iostream
-
map
-
string
-
vector
-
errors.h
../src/system/errors.h
sm_defs.h
../src/system/sm_defs.h

../src/transaction/concurrency/lock_manager.h
mutex
-
condition_variable
-
transaction/transaction.h
../src/transaction/concurrency/transaction/transaction.h

../src/transaction/transaction.h
atomic
-
deque
-
memory
-
string
-
thread
-
unordered_set
-
vector
-
common/common.h
../src/transaction/common/common.h
transaction/txn_defs.h
../src/transaction/transaction/txn_defs.h
record/rm_defs.h
../src/transaction/record/rm_defs.h

../src/transaction/transaction_manager.h
atomic
-
unordered_map
-
optional
-
functional
-
shared_mutex
-
transaction.h
../src/transaction/transaction.h
watermark.h
../src/transaction/watermark.h
recovery/log_manager.h
../src/transaction/recovery/log_manager.h
concurrency/lock_manager.h
../src/transaction/concurrency/lock_manager.h
system/sm_manager.h
../src/transaction/system/sm_manager.h
common/exception.h
../src/transaction/common/exception.h

../src/transaction/txn_defs.h
atomic
-
common/config.h
../src/transaction/common/config.h
defs.h
../src/transaction/defs.h
record/rm_defs.h
../src/transaction/record/rm_defs.h

../src/transaction/watermark.h
map
-
unordered_map
-
algorithm
-
transaction/transaction.h
../src/transaction/transaction/transaction.h

/home/<USER>/db2025/db2025-x1/src/system/sm_defs.h
defs.h
/home/<USER>/db2025/db2025-x1/src/system/defs.h
string
-

/home/<USER>/db2025/db2025-x1/src/system/sm_manager.cpp
sm_manager.h
/home/<USER>/db2025/db2025-x1/src/system/sm_manager.h
sys/stat.h
-
unistd.h
-
fstream
-
index/ix.h
/home/<USER>/db2025/db2025-x1/src/system/index/ix.h
record/rm.h
/home/<USER>/db2025/db2025-x1/src/system/record/rm.h
record_printer.h
/home/<USER>/db2025/db2025-x1/src/system/record_printer.h
transaction/transaction_manager.h
/home/<USER>/db2025/db2025-x1/src/system/transaction/transaction_manager.h
recovery/log_manager.h
/home/<USER>/db2025/db2025-x1/src/system/recovery/log_manager.h

/home/<USER>/db2025/db2025-x1/src/system/sm_manager.h
index/ix.h
/home/<USER>/db2025/db2025-x1/src/system/index/ix.h
record/rm_file_handle.h
/home/<USER>/db2025/db2025-x1/src/system/record/rm_file_handle.h
sm_defs.h
/home/<USER>/db2025/db2025-x1/src/system/sm_defs.h
sm_meta.h
/home/<USER>/db2025/db2025-x1/src/system/sm_meta.h
common/context.h
/home/<USER>/db2025/db2025-x1/src/system/common/context.h

/home/<USER>/db2025/db2025-x1/src/system/sm_meta.h
algorithm
-
iostream
-
map
-
string
-
vector
-
errors.h
/home/<USER>/db2025/db2025-x1/src/system/errors.h
sm_defs.h
/home/<USER>/db2025/db2025-x1/src/system/sm_defs.h

