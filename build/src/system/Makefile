# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/db2025/db2025-x1

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/db2025/db2025-x1/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	/usr/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test

.PHONY : test/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/db2025/db2025-x1/build && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles /home/<USER>/db2025/db2025-x1/build/src/system/CMakeFiles/progress.marks
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f CMakeFiles/Makefile2 src/system/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f CMakeFiles/Makefile2 src/system/clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f CMakeFiles/Makefile2 src/system/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f CMakeFiles/Makefile2 src/system/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/db2025/db2025-x1/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
src/system/CMakeFiles/system.dir/rule:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f CMakeFiles/Makefile2 src/system/CMakeFiles/system.dir/rule
.PHONY : src/system/CMakeFiles/system.dir/rule

# Convenience name for target.
system: src/system/CMakeFiles/system.dir/rule

.PHONY : system

# fast build rule for target.
system/fast:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f src/system/CMakeFiles/system.dir/build.make src/system/CMakeFiles/system.dir/build
.PHONY : system/fast

sm_manager.o: sm_manager.cpp.o

.PHONY : sm_manager.o

# target to build an object file
sm_manager.cpp.o:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f src/system/CMakeFiles/system.dir/build.make src/system/CMakeFiles/system.dir/sm_manager.cpp.o
.PHONY : sm_manager.cpp.o

sm_manager.i: sm_manager.cpp.i

.PHONY : sm_manager.i

# target to preprocess a source file
sm_manager.cpp.i:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f src/system/CMakeFiles/system.dir/build.make src/system/CMakeFiles/system.dir/sm_manager.cpp.i
.PHONY : sm_manager.cpp.i

sm_manager.s: sm_manager.cpp.s

.PHONY : sm_manager.s

# target to generate assembly for a file
sm_manager.cpp.s:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f src/system/CMakeFiles/system.dir/build.make src/system/CMakeFiles/system.dir/sm_manager.cpp.s
.PHONY : sm_manager.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... install/local"
	@echo "... install"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... edit_cache"
	@echo "... test"
	@echo "... install/strip"
	@echo "... system"
	@echo "... sm_manager.o"
	@echo "... sm_manager.i"
	@echo "... sm_manager.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/db2025/db2025-x1/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

