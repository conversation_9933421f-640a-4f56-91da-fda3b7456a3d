# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/db2025/db2025-x1

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/db2025/db2025-x1/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	/usr/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test

.PHONY : test/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles /home/<USER>/db2025/db2025-x1/build/CMakeFiles/progress.marks
	$(MAKE) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named reconstructuple_test

# Build rule for target.
reconstructuple_test: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 reconstructuple_test
.PHONY : reconstructuple_test

# fast build rule for target.
reconstructuple_test/fast:
	$(MAKE) -f src/CMakeFiles/reconstructuple_test.dir/build.make src/CMakeFiles/reconstructuple_test.dir/build
.PHONY : reconstructuple_test/fast

#=============================================================================
# Target rules for targets named unit_test

# Build rule for target.
unit_test: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 unit_test
.PHONY : unit_test

# fast build rule for target.
unit_test/fast:
	$(MAKE) -f src/CMakeFiles/unit_test.dir/build.make src/CMakeFiles/unit_test.dir/build
.PHONY : unit_test/fast

#=============================================================================
# Target rules for targets named rmdb

# Build rule for target.
rmdb: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 rmdb
.PHONY : rmdb

# fast build rule for target.
rmdb/fast:
	$(MAKE) -f src/CMakeFiles/rmdb.dir/build.make src/CMakeFiles/rmdb.dir/build
.PHONY : rmdb/fast

#=============================================================================
# Target rules for targets named analyze

# Build rule for target.
analyze: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 analyze
.PHONY : analyze

# fast build rule for target.
analyze/fast:
	$(MAKE) -f src/analyze/CMakeFiles/analyze.dir/build.make src/analyze/CMakeFiles/analyze.dir/build
.PHONY : analyze/fast

#=============================================================================
# Target rules for targets named records

# Build rule for target.
records: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 records
.PHONY : records

# fast build rule for target.
records/fast:
	$(MAKE) -f src/record/CMakeFiles/records.dir/build.make src/record/CMakeFiles/records.dir/build
.PHONY : records/fast

#=============================================================================
# Target rules for targets named record

# Build rule for target.
record: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 record
.PHONY : record

# fast build rule for target.
record/fast:
	$(MAKE) -f src/record/CMakeFiles/record.dir/build.make src/record/CMakeFiles/record.dir/build
.PHONY : record/fast

#=============================================================================
# Target rules for targets named index

# Build rule for target.
index: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 index
.PHONY : index

# fast build rule for target.
index/fast:
	$(MAKE) -f src/index/CMakeFiles/index.dir/build.make src/index/CMakeFiles/index.dir/build
.PHONY : index/fast

#=============================================================================
# Target rules for targets named system

# Build rule for target.
system: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 system
.PHONY : system

# fast build rule for target.
system/fast:
	$(MAKE) -f src/system/CMakeFiles/system.dir/build.make src/system/CMakeFiles/system.dir/build
.PHONY : system/fast

#=============================================================================
# Target rules for targets named execution

# Build rule for target.
execution: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 execution
.PHONY : execution

# fast build rule for target.
execution/fast:
	$(MAKE) -f src/execution/CMakeFiles/execution.dir/build.make src/execution/CMakeFiles/execution.dir/build
.PHONY : execution/fast

#=============================================================================
# Target rules for targets named test_parser

# Build rule for target.
test_parser: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 test_parser
.PHONY : test_parser

# fast build rule for target.
test_parser/fast:
	$(MAKE) -f src/parser/CMakeFiles/test_parser.dir/build.make src/parser/CMakeFiles/test_parser.dir/build
.PHONY : test_parser/fast

#=============================================================================
# Target rules for targets named parser

# Build rule for target.
parser: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 parser
.PHONY : parser

# fast build rule for target.
parser/fast:
	$(MAKE) -f src/parser/CMakeFiles/parser.dir/build.make src/parser/CMakeFiles/parser.dir/build
.PHONY : parser/fast

#=============================================================================
# Target rules for targets named planner

# Build rule for target.
planner: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 planner
.PHONY : planner

# fast build rule for target.
planner/fast:
	$(MAKE) -f src/optimizer/CMakeFiles/planner.dir/build.make src/optimizer/CMakeFiles/planner.dir/build
.PHONY : planner/fast

#=============================================================================
# Target rules for targets named storage

# Build rule for target.
storage: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 storage
.PHONY : storage

# fast build rule for target.
storage/fast:
	$(MAKE) -f src/storage/CMakeFiles/storage.dir/build.make src/storage/CMakeFiles/storage.dir/build
.PHONY : storage/fast

#=============================================================================
# Target rules for targets named lru_replacer

# Build rule for target.
lru_replacer: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 lru_replacer
.PHONY : lru_replacer

# fast build rule for target.
lru_replacer/fast:
	$(MAKE) -f src/replacer/CMakeFiles/lru_replacer.dir/build.make src/replacer/CMakeFiles/lru_replacer.dir/build
.PHONY : lru_replacer/fast

#=============================================================================
# Target rules for targets named transaction

# Build rule for target.
transaction: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 transaction
.PHONY : transaction

# fast build rule for target.
transaction/fast:
	$(MAKE) -f src/transaction/CMakeFiles/transaction.dir/build.make src/transaction/CMakeFiles/transaction.dir/build
.PHONY : transaction/fast

#=============================================================================
# Target rules for targets named recoverys

# Build rule for target.
recoverys: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 recoverys
.PHONY : recoverys

# fast build rule for target.
recoverys/fast:
	$(MAKE) -f src/recovery/CMakeFiles/recoverys.dir/build.make src/recovery/CMakeFiles/recoverys.dir/build
.PHONY : recoverys/fast

#=============================================================================
# Target rules for targets named recovery

# Build rule for target.
recovery: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 recovery
.PHONY : recovery

# fast build rule for target.
recovery/fast:
	$(MAKE) -f src/recovery/CMakeFiles/recovery.dir/build.make src/recovery/CMakeFiles/recovery.dir/build
.PHONY : recovery/fast

#=============================================================================
# Target rules for targets named gmock_main

# Build rule for target.
gmock_main: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 gmock_main
.PHONY : gmock_main

# fast build rule for target.
gmock_main/fast:
	$(MAKE) -f deps/googletest/googlemock/CMakeFiles/gmock_main.dir/build.make deps/googletest/googlemock/CMakeFiles/gmock_main.dir/build
.PHONY : gmock_main/fast

#=============================================================================
# Target rules for targets named gmock

# Build rule for target.
gmock: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 gmock
.PHONY : gmock

# fast build rule for target.
gmock/fast:
	$(MAKE) -f deps/googletest/googlemock/CMakeFiles/gmock.dir/build.make deps/googletest/googlemock/CMakeFiles/gmock.dir/build
.PHONY : gmock/fast

#=============================================================================
# Target rules for targets named gtest_main

# Build rule for target.
gtest_main: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 gtest_main
.PHONY : gtest_main

# fast build rule for target.
gtest_main/fast:
	$(MAKE) -f deps/googletest/googletest/CMakeFiles/gtest_main.dir/build.make deps/googletest/googletest/CMakeFiles/gtest_main.dir/build
.PHONY : gtest_main/fast

#=============================================================================
# Target rules for targets named gtest

# Build rule for target.
gtest: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 gtest
.PHONY : gtest

# fast build rule for target.
gtest/fast:
	$(MAKE) -f deps/googletest/googletest/CMakeFiles/gtest.dir/build.make deps/googletest/googletest/CMakeFiles/gtest.dir/build
.PHONY : gtest/fast

#=============================================================================
# Target rules for targets named simple_consistency_check

# Build rule for target.
simple_consistency_check: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 simple_consistency_check
.PHONY : simple_consistency_check

# fast build rule for target.
simple_consistency_check/fast:
	$(MAKE) -f tpcc_run/tpc-c/CMakeFiles/simple_consistency_check.dir/build.make tpcc_run/tpc-c/CMakeFiles/simple_consistency_check.dir/build
.PHONY : simple_consistency_check/fast

#=============================================================================
# Target rules for targets named test_db2025_compatibility

# Build rule for target.
test_db2025_compatibility: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 test_db2025_compatibility
.PHONY : test_db2025_compatibility

# fast build rule for target.
test_db2025_compatibility/fast:
	$(MAKE) -f tpcc_run/tpc-c/CMakeFiles/test_db2025_compatibility.dir/build.make tpcc_run/tpc-c/CMakeFiles/test_db2025_compatibility.dir/build
.PHONY : test_db2025_compatibility/fast

#=============================================================================
# Target rules for targets named tpcc_start

# Build rule for target.
tpcc_start: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 tpcc_start
.PHONY : tpcc_start

# fast build rule for target.
tpcc_start/fast:
	$(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build
.PHONY : tpcc_start/fast

#=============================================================================
# Target rules for targets named tpcc_load

# Build rule for target.
tpcc_load: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 tpcc_load
.PHONY : tpcc_load

# fast build rule for target.
tpcc_load/fast:
	$(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_load.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_load.dir/build
.PHONY : tpcc_load/fast

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... install/strip"
	@echo "... install/local"
	@echo "... install"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... edit_cache"
	@echo "... test"
	@echo "... reconstructuple_test"
	@echo "... unit_test"
	@echo "... rmdb"
	@echo "... analyze"
	@echo "... records"
	@echo "... record"
	@echo "... index"
	@echo "... system"
	@echo "... execution"
	@echo "... test_parser"
	@echo "... parser"
	@echo "... planner"
	@echo "... storage"
	@echo "... lru_replacer"
	@echo "... transaction"
	@echo "... recoverys"
	@echo "... recovery"
	@echo "... gmock_main"
	@echo "... gmock"
	@echo "... gtest_main"
	@echo "... gtest"
	@echo "... simple_consistency_check"
	@echo "... test_db2025_compatibility"
	@echo "... tpcc_start"
	@echo "... tpcc_load"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

