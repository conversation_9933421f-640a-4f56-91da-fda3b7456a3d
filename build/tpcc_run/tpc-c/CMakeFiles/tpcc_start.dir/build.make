# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/db2025/db2025-x1

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/db2025/db2025-x1/build

# Include any dependencies generated for this target.
include tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/depend.make

# Include the progress variables for this target.
include tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/progress.make

# Include the compile flags for this target's objects.
include tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/flags.make

tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/start.cpp.o: tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/flags.make
tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/start.cpp.o: ../tpcc_run/tpc-c/start.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/start.cpp.o"
	cd /home/<USER>/db2025/db2025-x1/build/tpcc_run/tpc-c && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/tpcc_start.dir/start.cpp.o -c /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/start.cpp

tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/start.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tpcc_start.dir/start.cpp.i"
	cd /home/<USER>/db2025/db2025-x1/build/tpcc_run/tpc-c && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/start.cpp > CMakeFiles/tpcc_start.dir/start.cpp.i

tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/start.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tpcc_start.dir/start.cpp.s"
	cd /home/<USER>/db2025/db2025-x1/build/tpcc_run/tpc-c && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/start.cpp -o CMakeFiles/tpcc_start.dir/start.cpp.s

tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/spt_proc.cpp.o: tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/flags.make
tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/spt_proc.cpp.o: ../tpcc_run/tpc-c/spt_proc.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/spt_proc.cpp.o"
	cd /home/<USER>/db2025/db2025-x1/build/tpcc_run/tpc-c && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/tpcc_start.dir/spt_proc.cpp.o -c /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/spt_proc.cpp

tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/spt_proc.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tpcc_start.dir/spt_proc.cpp.i"
	cd /home/<USER>/db2025/db2025-x1/build/tpcc_run/tpc-c && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/spt_proc.cpp > CMakeFiles/tpcc_start.dir/spt_proc.cpp.i

tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/spt_proc.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tpcc_start.dir/spt_proc.cpp.s"
	cd /home/<USER>/db2025/db2025-x1/build/tpcc_run/tpc-c && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/spt_proc.cpp -o CMakeFiles/tpcc_start.dir/spt_proc.cpp.s

tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/driver.cpp.o: tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/flags.make
tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/driver.cpp.o: ../tpcc_run/tpc-c/driver.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/driver.cpp.o"
	cd /home/<USER>/db2025/db2025-x1/build/tpcc_run/tpc-c && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/tpcc_start.dir/driver.cpp.o -c /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/driver.cpp

tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/driver.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tpcc_start.dir/driver.cpp.i"
	cd /home/<USER>/db2025/db2025-x1/build/tpcc_run/tpc-c && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/driver.cpp > CMakeFiles/tpcc_start.dir/driver.cpp.i

tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/driver.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tpcc_start.dir/driver.cpp.s"
	cd /home/<USER>/db2025/db2025-x1/build/tpcc_run/tpc-c && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/driver.cpp -o CMakeFiles/tpcc_start.dir/driver.cpp.s

tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/support.cpp.o: tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/flags.make
tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/support.cpp.o: ../tpcc_run/tpc-c/support.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/support.cpp.o"
	cd /home/<USER>/db2025/db2025-x1/build/tpcc_run/tpc-c && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/tpcc_start.dir/support.cpp.o -c /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/support.cpp

tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/support.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tpcc_start.dir/support.cpp.i"
	cd /home/<USER>/db2025/db2025-x1/build/tpcc_run/tpc-c && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/support.cpp > CMakeFiles/tpcc_start.dir/support.cpp.i

tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/support.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tpcc_start.dir/support.cpp.s"
	cd /home/<USER>/db2025/db2025-x1/build/tpcc_run/tpc-c && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/support.cpp -o CMakeFiles/tpcc_start.dir/support.cpp.s

tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/sequence.cpp.o: tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/flags.make
tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/sequence.cpp.o: ../tpcc_run/tpc-c/sequence.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/sequence.cpp.o"
	cd /home/<USER>/db2025/db2025-x1/build/tpcc_run/tpc-c && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/tpcc_start.dir/sequence.cpp.o -c /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/sequence.cpp

tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/sequence.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tpcc_start.dir/sequence.cpp.i"
	cd /home/<USER>/db2025/db2025-x1/build/tpcc_run/tpc-c && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/sequence.cpp > CMakeFiles/tpcc_start.dir/sequence.cpp.i

tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/sequence.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tpcc_start.dir/sequence.cpp.s"
	cd /home/<USER>/db2025/db2025-x1/build/tpcc_run/tpc-c && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/sequence.cpp -o CMakeFiles/tpcc_start.dir/sequence.cpp.s

tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/rthist.cpp.o: tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/flags.make
tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/rthist.cpp.o: ../tpcc_run/tpc-c/rthist.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/rthist.cpp.o"
	cd /home/<USER>/db2025/db2025-x1/build/tpcc_run/tpc-c && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/tpcc_start.dir/rthist.cpp.o -c /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/rthist.cpp

tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/rthist.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tpcc_start.dir/rthist.cpp.i"
	cd /home/<USER>/db2025/db2025-x1/build/tpcc_run/tpc-c && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/rthist.cpp > CMakeFiles/tpcc_start.dir/rthist.cpp.i

tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/rthist.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tpcc_start.dir/rthist.cpp.s"
	cd /home/<USER>/db2025/db2025-x1/build/tpcc_run/tpc-c && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/rthist.cpp -o CMakeFiles/tpcc_start.dir/rthist.cpp.s

tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/sb_percentile.cpp.o: tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/flags.make
tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/sb_percentile.cpp.o: ../tpcc_run/tpc-c/sb_percentile.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/sb_percentile.cpp.o"
	cd /home/<USER>/db2025/db2025-x1/build/tpcc_run/tpc-c && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/tpcc_start.dir/sb_percentile.cpp.o -c /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/sb_percentile.cpp

tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/sb_percentile.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tpcc_start.dir/sb_percentile.cpp.i"
	cd /home/<USER>/db2025/db2025-x1/build/tpcc_run/tpc-c && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/sb_percentile.cpp > CMakeFiles/tpcc_start.dir/sb_percentile.cpp.i

tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/sb_percentile.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tpcc_start.dir/sb_percentile.cpp.s"
	cd /home/<USER>/db2025/db2025-x1/build/tpcc_run/tpc-c && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/sb_percentile.cpp -o CMakeFiles/tpcc_start.dir/sb_percentile.cpp.s

tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/neword.cpp.o: tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/flags.make
tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/neword.cpp.o: ../tpcc_run/tpc-c/neword.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/neword.cpp.o"
	cd /home/<USER>/db2025/db2025-x1/build/tpcc_run/tpc-c && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/tpcc_start.dir/neword.cpp.o -c /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/neword.cpp

tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/neword.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tpcc_start.dir/neword.cpp.i"
	cd /home/<USER>/db2025/db2025-x1/build/tpcc_run/tpc-c && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/neword.cpp > CMakeFiles/tpcc_start.dir/neword.cpp.i

tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/neword.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tpcc_start.dir/neword.cpp.s"
	cd /home/<USER>/db2025/db2025-x1/build/tpcc_run/tpc-c && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/neword.cpp -o CMakeFiles/tpcc_start.dir/neword.cpp.s

tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/payment.cpp.o: tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/flags.make
tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/payment.cpp.o: ../tpcc_run/tpc-c/payment.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/payment.cpp.o"
	cd /home/<USER>/db2025/db2025-x1/build/tpcc_run/tpc-c && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/tpcc_start.dir/payment.cpp.o -c /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/payment.cpp

tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/payment.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tpcc_start.dir/payment.cpp.i"
	cd /home/<USER>/db2025/db2025-x1/build/tpcc_run/tpc-c && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/payment.cpp > CMakeFiles/tpcc_start.dir/payment.cpp.i

tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/payment.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tpcc_start.dir/payment.cpp.s"
	cd /home/<USER>/db2025/db2025-x1/build/tpcc_run/tpc-c && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/payment.cpp -o CMakeFiles/tpcc_start.dir/payment.cpp.s

tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/ordstat.cpp.o: tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/flags.make
tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/ordstat.cpp.o: ../tpcc_run/tpc-c/ordstat.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/ordstat.cpp.o"
	cd /home/<USER>/db2025/db2025-x1/build/tpcc_run/tpc-c && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/tpcc_start.dir/ordstat.cpp.o -c /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/ordstat.cpp

tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/ordstat.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tpcc_start.dir/ordstat.cpp.i"
	cd /home/<USER>/db2025/db2025-x1/build/tpcc_run/tpc-c && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/ordstat.cpp > CMakeFiles/tpcc_start.dir/ordstat.cpp.i

tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/ordstat.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tpcc_start.dir/ordstat.cpp.s"
	cd /home/<USER>/db2025/db2025-x1/build/tpcc_run/tpc-c && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/ordstat.cpp -o CMakeFiles/tpcc_start.dir/ordstat.cpp.s

tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/delivery.cpp.o: tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/flags.make
tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/delivery.cpp.o: ../tpcc_run/tpc-c/delivery.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/delivery.cpp.o"
	cd /home/<USER>/db2025/db2025-x1/build/tpcc_run/tpc-c && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/tpcc_start.dir/delivery.cpp.o -c /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/delivery.cpp

tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/delivery.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tpcc_start.dir/delivery.cpp.i"
	cd /home/<USER>/db2025/db2025-x1/build/tpcc_run/tpc-c && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/delivery.cpp > CMakeFiles/tpcc_start.dir/delivery.cpp.i

tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/delivery.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tpcc_start.dir/delivery.cpp.s"
	cd /home/<USER>/db2025/db2025-x1/build/tpcc_run/tpc-c && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/delivery.cpp -o CMakeFiles/tpcc_start.dir/delivery.cpp.s

tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/slev.cpp.o: tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/flags.make
tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/slev.cpp.o: ../tpcc_run/tpc-c/slev.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building CXX object tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/slev.cpp.o"
	cd /home/<USER>/db2025/db2025-x1/build/tpcc_run/tpc-c && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/tpcc_start.dir/slev.cpp.o -c /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/slev.cpp

tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/slev.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tpcc_start.dir/slev.cpp.i"
	cd /home/<USER>/db2025/db2025-x1/build/tpcc_run/tpc-c && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/slev.cpp > CMakeFiles/tpcc_start.dir/slev.cpp.i

tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/slev.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tpcc_start.dir/slev.cpp.s"
	cd /home/<USER>/db2025/db2025-x1/build/tpcc_run/tpc-c && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c/slev.cpp -o CMakeFiles/tpcc_start.dir/slev.cpp.s

# Object files for target tpcc_start
tpcc_start_OBJECTS = \
"CMakeFiles/tpcc_start.dir/start.cpp.o" \
"CMakeFiles/tpcc_start.dir/spt_proc.cpp.o" \
"CMakeFiles/tpcc_start.dir/driver.cpp.o" \
"CMakeFiles/tpcc_start.dir/support.cpp.o" \
"CMakeFiles/tpcc_start.dir/sequence.cpp.o" \
"CMakeFiles/tpcc_start.dir/rthist.cpp.o" \
"CMakeFiles/tpcc_start.dir/sb_percentile.cpp.o" \
"CMakeFiles/tpcc_start.dir/neword.cpp.o" \
"CMakeFiles/tpcc_start.dir/payment.cpp.o" \
"CMakeFiles/tpcc_start.dir/ordstat.cpp.o" \
"CMakeFiles/tpcc_start.dir/delivery.cpp.o" \
"CMakeFiles/tpcc_start.dir/slev.cpp.o"

# External object files for target tpcc_start
tpcc_start_EXTERNAL_OBJECTS =

bin/tpcc_start: tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/start.cpp.o
bin/tpcc_start: tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/spt_proc.cpp.o
bin/tpcc_start: tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/driver.cpp.o
bin/tpcc_start: tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/support.cpp.o
bin/tpcc_start: tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/sequence.cpp.o
bin/tpcc_start: tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/rthist.cpp.o
bin/tpcc_start: tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/sb_percentile.cpp.o
bin/tpcc_start: tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/neword.cpp.o
bin/tpcc_start: tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/payment.cpp.o
bin/tpcc_start: tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/ordstat.cpp.o
bin/tpcc_start: tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/delivery.cpp.o
bin/tpcc_start: tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/slev.cpp.o
bin/tpcc_start: tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build.make
bin/tpcc_start: tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Linking CXX executable ../../bin/tpcc_start"
	cd /home/<USER>/db2025/db2025-x1/build/tpcc_run/tpc-c && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/tpcc_start.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build: bin/tpcc_start

.PHONY : tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build

tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/clean:
	cd /home/<USER>/db2025/db2025-x1/build/tpcc_run/tpc-c && $(CMAKE_COMMAND) -P CMakeFiles/tpcc_start.dir/cmake_clean.cmake
.PHONY : tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/clean

tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/depend:
	cd /home/<USER>/db2025/db2025-x1/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/db2025/db2025-x1 /home/<USER>/db2025/db2025-x1/tpcc_run/tpc-c /home/<USER>/db2025/db2025-x1/build /home/<USER>/db2025/db2025-x1/build/tpcc_run/tpc-c /home/<USER>/db2025/db2025-x1/build/tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/depend

