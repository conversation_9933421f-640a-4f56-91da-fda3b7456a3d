# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/db2025/db2025-x1

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/db2025/db2025-x1/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	/usr/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test

.PHONY : test/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/db2025/db2025-x1/build && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles /home/<USER>/db2025/db2025-x1/build/tpcc_run/tpc-c/CMakeFiles/progress.marks
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f CMakeFiles/Makefile2 tpcc_run/tpc-c/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f CMakeFiles/Makefile2 tpcc_run/tpc-c/clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f CMakeFiles/Makefile2 tpcc_run/tpc-c/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f CMakeFiles/Makefile2 tpcc_run/tpc-c/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/db2025/db2025-x1/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
tpcc_run/tpc-c/CMakeFiles/simple_consistency_check.dir/rule:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f CMakeFiles/Makefile2 tpcc_run/tpc-c/CMakeFiles/simple_consistency_check.dir/rule
.PHONY : tpcc_run/tpc-c/CMakeFiles/simple_consistency_check.dir/rule

# Convenience name for target.
simple_consistency_check: tpcc_run/tpc-c/CMakeFiles/simple_consistency_check.dir/rule

.PHONY : simple_consistency_check

# fast build rule for target.
simple_consistency_check/fast:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/simple_consistency_check.dir/build.make tpcc_run/tpc-c/CMakeFiles/simple_consistency_check.dir/build
.PHONY : simple_consistency_check/fast

# Convenience name for target.
tpcc_run/tpc-c/CMakeFiles/test_db2025_compatibility.dir/rule:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f CMakeFiles/Makefile2 tpcc_run/tpc-c/CMakeFiles/test_db2025_compatibility.dir/rule
.PHONY : tpcc_run/tpc-c/CMakeFiles/test_db2025_compatibility.dir/rule

# Convenience name for target.
test_db2025_compatibility: tpcc_run/tpc-c/CMakeFiles/test_db2025_compatibility.dir/rule

.PHONY : test_db2025_compatibility

# fast build rule for target.
test_db2025_compatibility/fast:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/test_db2025_compatibility.dir/build.make tpcc_run/tpc-c/CMakeFiles/test_db2025_compatibility.dir/build
.PHONY : test_db2025_compatibility/fast

# Convenience name for target.
tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/rule:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f CMakeFiles/Makefile2 tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/rule
.PHONY : tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/rule

# Convenience name for target.
tpcc_start: tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/rule

.PHONY : tpcc_start

# fast build rule for target.
tpcc_start/fast:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build
.PHONY : tpcc_start/fast

# Convenience name for target.
tpcc_run/tpc-c/CMakeFiles/tpcc_load.dir/rule:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f CMakeFiles/Makefile2 tpcc_run/tpc-c/CMakeFiles/tpcc_load.dir/rule
.PHONY : tpcc_run/tpc-c/CMakeFiles/tpcc_load.dir/rule

# Convenience name for target.
tpcc_load: tpcc_run/tpc-c/CMakeFiles/tpcc_load.dir/rule

.PHONY : tpcc_load

# fast build rule for target.
tpcc_load/fast:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_load.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_load.dir/build
.PHONY : tpcc_load/fast

delivery.o: delivery.cpp.o

.PHONY : delivery.o

# target to build an object file
delivery.cpp.o:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/delivery.cpp.o
.PHONY : delivery.cpp.o

delivery.i: delivery.cpp.i

.PHONY : delivery.i

# target to preprocess a source file
delivery.cpp.i:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/delivery.cpp.i
.PHONY : delivery.cpp.i

delivery.s: delivery.cpp.s

.PHONY : delivery.s

# target to generate assembly for a file
delivery.cpp.s:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/delivery.cpp.s
.PHONY : delivery.cpp.s

driver.o: driver.cpp.o

.PHONY : driver.o

# target to build an object file
driver.cpp.o:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/driver.cpp.o
.PHONY : driver.cpp.o

driver.i: driver.cpp.i

.PHONY : driver.i

# target to preprocess a source file
driver.cpp.i:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/driver.cpp.i
.PHONY : driver.cpp.i

driver.s: driver.cpp.s

.PHONY : driver.s

# target to generate assembly for a file
driver.cpp.s:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/driver.cpp.s
.PHONY : driver.cpp.s

load.o: load.cpp.o

.PHONY : load.o

# target to build an object file
load.cpp.o:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_load.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_load.dir/load.cpp.o
.PHONY : load.cpp.o

load.i: load.cpp.i

.PHONY : load.i

# target to preprocess a source file
load.cpp.i:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_load.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_load.dir/load.cpp.i
.PHONY : load.cpp.i

load.s: load.cpp.s

.PHONY : load.s

# target to generate assembly for a file
load.cpp.s:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_load.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_load.dir/load.cpp.s
.PHONY : load.cpp.s

neword.o: neword.cpp.o

.PHONY : neword.o

# target to build an object file
neword.cpp.o:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/neword.cpp.o
.PHONY : neword.cpp.o

neword.i: neword.cpp.i

.PHONY : neword.i

# target to preprocess a source file
neword.cpp.i:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/neword.cpp.i
.PHONY : neword.cpp.i

neword.s: neword.cpp.s

.PHONY : neword.s

# target to generate assembly for a file
neword.cpp.s:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/neword.cpp.s
.PHONY : neword.cpp.s

ordstat.o: ordstat.cpp.o

.PHONY : ordstat.o

# target to build an object file
ordstat.cpp.o:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/ordstat.cpp.o
.PHONY : ordstat.cpp.o

ordstat.i: ordstat.cpp.i

.PHONY : ordstat.i

# target to preprocess a source file
ordstat.cpp.i:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/ordstat.cpp.i
.PHONY : ordstat.cpp.i

ordstat.s: ordstat.cpp.s

.PHONY : ordstat.s

# target to generate assembly for a file
ordstat.cpp.s:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/ordstat.cpp.s
.PHONY : ordstat.cpp.s

payment.o: payment.cpp.o

.PHONY : payment.o

# target to build an object file
payment.cpp.o:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/payment.cpp.o
.PHONY : payment.cpp.o

payment.i: payment.cpp.i

.PHONY : payment.i

# target to preprocess a source file
payment.cpp.i:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/payment.cpp.i
.PHONY : payment.cpp.i

payment.s: payment.cpp.s

.PHONY : payment.s

# target to generate assembly for a file
payment.cpp.s:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/payment.cpp.s
.PHONY : payment.cpp.s

rthist.o: rthist.cpp.o

.PHONY : rthist.o

# target to build an object file
rthist.cpp.o:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/rthist.cpp.o
.PHONY : rthist.cpp.o

rthist.i: rthist.cpp.i

.PHONY : rthist.i

# target to preprocess a source file
rthist.cpp.i:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/rthist.cpp.i
.PHONY : rthist.cpp.i

rthist.s: rthist.cpp.s

.PHONY : rthist.s

# target to generate assembly for a file
rthist.cpp.s:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/rthist.cpp.s
.PHONY : rthist.cpp.s

sb_percentile.o: sb_percentile.cpp.o

.PHONY : sb_percentile.o

# target to build an object file
sb_percentile.cpp.o:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/sb_percentile.cpp.o
.PHONY : sb_percentile.cpp.o

sb_percentile.i: sb_percentile.cpp.i

.PHONY : sb_percentile.i

# target to preprocess a source file
sb_percentile.cpp.i:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/sb_percentile.cpp.i
.PHONY : sb_percentile.cpp.i

sb_percentile.s: sb_percentile.cpp.s

.PHONY : sb_percentile.s

# target to generate assembly for a file
sb_percentile.cpp.s:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/sb_percentile.cpp.s
.PHONY : sb_percentile.cpp.s

sequence.o: sequence.cpp.o

.PHONY : sequence.o

# target to build an object file
sequence.cpp.o:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/sequence.cpp.o
.PHONY : sequence.cpp.o

sequence.i: sequence.cpp.i

.PHONY : sequence.i

# target to preprocess a source file
sequence.cpp.i:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/sequence.cpp.i
.PHONY : sequence.cpp.i

sequence.s: sequence.cpp.s

.PHONY : sequence.s

# target to generate assembly for a file
sequence.cpp.s:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/sequence.cpp.s
.PHONY : sequence.cpp.s

simple_consistency_check.o: simple_consistency_check.cpp.o

.PHONY : simple_consistency_check.o

# target to build an object file
simple_consistency_check.cpp.o:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/simple_consistency_check.dir/build.make tpcc_run/tpc-c/CMakeFiles/simple_consistency_check.dir/simple_consistency_check.cpp.o
.PHONY : simple_consistency_check.cpp.o

simple_consistency_check.i: simple_consistency_check.cpp.i

.PHONY : simple_consistency_check.i

# target to preprocess a source file
simple_consistency_check.cpp.i:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/simple_consistency_check.dir/build.make tpcc_run/tpc-c/CMakeFiles/simple_consistency_check.dir/simple_consistency_check.cpp.i
.PHONY : simple_consistency_check.cpp.i

simple_consistency_check.s: simple_consistency_check.cpp.s

.PHONY : simple_consistency_check.s

# target to generate assembly for a file
simple_consistency_check.cpp.s:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/simple_consistency_check.dir/build.make tpcc_run/tpc-c/CMakeFiles/simple_consistency_check.dir/simple_consistency_check.cpp.s
.PHONY : simple_consistency_check.cpp.s

slev.o: slev.cpp.o

.PHONY : slev.o

# target to build an object file
slev.cpp.o:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/slev.cpp.o
.PHONY : slev.cpp.o

slev.i: slev.cpp.i

.PHONY : slev.i

# target to preprocess a source file
slev.cpp.i:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/slev.cpp.i
.PHONY : slev.cpp.i

slev.s: slev.cpp.s

.PHONY : slev.s

# target to generate assembly for a file
slev.cpp.s:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/slev.cpp.s
.PHONY : slev.cpp.s

spt_proc.o: spt_proc.cpp.o

.PHONY : spt_proc.o

# target to build an object file
spt_proc.cpp.o:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/spt_proc.cpp.o
.PHONY : spt_proc.cpp.o

spt_proc.i: spt_proc.cpp.i

.PHONY : spt_proc.i

# target to preprocess a source file
spt_proc.cpp.i:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/spt_proc.cpp.i
.PHONY : spt_proc.cpp.i

spt_proc.s: spt_proc.cpp.s

.PHONY : spt_proc.s

# target to generate assembly for a file
spt_proc.cpp.s:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/spt_proc.cpp.s
.PHONY : spt_proc.cpp.s

start.o: start.cpp.o

.PHONY : start.o

# target to build an object file
start.cpp.o:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/start.cpp.o
.PHONY : start.cpp.o

start.i: start.cpp.i

.PHONY : start.i

# target to preprocess a source file
start.cpp.i:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/start.cpp.i
.PHONY : start.cpp.i

start.s: start.cpp.s

.PHONY : start.s

# target to generate assembly for a file
start.cpp.s:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/start.cpp.s
.PHONY : start.cpp.s

support.o: support.cpp.o

.PHONY : support.o

# target to build an object file
support.cpp.o:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/simple_consistency_check.dir/build.make tpcc_run/tpc-c/CMakeFiles/simple_consistency_check.dir/support.cpp.o
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/support.cpp.o
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_load.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_load.dir/support.cpp.o
.PHONY : support.cpp.o

support.i: support.cpp.i

.PHONY : support.i

# target to preprocess a source file
support.cpp.i:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/simple_consistency_check.dir/build.make tpcc_run/tpc-c/CMakeFiles/simple_consistency_check.dir/support.cpp.i
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/support.cpp.i
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_load.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_load.dir/support.cpp.i
.PHONY : support.cpp.i

support.s: support.cpp.s

.PHONY : support.s

# target to generate assembly for a file
support.cpp.s:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/simple_consistency_check.dir/build.make tpcc_run/tpc-c/CMakeFiles/simple_consistency_check.dir/support.cpp.s
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/support.cpp.s
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_load.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_load.dir/support.cpp.s
.PHONY : support.cpp.s

test_db2025_compatibility.o: test_db2025_compatibility.cpp.o

.PHONY : test_db2025_compatibility.o

# target to build an object file
test_db2025_compatibility.cpp.o:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/test_db2025_compatibility.dir/build.make tpcc_run/tpc-c/CMakeFiles/test_db2025_compatibility.dir/test_db2025_compatibility.cpp.o
.PHONY : test_db2025_compatibility.cpp.o

test_db2025_compatibility.i: test_db2025_compatibility.cpp.i

.PHONY : test_db2025_compatibility.i

# target to preprocess a source file
test_db2025_compatibility.cpp.i:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/test_db2025_compatibility.dir/build.make tpcc_run/tpc-c/CMakeFiles/test_db2025_compatibility.dir/test_db2025_compatibility.cpp.i
.PHONY : test_db2025_compatibility.cpp.i

test_db2025_compatibility.s: test_db2025_compatibility.cpp.s

.PHONY : test_db2025_compatibility.s

# target to generate assembly for a file
test_db2025_compatibility.cpp.s:
	cd /home/<USER>/db2025/db2025-x1/build && $(MAKE) -f tpcc_run/tpc-c/CMakeFiles/test_db2025_compatibility.dir/build.make tpcc_run/tpc-c/CMakeFiles/test_db2025_compatibility.dir/test_db2025_compatibility.cpp.s
.PHONY : test_db2025_compatibility.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... install/local"
	@echo "... edit_cache"
	@echo "... test"
	@echo "... install"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... simple_consistency_check"
	@echo "... install/strip"
	@echo "... test_db2025_compatibility"
	@echo "... tpcc_start"
	@echo "... tpcc_load"
	@echo "... delivery.o"
	@echo "... delivery.i"
	@echo "... delivery.s"
	@echo "... driver.o"
	@echo "... driver.i"
	@echo "... driver.s"
	@echo "... load.o"
	@echo "... load.i"
	@echo "... load.s"
	@echo "... neword.o"
	@echo "... neword.i"
	@echo "... neword.s"
	@echo "... ordstat.o"
	@echo "... ordstat.i"
	@echo "... ordstat.s"
	@echo "... payment.o"
	@echo "... payment.i"
	@echo "... payment.s"
	@echo "... rthist.o"
	@echo "... rthist.i"
	@echo "... rthist.s"
	@echo "... sb_percentile.o"
	@echo "... sb_percentile.i"
	@echo "... sb_percentile.s"
	@echo "... sequence.o"
	@echo "... sequence.i"
	@echo "... sequence.s"
	@echo "... simple_consistency_check.o"
	@echo "... simple_consistency_check.i"
	@echo "... simple_consistency_check.s"
	@echo "... slev.o"
	@echo "... slev.i"
	@echo "... slev.s"
	@echo "... spt_proc.o"
	@echo "... spt_proc.i"
	@echo "... spt_proc.s"
	@echo "... start.o"
	@echo "... start.i"
	@echo "... start.s"
	@echo "... support.o"
	@echo "... support.i"
	@echo "... support.s"
	@echo "... test_db2025_compatibility.o"
	@echo "... test_db2025_compatibility.i"
	@echo "... test_db2025_compatibility.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/db2025/db2025-x1/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

