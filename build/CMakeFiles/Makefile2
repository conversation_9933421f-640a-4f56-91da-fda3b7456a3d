# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/db2025/db2025-x1

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/db2025/db2025-x1/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: src/all
all: deps/all
all: tpcc_run/all

.PHONY : all

# The main recursive "preinstall" target.
preinstall: src/preinstall
preinstall: deps/preinstall
preinstall: tpcc_run/preinstall

.PHONY : preinstall

# The main recursive "clean" target.
clean: src/clean
clean: deps/clean
clean: tpcc_run/clean

.PHONY : clean

#=============================================================================
# Directory level rules for directory deps

# Recursive "all" directory target.
deps/all: deps/googletest/all

.PHONY : deps/all

# Recursive "preinstall" directory target.
deps/preinstall: deps/googletest/preinstall

.PHONY : deps/preinstall

# Recursive "clean" directory target.
deps/clean: deps/googletest/clean

.PHONY : deps/clean

#=============================================================================
# Directory level rules for directory deps/googletest

# Recursive "all" directory target.
deps/googletest/all: deps/googletest/googlemock/all

.PHONY : deps/googletest/all

# Recursive "preinstall" directory target.
deps/googletest/preinstall: deps/googletest/googlemock/preinstall

.PHONY : deps/googletest/preinstall

# Recursive "clean" directory target.
deps/googletest/clean: deps/googletest/googlemock/clean

.PHONY : deps/googletest/clean

#=============================================================================
# Directory level rules for directory deps/googletest/googlemock

# Recursive "all" directory target.
deps/googletest/googlemock/all: deps/googletest/googlemock/CMakeFiles/gmock_main.dir/all
deps/googletest/googlemock/all: deps/googletest/googlemock/CMakeFiles/gmock.dir/all
deps/googletest/googlemock/all: deps/googletest/googletest/all

.PHONY : deps/googletest/googlemock/all

# Recursive "preinstall" directory target.
deps/googletest/googlemock/preinstall: deps/googletest/googletest/preinstall

.PHONY : deps/googletest/googlemock/preinstall

# Recursive "clean" directory target.
deps/googletest/googlemock/clean: deps/googletest/googlemock/CMakeFiles/gmock_main.dir/clean
deps/googletest/googlemock/clean: deps/googletest/googlemock/CMakeFiles/gmock.dir/clean
deps/googletest/googlemock/clean: deps/googletest/googletest/clean

.PHONY : deps/googletest/googlemock/clean

#=============================================================================
# Directory level rules for directory deps/googletest/googletest

# Recursive "all" directory target.
deps/googletest/googletest/all: deps/googletest/googletest/CMakeFiles/gtest_main.dir/all
deps/googletest/googletest/all: deps/googletest/googletest/CMakeFiles/gtest.dir/all

.PHONY : deps/googletest/googletest/all

# Recursive "preinstall" directory target.
deps/googletest/googletest/preinstall:

.PHONY : deps/googletest/googletest/preinstall

# Recursive "clean" directory target.
deps/googletest/googletest/clean: deps/googletest/googletest/CMakeFiles/gtest_main.dir/clean
deps/googletest/googletest/clean: deps/googletest/googletest/CMakeFiles/gtest.dir/clean

.PHONY : deps/googletest/googletest/clean

#=============================================================================
# Directory level rules for directory src

# Recursive "all" directory target.
src/all: src/CMakeFiles/reconstructuple_test.dir/all
src/all: src/CMakeFiles/unit_test.dir/all
src/all: src/CMakeFiles/rmdb.dir/all
src/all: src/analyze/all
src/all: src/record/all
src/all: src/index/all
src/all: src/system/all
src/all: src/execution/all
src/all: src/parser/all
src/all: src/optimizer/all
src/all: src/storage/all
src/all: src/common/all
src/all: src/replacer/all
src/all: src/transaction/all
src/all: src/recovery/all
src/all: src/test/all

.PHONY : src/all

# Recursive "preinstall" directory target.
src/preinstall: src/analyze/preinstall
src/preinstall: src/record/preinstall
src/preinstall: src/index/preinstall
src/preinstall: src/system/preinstall
src/preinstall: src/execution/preinstall
src/preinstall: src/parser/preinstall
src/preinstall: src/optimizer/preinstall
src/preinstall: src/storage/preinstall
src/preinstall: src/common/preinstall
src/preinstall: src/replacer/preinstall
src/preinstall: src/transaction/preinstall
src/preinstall: src/recovery/preinstall
src/preinstall: src/test/preinstall

.PHONY : src/preinstall

# Recursive "clean" directory target.
src/clean: src/CMakeFiles/reconstructuple_test.dir/clean
src/clean: src/CMakeFiles/unit_test.dir/clean
src/clean: src/CMakeFiles/rmdb.dir/clean
src/clean: src/analyze/clean
src/clean: src/record/clean
src/clean: src/index/clean
src/clean: src/system/clean
src/clean: src/execution/clean
src/clean: src/parser/clean
src/clean: src/optimizer/clean
src/clean: src/storage/clean
src/clean: src/common/clean
src/clean: src/replacer/clean
src/clean: src/transaction/clean
src/clean: src/recovery/clean
src/clean: src/test/clean

.PHONY : src/clean

#=============================================================================
# Directory level rules for directory src/analyze

# Recursive "all" directory target.
src/analyze/all: src/analyze/CMakeFiles/analyze.dir/all

.PHONY : src/analyze/all

# Recursive "preinstall" directory target.
src/analyze/preinstall:

.PHONY : src/analyze/preinstall

# Recursive "clean" directory target.
src/analyze/clean: src/analyze/CMakeFiles/analyze.dir/clean

.PHONY : src/analyze/clean

#=============================================================================
# Directory level rules for directory src/common

# Recursive "all" directory target.
src/common/all:

.PHONY : src/common/all

# Recursive "preinstall" directory target.
src/common/preinstall:

.PHONY : src/common/preinstall

# Recursive "clean" directory target.
src/common/clean:

.PHONY : src/common/clean

#=============================================================================
# Directory level rules for directory src/execution

# Recursive "all" directory target.
src/execution/all: src/execution/CMakeFiles/execution.dir/all

.PHONY : src/execution/all

# Recursive "preinstall" directory target.
src/execution/preinstall:

.PHONY : src/execution/preinstall

# Recursive "clean" directory target.
src/execution/clean: src/execution/CMakeFiles/execution.dir/clean

.PHONY : src/execution/clean

#=============================================================================
# Directory level rules for directory src/index

# Recursive "all" directory target.
src/index/all: src/index/CMakeFiles/index.dir/all

.PHONY : src/index/all

# Recursive "preinstall" directory target.
src/index/preinstall:

.PHONY : src/index/preinstall

# Recursive "clean" directory target.
src/index/clean: src/index/CMakeFiles/index.dir/clean

.PHONY : src/index/clean

#=============================================================================
# Directory level rules for directory src/optimizer

# Recursive "all" directory target.
src/optimizer/all: src/optimizer/CMakeFiles/planner.dir/all

.PHONY : src/optimizer/all

# Recursive "preinstall" directory target.
src/optimizer/preinstall:

.PHONY : src/optimizer/preinstall

# Recursive "clean" directory target.
src/optimizer/clean: src/optimizer/CMakeFiles/planner.dir/clean

.PHONY : src/optimizer/clean

#=============================================================================
# Directory level rules for directory src/parser

# Recursive "all" directory target.
src/parser/all: src/parser/CMakeFiles/test_parser.dir/all
src/parser/all: src/parser/CMakeFiles/parser.dir/all

.PHONY : src/parser/all

# Recursive "preinstall" directory target.
src/parser/preinstall:

.PHONY : src/parser/preinstall

# Recursive "clean" directory target.
src/parser/clean: src/parser/CMakeFiles/test_parser.dir/clean
src/parser/clean: src/parser/CMakeFiles/parser.dir/clean

.PHONY : src/parser/clean

#=============================================================================
# Directory level rules for directory src/record

# Recursive "all" directory target.
src/record/all: src/record/CMakeFiles/records.dir/all
src/record/all: src/record/CMakeFiles/record.dir/all

.PHONY : src/record/all

# Recursive "preinstall" directory target.
src/record/preinstall:

.PHONY : src/record/preinstall

# Recursive "clean" directory target.
src/record/clean: src/record/CMakeFiles/records.dir/clean
src/record/clean: src/record/CMakeFiles/record.dir/clean

.PHONY : src/record/clean

#=============================================================================
# Directory level rules for directory src/recovery

# Recursive "all" directory target.
src/recovery/all: src/recovery/CMakeFiles/recoverys.dir/all
src/recovery/all: src/recovery/CMakeFiles/recovery.dir/all

.PHONY : src/recovery/all

# Recursive "preinstall" directory target.
src/recovery/preinstall:

.PHONY : src/recovery/preinstall

# Recursive "clean" directory target.
src/recovery/clean: src/recovery/CMakeFiles/recoverys.dir/clean
src/recovery/clean: src/recovery/CMakeFiles/recovery.dir/clean

.PHONY : src/recovery/clean

#=============================================================================
# Directory level rules for directory src/replacer

# Recursive "all" directory target.
src/replacer/all: src/replacer/CMakeFiles/lru_replacer.dir/all

.PHONY : src/replacer/all

# Recursive "preinstall" directory target.
src/replacer/preinstall:

.PHONY : src/replacer/preinstall

# Recursive "clean" directory target.
src/replacer/clean: src/replacer/CMakeFiles/lru_replacer.dir/clean

.PHONY : src/replacer/clean

#=============================================================================
# Directory level rules for directory src/storage

# Recursive "all" directory target.
src/storage/all: src/storage/CMakeFiles/storage.dir/all

.PHONY : src/storage/all

# Recursive "preinstall" directory target.
src/storage/preinstall:

.PHONY : src/storage/preinstall

# Recursive "clean" directory target.
src/storage/clean: src/storage/CMakeFiles/storage.dir/clean

.PHONY : src/storage/clean

#=============================================================================
# Directory level rules for directory src/system

# Recursive "all" directory target.
src/system/all: src/system/CMakeFiles/system.dir/all

.PHONY : src/system/all

# Recursive "preinstall" directory target.
src/system/preinstall:

.PHONY : src/system/preinstall

# Recursive "clean" directory target.
src/system/clean: src/system/CMakeFiles/system.dir/clean

.PHONY : src/system/clean

#=============================================================================
# Directory level rules for directory src/test

# Recursive "all" directory target.
src/test/all:

.PHONY : src/test/all

# Recursive "preinstall" directory target.
src/test/preinstall:

.PHONY : src/test/preinstall

# Recursive "clean" directory target.
src/test/clean:

.PHONY : src/test/clean

#=============================================================================
# Directory level rules for directory src/transaction

# Recursive "all" directory target.
src/transaction/all: src/transaction/CMakeFiles/transaction.dir/all

.PHONY : src/transaction/all

# Recursive "preinstall" directory target.
src/transaction/preinstall:

.PHONY : src/transaction/preinstall

# Recursive "clean" directory target.
src/transaction/clean: src/transaction/CMakeFiles/transaction.dir/clean

.PHONY : src/transaction/clean

#=============================================================================
# Directory level rules for directory tpcc_run

# Recursive "all" directory target.
tpcc_run/all: tpcc_run/tpc-c/all

.PHONY : tpcc_run/all

# Recursive "preinstall" directory target.
tpcc_run/preinstall: tpcc_run/tpc-c/preinstall

.PHONY : tpcc_run/preinstall

# Recursive "clean" directory target.
tpcc_run/clean: tpcc_run/tpc-c/clean

.PHONY : tpcc_run/clean

#=============================================================================
# Directory level rules for directory tpcc_run/tpc-c

# Recursive "all" directory target.
tpcc_run/tpc-c/all: tpcc_run/tpc-c/CMakeFiles/simple_consistency_check.dir/all
tpcc_run/tpc-c/all: tpcc_run/tpc-c/CMakeFiles/test_db2025_compatibility.dir/all
tpcc_run/tpc-c/all: tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/all
tpcc_run/tpc-c/all: tpcc_run/tpc-c/CMakeFiles/tpcc_load.dir/all

.PHONY : tpcc_run/tpc-c/all

# Recursive "preinstall" directory target.
tpcc_run/tpc-c/preinstall:

.PHONY : tpcc_run/tpc-c/preinstall

# Recursive "clean" directory target.
tpcc_run/tpc-c/clean: tpcc_run/tpc-c/CMakeFiles/simple_consistency_check.dir/clean
tpcc_run/tpc-c/clean: tpcc_run/tpc-c/CMakeFiles/test_db2025_compatibility.dir/clean
tpcc_run/tpc-c/clean: tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/clean
tpcc_run/tpc-c/clean: tpcc_run/tpc-c/CMakeFiles/tpcc_load.dir/clean

.PHONY : tpcc_run/tpc-c/clean

#=============================================================================
# Target rules for target src/CMakeFiles/reconstructuple_test.dir

# All Build rule for target.
src/CMakeFiles/reconstructuple_test.dir/all: src/analyze/CMakeFiles/analyze.dir/all
src/CMakeFiles/reconstructuple_test.dir/all: src/record/CMakeFiles/record.dir/all
src/CMakeFiles/reconstructuple_test.dir/all: src/index/CMakeFiles/index.dir/all
src/CMakeFiles/reconstructuple_test.dir/all: src/execution/CMakeFiles/execution.dir/all
src/CMakeFiles/reconstructuple_test.dir/all: src/parser/CMakeFiles/parser.dir/all
src/CMakeFiles/reconstructuple_test.dir/all: src/optimizer/CMakeFiles/planner.dir/all
src/CMakeFiles/reconstructuple_test.dir/all: src/storage/CMakeFiles/storage.dir/all
src/CMakeFiles/reconstructuple_test.dir/all: deps/googletest/googletest/CMakeFiles/gtest_main.dir/all
src/CMakeFiles/reconstructuple_test.dir/all: deps/googletest/googletest/CMakeFiles/gtest.dir/all
	$(MAKE) -f src/CMakeFiles/reconstructuple_test.dir/build.make src/CMakeFiles/reconstructuple_test.dir/depend
	$(MAKE) -f src/CMakeFiles/reconstructuple_test.dir/build.make src/CMakeFiles/reconstructuple_test.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=29,30 "Built target reconstructuple_test"
.PHONY : src/CMakeFiles/reconstructuple_test.dir/all

# Build rule for subdir invocation for target.
src/CMakeFiles/reconstructuple_test.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 40
	$(MAKE) -f CMakeFiles/Makefile2 src/CMakeFiles/reconstructuple_test.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 0
.PHONY : src/CMakeFiles/reconstructuple_test.dir/rule

# Convenience name for target.
reconstructuple_test: src/CMakeFiles/reconstructuple_test.dir/rule

.PHONY : reconstructuple_test

# clean rule for target.
src/CMakeFiles/reconstructuple_test.dir/clean:
	$(MAKE) -f src/CMakeFiles/reconstructuple_test.dir/build.make src/CMakeFiles/reconstructuple_test.dir/clean
.PHONY : src/CMakeFiles/reconstructuple_test.dir/clean

#=============================================================================
# Target rules for target src/CMakeFiles/unit_test.dir

# All Build rule for target.
src/CMakeFiles/unit_test.dir/all: src/record/CMakeFiles/record.dir/all
src/CMakeFiles/unit_test.dir/all: src/index/CMakeFiles/index.dir/all
src/CMakeFiles/unit_test.dir/all: src/storage/CMakeFiles/storage.dir/all
src/CMakeFiles/unit_test.dir/all: src/replacer/CMakeFiles/lru_replacer.dir/all
src/CMakeFiles/unit_test.dir/all: deps/googletest/googletest/CMakeFiles/gtest_main.dir/all
src/CMakeFiles/unit_test.dir/all: deps/googletest/googletest/CMakeFiles/gtest.dir/all
	$(MAKE) -f src/CMakeFiles/unit_test.dir/build.make src/CMakeFiles/unit_test.dir/depend
	$(MAKE) -f src/CMakeFiles/unit_test.dir/build.make src/CMakeFiles/unit_test.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=78,79 "Built target unit_test"
.PHONY : src/CMakeFiles/unit_test.dir/all

# Build rule for subdir invocation for target.
src/CMakeFiles/unit_test.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 27
	$(MAKE) -f CMakeFiles/Makefile2 src/CMakeFiles/unit_test.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 0
.PHONY : src/CMakeFiles/unit_test.dir/rule

# Convenience name for target.
unit_test: src/CMakeFiles/unit_test.dir/rule

.PHONY : unit_test

# clean rule for target.
src/CMakeFiles/unit_test.dir/clean:
	$(MAKE) -f src/CMakeFiles/unit_test.dir/build.make src/CMakeFiles/unit_test.dir/clean
.PHONY : src/CMakeFiles/unit_test.dir/clean

#=============================================================================
# Target rules for target src/CMakeFiles/rmdb.dir

# All Build rule for target.
src/CMakeFiles/rmdb.dir/all: src/analyze/CMakeFiles/analyze.dir/all
src/CMakeFiles/rmdb.dir/all: src/record/CMakeFiles/record.dir/all
src/CMakeFiles/rmdb.dir/all: src/index/CMakeFiles/index.dir/all
src/CMakeFiles/rmdb.dir/all: src/execution/CMakeFiles/execution.dir/all
src/CMakeFiles/rmdb.dir/all: src/parser/CMakeFiles/parser.dir/all
src/CMakeFiles/rmdb.dir/all: src/optimizer/CMakeFiles/planner.dir/all
src/CMakeFiles/rmdb.dir/all: src/storage/CMakeFiles/storage.dir/all
	$(MAKE) -f src/CMakeFiles/rmdb.dir/build.make src/CMakeFiles/rmdb.dir/depend
	$(MAKE) -f src/CMakeFiles/rmdb.dir/build.make src/CMakeFiles/rmdb.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=43,44 "Built target rmdb"
.PHONY : src/CMakeFiles/rmdb.dir/all

# Build rule for subdir invocation for target.
src/CMakeFiles/rmdb.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 36
	$(MAKE) -f CMakeFiles/Makefile2 src/CMakeFiles/rmdb.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 0
.PHONY : src/CMakeFiles/rmdb.dir/rule

# Convenience name for target.
rmdb: src/CMakeFiles/rmdb.dir/rule

.PHONY : rmdb

# clean rule for target.
src/CMakeFiles/rmdb.dir/clean:
	$(MAKE) -f src/CMakeFiles/rmdb.dir/build.make src/CMakeFiles/rmdb.dir/clean
.PHONY : src/CMakeFiles/rmdb.dir/clean

#=============================================================================
# Target rules for target src/analyze/CMakeFiles/analyze.dir

# All Build rule for target.
src/analyze/CMakeFiles/analyze.dir/all:
	$(MAKE) -f src/analyze/CMakeFiles/analyze.dir/build.make src/analyze/CMakeFiles/analyze.dir/depend
	$(MAKE) -f src/analyze/CMakeFiles/analyze.dir/build.make src/analyze/CMakeFiles/analyze.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=1,2 "Built target analyze"
.PHONY : src/analyze/CMakeFiles/analyze.dir/all

# Build rule for subdir invocation for target.
src/analyze/CMakeFiles/analyze.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 2
	$(MAKE) -f CMakeFiles/Makefile2 src/analyze/CMakeFiles/analyze.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 0
.PHONY : src/analyze/CMakeFiles/analyze.dir/rule

# Convenience name for target.
analyze: src/analyze/CMakeFiles/analyze.dir/rule

.PHONY : analyze

# clean rule for target.
src/analyze/CMakeFiles/analyze.dir/clean:
	$(MAKE) -f src/analyze/CMakeFiles/analyze.dir/build.make src/analyze/CMakeFiles/analyze.dir/clean
.PHONY : src/analyze/CMakeFiles/analyze.dir/clean

#=============================================================================
# Target rules for target src/record/CMakeFiles/records.dir

# All Build rule for target.
src/record/CMakeFiles/records.dir/all:
	$(MAKE) -f src/record/CMakeFiles/records.dir/build.make src/record/CMakeFiles/records.dir/depend
	$(MAKE) -f src/record/CMakeFiles/records.dir/build.make src/record/CMakeFiles/records.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=34,35,36 "Built target records"
.PHONY : src/record/CMakeFiles/records.dir/all

# Build rule for subdir invocation for target.
src/record/CMakeFiles/records.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 3
	$(MAKE) -f CMakeFiles/Makefile2 src/record/CMakeFiles/records.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 0
.PHONY : src/record/CMakeFiles/records.dir/rule

# Convenience name for target.
records: src/record/CMakeFiles/records.dir/rule

.PHONY : records

# clean rule for target.
src/record/CMakeFiles/records.dir/clean:
	$(MAKE) -f src/record/CMakeFiles/records.dir/build.make src/record/CMakeFiles/records.dir/clean
.PHONY : src/record/CMakeFiles/records.dir/clean

#=============================================================================
# Target rules for target src/record/CMakeFiles/record.dir

# All Build rule for target.
src/record/CMakeFiles/record.dir/all: src/system/CMakeFiles/system.dir/all
	$(MAKE) -f src/record/CMakeFiles/record.dir/build.make src/record/CMakeFiles/record.dir/depend
	$(MAKE) -f src/record/CMakeFiles/record.dir/build.make src/record/CMakeFiles/record.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=31,32,33 "Built target record"
.PHONY : src/record/CMakeFiles/record.dir/all

# Build rule for subdir invocation for target.
src/record/CMakeFiles/record.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 19
	$(MAKE) -f CMakeFiles/Makefile2 src/record/CMakeFiles/record.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 0
.PHONY : src/record/CMakeFiles/record.dir/rule

# Convenience name for target.
record: src/record/CMakeFiles/record.dir/rule

.PHONY : record

# clean rule for target.
src/record/CMakeFiles/record.dir/clean:
	$(MAKE) -f src/record/CMakeFiles/record.dir/build.make src/record/CMakeFiles/record.dir/clean
.PHONY : src/record/CMakeFiles/record.dir/clean

#=============================================================================
# Target rules for target src/index/CMakeFiles/index.dir

# All Build rule for target.
src/index/CMakeFiles/index.dir/all: src/storage/CMakeFiles/storage.dir/all
	$(MAKE) -f src/index/CMakeFiles/index.dir/build.make src/index/CMakeFiles/index.dir/depend
	$(MAKE) -f src/index/CMakeFiles/index.dir/build.make src/index/CMakeFiles/index.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=16,17,18 "Built target index"
.PHONY : src/index/CMakeFiles/index.dir/all

# Build rule for subdir invocation for target.
src/index/CMakeFiles/index.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 7
	$(MAKE) -f CMakeFiles/Makefile2 src/index/CMakeFiles/index.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 0
.PHONY : src/index/CMakeFiles/index.dir/rule

# Convenience name for target.
index: src/index/CMakeFiles/index.dir/rule

.PHONY : index

# clean rule for target.
src/index/CMakeFiles/index.dir/clean:
	$(MAKE) -f src/index/CMakeFiles/index.dir/build.make src/index/CMakeFiles/index.dir/clean
.PHONY : src/index/CMakeFiles/index.dir/clean

#=============================================================================
# Target rules for target src/system/CMakeFiles/system.dir

# All Build rule for target.
src/system/CMakeFiles/system.dir/all: src/transaction/CMakeFiles/transaction.dir/all
	$(MAKE) -f src/system/CMakeFiles/system.dir/build.make src/system/CMakeFiles/system.dir/depend
	$(MAKE) -f src/system/CMakeFiles/system.dir/build.make src/system/CMakeFiles/system.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=52,53 "Built target system"
.PHONY : src/system/CMakeFiles/system.dir/all

# Build rule for subdir invocation for target.
src/system/CMakeFiles/system.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 16
	$(MAKE) -f CMakeFiles/Makefile2 src/system/CMakeFiles/system.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 0
.PHONY : src/system/CMakeFiles/system.dir/rule

# Convenience name for target.
system: src/system/CMakeFiles/system.dir/rule

.PHONY : system

# clean rule for target.
src/system/CMakeFiles/system.dir/clean:
	$(MAKE) -f src/system/CMakeFiles/system.dir/build.make src/system/CMakeFiles/system.dir/clean
.PHONY : src/system/CMakeFiles/system.dir/clean

#=============================================================================
# Target rules for target src/execution/CMakeFiles/execution.dir

# All Build rule for target.
src/execution/CMakeFiles/execution.dir/all: src/record/CMakeFiles/record.dir/all
src/execution/CMakeFiles/execution.dir/all: src/index/CMakeFiles/index.dir/all
src/execution/CMakeFiles/execution.dir/all: src/optimizer/CMakeFiles/planner.dir/all
src/execution/CMakeFiles/execution.dir/all: src/storage/CMakeFiles/storage.dir/all
	$(MAKE) -f src/execution/CMakeFiles/execution.dir/build.make src/execution/CMakeFiles/execution.dir/depend
	$(MAKE) -f src/execution/CMakeFiles/execution.dir/build.make src/execution/CMakeFiles/execution.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=3,4,5,6,7 "Built target execution"
.PHONY : src/execution/CMakeFiles/execution.dir/all

# Build rule for subdir invocation for target.
src/execution/CMakeFiles/execution.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 26
	$(MAKE) -f CMakeFiles/Makefile2 src/execution/CMakeFiles/execution.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 0
.PHONY : src/execution/CMakeFiles/execution.dir/rule

# Convenience name for target.
execution: src/execution/CMakeFiles/execution.dir/rule

.PHONY : execution

# clean rule for target.
src/execution/CMakeFiles/execution.dir/clean:
	$(MAKE) -f src/execution/CMakeFiles/execution.dir/build.make src/execution/CMakeFiles/execution.dir/clean
.PHONY : src/execution/CMakeFiles/execution.dir/clean

#=============================================================================
# Target rules for target src/parser/CMakeFiles/test_parser.dir

# All Build rule for target.
src/parser/CMakeFiles/test_parser.dir/all: src/record/CMakeFiles/record.dir/all
src/parser/CMakeFiles/test_parser.dir/all: src/index/CMakeFiles/index.dir/all
src/parser/CMakeFiles/test_parser.dir/all: src/execution/CMakeFiles/execution.dir/all
src/parser/CMakeFiles/test_parser.dir/all: src/parser/CMakeFiles/parser.dir/all
src/parser/CMakeFiles/test_parser.dir/all: src/optimizer/CMakeFiles/planner.dir/all
src/parser/CMakeFiles/test_parser.dir/all: src/storage/CMakeFiles/storage.dir/all
	$(MAKE) -f src/parser/CMakeFiles/test_parser.dir/build.make src/parser/CMakeFiles/test_parser.dir/depend
	$(MAKE) -f src/parser/CMakeFiles/test_parser.dir/build.make src/parser/CMakeFiles/test_parser.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=56,57 "Built target test_parser"
.PHONY : src/parser/CMakeFiles/test_parser.dir/all

# Build rule for subdir invocation for target.
src/parser/CMakeFiles/test_parser.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 34
	$(MAKE) -f CMakeFiles/Makefile2 src/parser/CMakeFiles/test_parser.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 0
.PHONY : src/parser/CMakeFiles/test_parser.dir/rule

# Convenience name for target.
test_parser: src/parser/CMakeFiles/test_parser.dir/rule

.PHONY : test_parser

# clean rule for target.
src/parser/CMakeFiles/test_parser.dir/clean:
	$(MAKE) -f src/parser/CMakeFiles/test_parser.dir/build.make src/parser/CMakeFiles/test_parser.dir/clean
.PHONY : src/parser/CMakeFiles/test_parser.dir/clean

#=============================================================================
# Target rules for target src/parser/CMakeFiles/parser.dir

# All Build rule for target.
src/parser/CMakeFiles/parser.dir/all: src/record/CMakeFiles/record.dir/all
src/parser/CMakeFiles/parser.dir/all: src/index/CMakeFiles/index.dir/all
src/parser/CMakeFiles/parser.dir/all: src/execution/CMakeFiles/execution.dir/all
src/parser/CMakeFiles/parser.dir/all: src/optimizer/CMakeFiles/planner.dir/all
src/parser/CMakeFiles/parser.dir/all: src/storage/CMakeFiles/storage.dir/all
	$(MAKE) -f src/parser/CMakeFiles/parser.dir/build.make src/parser/CMakeFiles/parser.dir/depend
	$(MAKE) -f src/parser/CMakeFiles/parser.dir/build.make src/parser/CMakeFiles/parser.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=21,22,23,24,25,26 "Built target parser"
.PHONY : src/parser/CMakeFiles/parser.dir/all

# Build rule for subdir invocation for target.
src/parser/CMakeFiles/parser.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 32
	$(MAKE) -f CMakeFiles/Makefile2 src/parser/CMakeFiles/parser.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 0
.PHONY : src/parser/CMakeFiles/parser.dir/rule

# Convenience name for target.
parser: src/parser/CMakeFiles/parser.dir/rule

.PHONY : parser

# clean rule for target.
src/parser/CMakeFiles/parser.dir/clean:
	$(MAKE) -f src/parser/CMakeFiles/parser.dir/build.make src/parser/CMakeFiles/parser.dir/clean
.PHONY : src/parser/CMakeFiles/parser.dir/clean

#=============================================================================
# Target rules for target src/optimizer/CMakeFiles/planner.dir

# All Build rule for target.
src/optimizer/CMakeFiles/planner.dir/all:
	$(MAKE) -f src/optimizer/CMakeFiles/planner.dir/build.make src/optimizer/CMakeFiles/planner.dir/depend
	$(MAKE) -f src/optimizer/CMakeFiles/planner.dir/build.make src/optimizer/CMakeFiles/planner.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=27,28 "Built target planner"
.PHONY : src/optimizer/CMakeFiles/planner.dir/all

# Build rule for subdir invocation for target.
src/optimizer/CMakeFiles/planner.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 2
	$(MAKE) -f CMakeFiles/Makefile2 src/optimizer/CMakeFiles/planner.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 0
.PHONY : src/optimizer/CMakeFiles/planner.dir/rule

# Convenience name for target.
planner: src/optimizer/CMakeFiles/planner.dir/rule

.PHONY : planner

# clean rule for target.
src/optimizer/CMakeFiles/planner.dir/clean:
	$(MAKE) -f src/optimizer/CMakeFiles/planner.dir/build.make src/optimizer/CMakeFiles/planner.dir/clean
.PHONY : src/optimizer/CMakeFiles/planner.dir/clean

#=============================================================================
# Target rules for target src/storage/CMakeFiles/storage.dir

# All Build rule for target.
src/storage/CMakeFiles/storage.dir/all:
	$(MAKE) -f src/storage/CMakeFiles/storage.dir/build.make src/storage/CMakeFiles/storage.dir/depend
	$(MAKE) -f src/storage/CMakeFiles/storage.dir/build.make src/storage/CMakeFiles/storage.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=48,49,50,51 "Built target storage"
.PHONY : src/storage/CMakeFiles/storage.dir/all

# Build rule for subdir invocation for target.
src/storage/CMakeFiles/storage.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 4
	$(MAKE) -f CMakeFiles/Makefile2 src/storage/CMakeFiles/storage.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 0
.PHONY : src/storage/CMakeFiles/storage.dir/rule

# Convenience name for target.
storage: src/storage/CMakeFiles/storage.dir/rule

.PHONY : storage

# clean rule for target.
src/storage/CMakeFiles/storage.dir/clean:
	$(MAKE) -f src/storage/CMakeFiles/storage.dir/build.make src/storage/CMakeFiles/storage.dir/clean
.PHONY : src/storage/CMakeFiles/storage.dir/clean

#=============================================================================
# Target rules for target src/replacer/CMakeFiles/lru_replacer.dir

# All Build rule for target.
src/replacer/CMakeFiles/lru_replacer.dir/all:
	$(MAKE) -f src/replacer/CMakeFiles/lru_replacer.dir/build.make src/replacer/CMakeFiles/lru_replacer.dir/depend
	$(MAKE) -f src/replacer/CMakeFiles/lru_replacer.dir/build.make src/replacer/CMakeFiles/lru_replacer.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=19,20 "Built target lru_replacer"
.PHONY : src/replacer/CMakeFiles/lru_replacer.dir/all

# Build rule for subdir invocation for target.
src/replacer/CMakeFiles/lru_replacer.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 2
	$(MAKE) -f CMakeFiles/Makefile2 src/replacer/CMakeFiles/lru_replacer.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 0
.PHONY : src/replacer/CMakeFiles/lru_replacer.dir/rule

# Convenience name for target.
lru_replacer: src/replacer/CMakeFiles/lru_replacer.dir/rule

.PHONY : lru_replacer

# clean rule for target.
src/replacer/CMakeFiles/lru_replacer.dir/clean:
	$(MAKE) -f src/replacer/CMakeFiles/lru_replacer.dir/build.make src/replacer/CMakeFiles/lru_replacer.dir/clean
.PHONY : src/replacer/CMakeFiles/lru_replacer.dir/clean

#=============================================================================
# Target rules for target src/transaction/CMakeFiles/transaction.dir

# All Build rule for target.
src/transaction/CMakeFiles/transaction.dir/all: src/recovery/CMakeFiles/recovery.dir/all
	$(MAKE) -f src/transaction/CMakeFiles/transaction.dir/build.make src/transaction/CMakeFiles/transaction.dir/depend
	$(MAKE) -f src/transaction/CMakeFiles/transaction.dir/build.make src/transaction/CMakeFiles/transaction.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=74,75,76,77 "Built target transaction"
.PHONY : src/transaction/CMakeFiles/transaction.dir/all

# Build rule for subdir invocation for target.
src/transaction/CMakeFiles/transaction.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 14
	$(MAKE) -f CMakeFiles/Makefile2 src/transaction/CMakeFiles/transaction.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 0
.PHONY : src/transaction/CMakeFiles/transaction.dir/rule

# Convenience name for target.
transaction: src/transaction/CMakeFiles/transaction.dir/rule

.PHONY : transaction

# clean rule for target.
src/transaction/CMakeFiles/transaction.dir/clean:
	$(MAKE) -f src/transaction/CMakeFiles/transaction.dir/build.make src/transaction/CMakeFiles/transaction.dir/clean
.PHONY : src/transaction/CMakeFiles/transaction.dir/clean

#=============================================================================
# Target rules for target src/recovery/CMakeFiles/recoverys.dir

# All Build rule for target.
src/recovery/CMakeFiles/recoverys.dir/all:
	$(MAKE) -f src/recovery/CMakeFiles/recoverys.dir/build.make src/recovery/CMakeFiles/recoverys.dir/depend
	$(MAKE) -f src/recovery/CMakeFiles/recoverys.dir/build.make src/recovery/CMakeFiles/recoverys.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=40,41,42 "Built target recoverys"
.PHONY : src/recovery/CMakeFiles/recoverys.dir/all

# Build rule for subdir invocation for target.
src/recovery/CMakeFiles/recoverys.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 3
	$(MAKE) -f CMakeFiles/Makefile2 src/recovery/CMakeFiles/recoverys.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 0
.PHONY : src/recovery/CMakeFiles/recoverys.dir/rule

# Convenience name for target.
recoverys: src/recovery/CMakeFiles/recoverys.dir/rule

.PHONY : recoverys

# clean rule for target.
src/recovery/CMakeFiles/recoverys.dir/clean:
	$(MAKE) -f src/recovery/CMakeFiles/recoverys.dir/build.make src/recovery/CMakeFiles/recoverys.dir/clean
.PHONY : src/recovery/CMakeFiles/recoverys.dir/clean

#=============================================================================
# Target rules for target src/recovery/CMakeFiles/recovery.dir

# All Build rule for target.
src/recovery/CMakeFiles/recovery.dir/all: src/index/CMakeFiles/index.dir/all
src/recovery/CMakeFiles/recovery.dir/all: src/storage/CMakeFiles/storage.dir/all
	$(MAKE) -f src/recovery/CMakeFiles/recovery.dir/build.make src/recovery/CMakeFiles/recovery.dir/depend
	$(MAKE) -f src/recovery/CMakeFiles/recovery.dir/build.make src/recovery/CMakeFiles/recovery.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=37,38,39 "Built target recovery"
.PHONY : src/recovery/CMakeFiles/recovery.dir/all

# Build rule for subdir invocation for target.
src/recovery/CMakeFiles/recovery.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 10
	$(MAKE) -f CMakeFiles/Makefile2 src/recovery/CMakeFiles/recovery.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 0
.PHONY : src/recovery/CMakeFiles/recovery.dir/rule

# Convenience name for target.
recovery: src/recovery/CMakeFiles/recovery.dir/rule

.PHONY : recovery

# clean rule for target.
src/recovery/CMakeFiles/recovery.dir/clean:
	$(MAKE) -f src/recovery/CMakeFiles/recovery.dir/build.make src/recovery/CMakeFiles/recovery.dir/clean
.PHONY : src/recovery/CMakeFiles/recovery.dir/clean

#=============================================================================
# Target rules for target deps/googletest/googlemock/CMakeFiles/gmock_main.dir

# All Build rule for target.
deps/googletest/googlemock/CMakeFiles/gmock_main.dir/all: deps/googletest/googlemock/CMakeFiles/gmock.dir/all
deps/googletest/googlemock/CMakeFiles/gmock_main.dir/all: deps/googletest/googletest/CMakeFiles/gtest.dir/all
	$(MAKE) -f deps/googletest/googlemock/CMakeFiles/gmock_main.dir/build.make deps/googletest/googlemock/CMakeFiles/gmock_main.dir/depend
	$(MAKE) -f deps/googletest/googlemock/CMakeFiles/gmock_main.dir/build.make deps/googletest/googlemock/CMakeFiles/gmock_main.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=10,11 "Built target gmock_main"
.PHONY : deps/googletest/googlemock/CMakeFiles/gmock_main.dir/all

# Build rule for subdir invocation for target.
deps/googletest/googlemock/CMakeFiles/gmock_main.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 6
	$(MAKE) -f CMakeFiles/Makefile2 deps/googletest/googlemock/CMakeFiles/gmock_main.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 0
.PHONY : deps/googletest/googlemock/CMakeFiles/gmock_main.dir/rule

# Convenience name for target.
gmock_main: deps/googletest/googlemock/CMakeFiles/gmock_main.dir/rule

.PHONY : gmock_main

# clean rule for target.
deps/googletest/googlemock/CMakeFiles/gmock_main.dir/clean:
	$(MAKE) -f deps/googletest/googlemock/CMakeFiles/gmock_main.dir/build.make deps/googletest/googlemock/CMakeFiles/gmock_main.dir/clean
.PHONY : deps/googletest/googlemock/CMakeFiles/gmock_main.dir/clean

#=============================================================================
# Target rules for target deps/googletest/googlemock/CMakeFiles/gmock.dir

# All Build rule for target.
deps/googletest/googlemock/CMakeFiles/gmock.dir/all: deps/googletest/googletest/CMakeFiles/gtest.dir/all
	$(MAKE) -f deps/googletest/googlemock/CMakeFiles/gmock.dir/build.make deps/googletest/googlemock/CMakeFiles/gmock.dir/depend
	$(MAKE) -f deps/googletest/googlemock/CMakeFiles/gmock.dir/build.make deps/googletest/googlemock/CMakeFiles/gmock.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=8,9 "Built target gmock"
.PHONY : deps/googletest/googlemock/CMakeFiles/gmock.dir/all

# Build rule for subdir invocation for target.
deps/googletest/googlemock/CMakeFiles/gmock.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 4
	$(MAKE) -f CMakeFiles/Makefile2 deps/googletest/googlemock/CMakeFiles/gmock.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 0
.PHONY : deps/googletest/googlemock/CMakeFiles/gmock.dir/rule

# Convenience name for target.
gmock: deps/googletest/googlemock/CMakeFiles/gmock.dir/rule

.PHONY : gmock

# clean rule for target.
deps/googletest/googlemock/CMakeFiles/gmock.dir/clean:
	$(MAKE) -f deps/googletest/googlemock/CMakeFiles/gmock.dir/build.make deps/googletest/googlemock/CMakeFiles/gmock.dir/clean
.PHONY : deps/googletest/googlemock/CMakeFiles/gmock.dir/clean

#=============================================================================
# Target rules for target deps/googletest/googletest/CMakeFiles/gtest_main.dir

# All Build rule for target.
deps/googletest/googletest/CMakeFiles/gtest_main.dir/all: deps/googletest/googletest/CMakeFiles/gtest.dir/all
	$(MAKE) -f deps/googletest/googletest/CMakeFiles/gtest_main.dir/build.make deps/googletest/googletest/CMakeFiles/gtest_main.dir/depend
	$(MAKE) -f deps/googletest/googletest/CMakeFiles/gtest_main.dir/build.make deps/googletest/googletest/CMakeFiles/gtest_main.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=14,15 "Built target gtest_main"
.PHONY : deps/googletest/googletest/CMakeFiles/gtest_main.dir/all

# Build rule for subdir invocation for target.
deps/googletest/googletest/CMakeFiles/gtest_main.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 4
	$(MAKE) -f CMakeFiles/Makefile2 deps/googletest/googletest/CMakeFiles/gtest_main.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 0
.PHONY : deps/googletest/googletest/CMakeFiles/gtest_main.dir/rule

# Convenience name for target.
gtest_main: deps/googletest/googletest/CMakeFiles/gtest_main.dir/rule

.PHONY : gtest_main

# clean rule for target.
deps/googletest/googletest/CMakeFiles/gtest_main.dir/clean:
	$(MAKE) -f deps/googletest/googletest/CMakeFiles/gtest_main.dir/build.make deps/googletest/googletest/CMakeFiles/gtest_main.dir/clean
.PHONY : deps/googletest/googletest/CMakeFiles/gtest_main.dir/clean

#=============================================================================
# Target rules for target deps/googletest/googletest/CMakeFiles/gtest.dir

# All Build rule for target.
deps/googletest/googletest/CMakeFiles/gtest.dir/all:
	$(MAKE) -f deps/googletest/googletest/CMakeFiles/gtest.dir/build.make deps/googletest/googletest/CMakeFiles/gtest.dir/depend
	$(MAKE) -f deps/googletest/googletest/CMakeFiles/gtest.dir/build.make deps/googletest/googletest/CMakeFiles/gtest.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=12,13 "Built target gtest"
.PHONY : deps/googletest/googletest/CMakeFiles/gtest.dir/all

# Build rule for subdir invocation for target.
deps/googletest/googletest/CMakeFiles/gtest.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 2
	$(MAKE) -f CMakeFiles/Makefile2 deps/googletest/googletest/CMakeFiles/gtest.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 0
.PHONY : deps/googletest/googletest/CMakeFiles/gtest.dir/rule

# Convenience name for target.
gtest: deps/googletest/googletest/CMakeFiles/gtest.dir/rule

.PHONY : gtest

# clean rule for target.
deps/googletest/googletest/CMakeFiles/gtest.dir/clean:
	$(MAKE) -f deps/googletest/googletest/CMakeFiles/gtest.dir/build.make deps/googletest/googletest/CMakeFiles/gtest.dir/clean
.PHONY : deps/googletest/googletest/CMakeFiles/gtest.dir/clean

#=============================================================================
# Target rules for target tpcc_run/tpc-c/CMakeFiles/simple_consistency_check.dir

# All Build rule for target.
tpcc_run/tpc-c/CMakeFiles/simple_consistency_check.dir/all:
	$(MAKE) -f tpcc_run/tpc-c/CMakeFiles/simple_consistency_check.dir/build.make tpcc_run/tpc-c/CMakeFiles/simple_consistency_check.dir/depend
	$(MAKE) -f tpcc_run/tpc-c/CMakeFiles/simple_consistency_check.dir/build.make tpcc_run/tpc-c/CMakeFiles/simple_consistency_check.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=45,46,47 "Built target simple_consistency_check"
.PHONY : tpcc_run/tpc-c/CMakeFiles/simple_consistency_check.dir/all

# Build rule for subdir invocation for target.
tpcc_run/tpc-c/CMakeFiles/simple_consistency_check.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 3
	$(MAKE) -f CMakeFiles/Makefile2 tpcc_run/tpc-c/CMakeFiles/simple_consistency_check.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 0
.PHONY : tpcc_run/tpc-c/CMakeFiles/simple_consistency_check.dir/rule

# Convenience name for target.
simple_consistency_check: tpcc_run/tpc-c/CMakeFiles/simple_consistency_check.dir/rule

.PHONY : simple_consistency_check

# clean rule for target.
tpcc_run/tpc-c/CMakeFiles/simple_consistency_check.dir/clean:
	$(MAKE) -f tpcc_run/tpc-c/CMakeFiles/simple_consistency_check.dir/build.make tpcc_run/tpc-c/CMakeFiles/simple_consistency_check.dir/clean
.PHONY : tpcc_run/tpc-c/CMakeFiles/simple_consistency_check.dir/clean

#=============================================================================
# Target rules for target tpcc_run/tpc-c/CMakeFiles/test_db2025_compatibility.dir

# All Build rule for target.
tpcc_run/tpc-c/CMakeFiles/test_db2025_compatibility.dir/all:
	$(MAKE) -f tpcc_run/tpc-c/CMakeFiles/test_db2025_compatibility.dir/build.make tpcc_run/tpc-c/CMakeFiles/test_db2025_compatibility.dir/depend
	$(MAKE) -f tpcc_run/tpc-c/CMakeFiles/test_db2025_compatibility.dir/build.make tpcc_run/tpc-c/CMakeFiles/test_db2025_compatibility.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=54,55 "Built target test_db2025_compatibility"
.PHONY : tpcc_run/tpc-c/CMakeFiles/test_db2025_compatibility.dir/all

# Build rule for subdir invocation for target.
tpcc_run/tpc-c/CMakeFiles/test_db2025_compatibility.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 2
	$(MAKE) -f CMakeFiles/Makefile2 tpcc_run/tpc-c/CMakeFiles/test_db2025_compatibility.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 0
.PHONY : tpcc_run/tpc-c/CMakeFiles/test_db2025_compatibility.dir/rule

# Convenience name for target.
test_db2025_compatibility: tpcc_run/tpc-c/CMakeFiles/test_db2025_compatibility.dir/rule

.PHONY : test_db2025_compatibility

# clean rule for target.
tpcc_run/tpc-c/CMakeFiles/test_db2025_compatibility.dir/clean:
	$(MAKE) -f tpcc_run/tpc-c/CMakeFiles/test_db2025_compatibility.dir/build.make tpcc_run/tpc-c/CMakeFiles/test_db2025_compatibility.dir/clean
.PHONY : tpcc_run/tpc-c/CMakeFiles/test_db2025_compatibility.dir/clean

#=============================================================================
# Target rules for target tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir

# All Build rule for target.
tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/all:
	$(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/depend
	$(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=61,62,63,64,65,66,67,68,69,70,71,72,73 "Built target tpcc_start"
.PHONY : tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/all

# Build rule for subdir invocation for target.
tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 13
	$(MAKE) -f CMakeFiles/Makefile2 tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 0
.PHONY : tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/rule

# Convenience name for target.
tpcc_start: tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/rule

.PHONY : tpcc_start

# clean rule for target.
tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/clean:
	$(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/clean
.PHONY : tpcc_run/tpc-c/CMakeFiles/tpcc_start.dir/clean

#=============================================================================
# Target rules for target tpcc_run/tpc-c/CMakeFiles/tpcc_load.dir

# All Build rule for target.
tpcc_run/tpc-c/CMakeFiles/tpcc_load.dir/all:
	$(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_load.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_load.dir/depend
	$(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_load.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_load.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/db2025/db2025-x1/build/CMakeFiles --progress-num=58,59,60 "Built target tpcc_load"
.PHONY : tpcc_run/tpc-c/CMakeFiles/tpcc_load.dir/all

# Build rule for subdir invocation for target.
tpcc_run/tpc-c/CMakeFiles/tpcc_load.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 3
	$(MAKE) -f CMakeFiles/Makefile2 tpcc_run/tpc-c/CMakeFiles/tpcc_load.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/db2025/db2025-x1/build/CMakeFiles 0
.PHONY : tpcc_run/tpc-c/CMakeFiles/tpcc_load.dir/rule

# Convenience name for target.
tpcc_load: tpcc_run/tpc-c/CMakeFiles/tpcc_load.dir/rule

.PHONY : tpcc_load

# clean rule for target.
tpcc_run/tpc-c/CMakeFiles/tpcc_load.dir/clean:
	$(MAKE) -f tpcc_run/tpc-c/CMakeFiles/tpcc_load.dir/build.make tpcc_run/tpc-c/CMakeFiles/tpcc_load.dir/clean
.PHONY : tpcc_run/tpc-c/CMakeFiles/tpcc_load.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

