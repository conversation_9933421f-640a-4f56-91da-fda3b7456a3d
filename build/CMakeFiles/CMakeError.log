Performing C SOURCE FILE Test CMAKE_HAVE_LIBC_PTHREAD failed with the following output:
Change Dir: /home/<USER>/db2025/db2025-x1/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_403c5/fast && /usr/bin/make -f CMakeFiles/cmTC_403c5.dir/build.make CMakeFiles/cmTC_403c5.dir/build
make[1]: Entering directory '/home/<USER>/db2025/db2025-x1/build/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_403c5.dir/src.c.o
/usr/bin/cc   -DCMAKE_HAVE_LIBC_PTHREAD   -o CMakeFiles/cmTC_403c5.dir/src.c.o   -c /home/<USER>/db2025/db2025-x1/build/CMakeFiles/CMakeTmp/src.c
Linking C executable cmTC_403c5
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_403c5.dir/link.txt --verbose=1
/usr/bin/cc  -DCMAKE_HAVE_LIBC_PTHREAD    CMakeFiles/cmTC_403c5.dir/src.c.o  -o cmTC_403c5 
/usr/bin/ld: CMakeFiles/cmTC_403c5.dir/src.c.o: in function `main':
src.c:(.text+0x46): undefined reference to `pthread_create'
/usr/bin/ld: src.c:(.text+0x52): undefined reference to `pthread_detach'
/usr/bin/ld: src.c:(.text+0x63): undefined reference to `pthread_join'
collect2: error: ld returned 1 exit status
make[1]: *** [CMakeFiles/cmTC_403c5.dir/build.make:87: cmTC_403c5] Error 1
make[1]: Leaving directory '/home/<USER>/db2025/db2025-x1/build/CMakeFiles/CMakeTmp'
make: *** [Makefile:121: cmTC_403c5/fast] Error 2


Source file was:
#include <pthread.h>

void* test_func(void* data)
{
  return data;
}

int main(void)
{
  pthread_t thread;
  pthread_create(&thread, NULL, test_func, NULL);
  pthread_detach(thread);
  pthread_join(thread, NULL);
  pthread_atfork(NULL, NULL, NULL);
  pthread_exit(NULL);

  return 0;
}

Determining if the function pthread_create exists in the pthreads failed with the following output:
Change Dir: /home/<USER>/db2025/db2025-x1/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_09083/fast && /usr/bin/make -f CMakeFiles/cmTC_09083.dir/build.make CMakeFiles/cmTC_09083.dir/build
make[1]: Entering directory '/home/<USER>/db2025/db2025-x1/build/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_09083.dir/CheckFunctionExists.c.o
/usr/bin/cc   -DCHECK_FUNCTION_EXISTS=pthread_create   -o CMakeFiles/cmTC_09083.dir/CheckFunctionExists.c.o   -c /usr/share/cmake-3.16/Modules/CheckFunctionExists.c
Linking C executable cmTC_09083
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_09083.dir/link.txt --verbose=1
/usr/bin/cc  -DCHECK_FUNCTION_EXISTS=pthread_create    CMakeFiles/cmTC_09083.dir/CheckFunctionExists.c.o  -o cmTC_09083  -lpthreads 
/usr/bin/ld: cannot find -lpthreads
collect2: error: ld returned 1 exit status
make[1]: *** [CMakeFiles/cmTC_09083.dir/build.make:87: cmTC_09083] Error 1
make[1]: Leaving directory '/home/<USER>/db2025/db2025-x1/build/CMakeFiles/CMakeTmp'
make: *** [Makefile:121: cmTC_09083/fast] Error 2



