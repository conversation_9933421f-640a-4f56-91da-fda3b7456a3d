-- 聚合函数优化测试脚本

-- 创建测试表
CREATE TABLE test_stock (
    s_w_id INT,
    s_i_id INT,
    s_quantity INT,
    s_data VARCHAR(50)
);

-- 插入测试数据
INSERT INTO test_stock VALUES (1, 100, 15, 'test data 1');
INSERT INTO test_stock VALUES (1, 101, 25, 'test data 2');
INSERT INTO test_stock VALUES (1, 102, 5, 'test data 3');
INSERT INTO test_stock VALUES (2, 100, 30, 'test data 4');
INSERT INTO test_stock VALUES (2, 101, 8, 'test data 5');

-- 测试简单COUNT(*)查询（应该使用优化路径）
SELECT COUNT(*) as total_count FROM test_stock;

-- 测试带WHERE条件的COUNT(*)查询（应该使用新的优化路径）
SELECT COUNT(*) as count_stock FROM test_stock WHERE s_w_id=1 AND s_i_id=100 AND s_quantity<20;

-- 测试简单SUM查询（应该使用新的优化路径）
SELECT SUM(s_quantity) as sum_quantity FROM test_stock WHERE s_w_id=1;

-- 测试简单MIN查询（应该使用新的优化路径）
SELECT MIN(s_quantity) as min_quantity FROM test_stock WHERE s_w_id=1;

-- 测试简单MAX查询（应该使用新的优化路径）
SELECT MAX(s_quantity) as max_quantity FROM test_stock WHERE s_w_id=2;

-- 测试COUNT(column)查询（应该使用新的优化路径）
SELECT COUNT(s_i_id) as count_items FROM test_stock WHERE s_w_id=1;

-- 清理测试数据
DROP TABLE test_stock;
