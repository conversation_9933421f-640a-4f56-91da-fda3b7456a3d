6. 2025题目五：聚合函数与分组统计
前置题目：查询执行

题目种类：基础功能



题目描述:

本题目要求实现聚合函数与分组统计。聚合函数：对一组值进行计算并返回单一的值，通过使用SQL聚合函数，可以确定数值集合的各种统计值。分组统计：将查询结果按照1个或者多个字段进行分组，字段值相同的为同一组。

 

要求：


聚合函数COUNT(expr)：其中expr可以是列名、"*"。count(*)统计元组个数，count(列名)统计一列中值的个数。该函数仅需支持int、float、char类型的字段。

聚合函数MAX(expr)：返回一列中的最大值。该函数仅需支持int、float类型的字段。

聚合函数MIN(expr)：返回一列中的最小值。该函数仅需支持int、float类型的字段。

聚合函数SUM(expr)：返回数值列的总数。该函数仅需支持int、float类型的字段。

聚合函数AVG(expr): 返回一列中的平均值。该函数仅需支持int、float类型的字段。

分组统计(group by、having)：group by子句根据一个或多个列对结果集进行分组，可以使用having子句对每一个分组按条件进行过滤。分组属性仅需支持int、float、char类型的字段。

排序统计：order by子句对最终结果按照一个或多个列进行排序，并可指定升序ASC或降序DESC，默认采用ASC。可以使用limit关键字限制查询结果的数量。




测试示例：

测试点1：单独使用聚合函数


测试示例:

create table grade (course char(20),id int,score float);

insert into grade values('DataStructure',1,95);

insert into grade values('DataStructure',2,93.5);

insert into grade values('DataStructure',4,87);

insert into grade values('DataStructure',3,85);

insert into grade values('DB',1,94);

insert into grade values('DB',2,74.5);

insert into grade values('DB',4,83);

insert into grade values('DB',3,87);

select MAX(id) as max_id from grade;

select MIN(score) as min_score from grade where course = 'DB';

select AVG(score) as avg_score from grade where course = 'DataStructure';

select COUNT(course) as course_num from grade;

select COUNT(*) as row_num from grade;

select COUNT(*) as row_num from grade where score < 60;

select SUM(score) as sum_score from grade where id = 1;

drop table grade;

期待输出：

| max_id |

| 4 |

| min_score |

| 74.500000 |

| avg_score |

| 90.125000 |

| course_num |

| 8 |

| row_num |

| 8 |

| row_num |

| 0 |

| sum_score |

| 189.000000 |

 

测试点2：聚合函数加分组统计


测试示例:

create table grade (course char(20),id int,score float);

insert into grade values('DataStructure',1,95);

insert into grade values('DataStructure',2,93.5);

insert into grade values('DataStructure',3,94.5);

insert into grade values('ComputerNetworks',1,99);

insert into grade values('ComputerNetworks',2,88.5);

insert into grade values('ComputerNetworks',3,92.5);

insert into grade values('C++',1,92);

insert into grade values('C++',2,89);

insert into grade values('C++',3,89.5);

select id,MAX(score) as max_score,MIN(score) as min_score,SUM(score) as sum_score from grade group by id;

select id,MAX(score) as max_score from grade group by id having COUNT(*) >  3;

insert into grade values ('ParallelCompute',1,100);

select id,MAX(score) as max_score from grade group by id having COUNT(*) >  3;

select id,MAX(score) as max_score,MIN(score) as min_score from grade group by id having COUNT(*) > 1 and MIN(score) > 88;

select course ,COUNT(*) as row_num , COUNT(id) as student_num , MAX(score) as top_score, MIN(score) as lowest_score from grade group by course;

select course, id, score from grade order by score desc;

drop table grade;

期待输出：

| id | max_score | min_score | sum_score |

| 1 |  99.000000 | 92.000000 | 286.000000 |

| 2 |  93.500000 | 88.500000 | 271.000000 |

| 3 |  94.500000 | 89.500000 | 276.500000 |

| id | max_score |

| id | max_score |

| 1 | 100.000000 |

| id | max_score | min_score |

| 1 | 100.000000 | 92.000000 |

| 2 | 93.500000 | 88.500000 |

| 3 | 94.500000 | 89.500000 |

| course | row_num | student_num | top_score | lowest_score |

| DataStructure | 3 | 3 | 95.000000 | 93.500000 |

| ComputerNetworks | 3 | 3 | 99.000000 | 88.500000 |

| C++ | 3 | 3 | 92.000000 | 89.000000 |

| ParallelCompute | 1 | 1 | 100.000000 | 100.000000 |

| course | id | score |

| ParallelCompute | 1 | 100.000000 |

| ComputerNetworks | 1 | 99.000000 |

| DataStructure | 1 | 95.000000 |

| DataStructure | 3 | 94.500000 |

| DataStructure | 2 | 93.500000 |

| ComputerNetworks | 3 | 92.500000 |

| C++ | 1 | 92.000000 |

| C++ | 3 | 89.500000 |

| C++ | 2 | 89.000000 |

| ComputerNetworks | 2 | 88.500000 |



测试点3：健壮性测试

测试示例:

create table grade (course char(20),id int,score float);

insert into grade values('DataStructure',1,95);

insert into grade values('DataStructure',2,93.5);

insert into grade values('DataStructure',3,94.5);

insert into grade values('ComputerNetworks',1,99);

insert into grade values('ComputerNetworks',2,88.5);

insert into grade values('ComputerNetworks',3,92.5);

-- SELECT 列表中不能出现没有在 GROUP BY 子句中的非聚集列

select id , score from grade group by course;

-- WHERE 子句中不能用聚集函数作为条件表达式

select id, MAX(score) as max_score from grade where MAX(score) > 90 group by id;

期待输出：

failure

failure

 

测试输出要求：

本题目的输出要求写入数据库文件夹下的output.txt文件中，例如测试数据库名称为execution_test_db，则在测试时使用./bin/rmdb execution_test_db命令来启动服务端，对应输出应写入buid/execution_test_db/output.txt文件中。