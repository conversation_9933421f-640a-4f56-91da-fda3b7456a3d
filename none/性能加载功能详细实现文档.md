# 性能加载功能详细实现文档

## 1. 概述与架构

### 1.1 功能概述
性能加载功能是RMDB数据库系统中的关键组件，负责高效地将大量数据从CSV文件批量导入到数据库表中。该功能专为TPC-C等性能测试基准设计，支持：
- 批量数据加载 (LOAD命令)
- CSV文件解析
- 批量插入优化
- 索引批量更新
- 内存管理优化

### 1.2 核心架构
```
LOAD命令 → 语法解析 → LoadExecutor → 批量处理 → 数据库存储
    ↓
CSV文件读取 → 数据解析 → 批量缓冲 → 批量插入 → 索引更新
```

### 1.3 性能优化策略
1. **批量处理**: 一次处理多条记录，减少I/O次数
2. **内存预分配**: 预分配缓冲区，避免频繁内存分配
3. **索引批量更新**: 批量更新索引，提高效率
4. **快速CSV解析**: 优化的CSV解析算法

## 2. LOAD命令语法解析

### 2.1 语法定义
**位置**: `src/parser/yacc.y:277-282`

```yacc
loadStmt:
    LOAD filename INTO tbName
    {
        $$ = std::make_shared<LoadStmt>($4, $2);
    }
    ;
```

**语法格式**:
```sql
LOAD file_name INTO table_name;
```

**示例**:
```sql
LOAD ../../src/test/performance_test/table_data/warehouse.csv INTO warehouse;
```

### 2.2 AST节点定义
**位置**: `src/parser/ast.h`

```cpp
struct LoadStmt : public TreeNode {
    std::string tab_name;   // 目标表名
    std::string file_name;  // CSV文件路径
    
    LoadStmt(std::string tab_name_, std::string file_name_) 
        : tab_name(std::move(tab_name_)), file_name(std::move(file_name_)) {}
};
```

## 3. LoadExecutor核心实现

### 3.1 执行器类定义
**位置**: `src/execution/executor_load.h:25-41`

```cpp
class LoadExecutor : public AbstractExecutor {
private:
    TabMeta tab_;                    // 表的元数据
    RmFileHandle *fh_;               // 表的数据文件句柄
    std::string tab_name_;           // 表名称
    std::string file_name_;          // 文件名称
    SmManager *sm_manager_;          // 系统管理器
    std::ifstream csv_file_;         // CSV文件流

    // 批量处理相关
    static constexpr size_t BATCH_SIZE = 10000;  // 批量处理大小
    std::vector<std::unique_ptr<char[]>> batch_records_;  // 批量记录缓冲区
    std::vector<Rid> batch_rids_;                         // 批量记录的RID
    size_t batch_count_;                                  // 当前批次记录数
    bool processing_complete_;                            // 处理是否完成
    Rid rid_;                                            // 最后插入记录的Rid
    std::chrono::steady_clock::time_point start_time_;   // 计时开始时间
};
```

**设计要点**:
- **BATCH_SIZE**: 设置为10000，平衡内存使用和I/O效率
- **batch_records_**: 预分配的记录缓冲区
- **batch_rids_**: 存储批量插入后的记录ID
- **计时功能**: 监控加载性能

### 3.2 构造函数实现
```cpp
LoadExecutor(SmManager *sm_manager, std::string tab_name, std::string file_name, Context *context) {
    sm_manager_ = sm_manager;
    tab_name_ = std::move(tab_name);
    file_name_ = std::move(file_name);
    context_ = context;
    
    // 获取表元数据
    tab_ = sm_manager_->db_.get_table(tab_name_);
    fh_ = sm_manager_->fhs_.at(tab_name_).get();
    
    // 初始化批量处理
    batch_count_ = 0;
    processing_complete_ = false;
    
    // 预分配批量记录缓冲区
    batch_records_.reserve(BATCH_SIZE);
    for (size_t i = 0; i < BATCH_SIZE; ++i) {
        batch_records_.emplace_back(std::make_unique<char[]>(tab_.get_col_tot_len()));
    }
    
    // 打开CSV文件
    csv_file_.open(file_name_);
    if (!csv_file_.is_open()) {
        throw RMDBError("Cannot open file: " + file_name_);
    }
    
    // 记录开始时间
    start_time_ = std::chrono::steady_clock::now();
}
```

**初始化流程**:
1. **元数据获取**: 获取目标表的元数据和文件句柄
2. **内存预分配**: 预分配BATCH_SIZE个记录缓冲区
3. **文件打开**: 打开CSV文件进行读取
4. **计时开始**: 记录加载开始时间

## 4. 批量处理核心算法

### 4.1 主处理流程
**位置**: `src/execution/executor_load.h:246-259`

```cpp
std::unique_ptr<RmRecord> Next() override {
    if (processing_complete_) {
        return nullptr;
    }
    
    try {
        // 批量处理所有数据
        while (process_batch()) {
            // 继续处理下一批
        }
        processing_complete_ = true;
        
        // 输出性能统计
        auto end_time = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time_);
        std::cout << "Load completed in " << duration.count() << " ms" << std::endl;
        
    } catch (const std::exception& e) {
        throw;
    }

    return nullptr;
}
```

### 4.2 批量处理实现
**位置**: `src/execution/executor_load.h:227-244`

```cpp
bool process_batch() {
    std::string line;
    batch_count_ = 0;
    
    // 读取一批数据
    while (batch_count_ < BATCH_SIZE && std::getline(csv_file_, line)) {
        if (line.empty()) continue;
        
        // 解析CSV行到记录缓冲区
        if (parse_csv_line_fast(line.c_str(), line.length(), batch_records_[batch_count_].get())) {
            batch_count_++;
        }
    }
    
    // 如果读取到数据，进行批量插入
    if (batch_count_ > 0) {
        batch_insert_records();
        return true;
    }
    
    return false;  // 没有更多数据
}
```

**处理逻辑**:
1. **批量读取**: 一次读取BATCH_SIZE行数据
2. **CSV解析**: 将每行解析为数据库记录格式
3. **批量插入**: 将整批记录插入数据库
4. **循环处理**: 直到文件结束

### 4.3 批量插入实现
**位置**: `src/execution/executor_load.h:185-205`

```cpp
void batch_insert_records() {
    if (batch_count_ == 0) return;

    batch_rids_.clear();
    batch_rids_.reserve(batch_count_);

    // 批量插入记录到数据文件
    for (size_t i = 0; i < batch_count_; ++i) {
        Rid rid = fh_->insert_record(batch_records_[i].get(), nullptr);
        batch_rids_.push_back(rid);
    }

    // 批量更新索引
    batch_update_indexes();

    // 更新最后插入的RID
    if (!batch_rids_.empty()) {
        rid_ = batch_rids_.back();
    }
    
    batch_count_ = 0;  // 重置批次计数
}
```

**插入流程**:
1. **记录插入**: 将所有记录插入到数据文件
2. **RID收集**: 收集插入后的记录ID
3. **索引更新**: 批量更新所有相关索引
4. **状态重置**: 重置批次状态

### 4.4 批量索引更新
**位置**: `src/execution/executor_load.h:208-224`

```cpp
void batch_update_indexes() {
    for (const auto& index : tab_.indexes) {
        // 为当前批次的所有记录更新此索引
        const std::string index_name = sm_manager_->get_ix_manager()->get_index_name(tab_name_, index.cols);
        auto* ih = sm_manager_->ihs_.at(index_name).get();
        
        for (size_t i = 0; i < batch_count_; ++i) {
            // 构造索引键
            auto key = std::make_unique<char[]>(index.col_tot_len);
            int offset = 0;
            
            for (int j = 0; j < index.col_num; ++j) {
                const auto& col = index.cols[j];
                memcpy(key.get() + offset, batch_records_[i].get() + col.offset, col.len);
                offset += col.len;
            }
            
            // 插入索引项
            ih->insert_entry(key.get(), batch_rids_[i], context_->txn_);
        }
    }
}
```

**索引更新逻辑**:
1. **遍历索引**: 处理表上的所有索引
2. **键构造**: 为每条记录构造索引键
3. **批量插入**: 将索引项批量插入到索引文件
4. **事务支持**: 在事务上下文中执行

## 5. 高性能CSV解析

### 5.1 快速解析算法
```cpp
bool parse_csv_line_fast(const char* line, size_t line_len, char* record_buf) {
    const char* ptr = line;
    const char* end = line + line_len;
    size_t col_idx = 0;
    
    while (ptr < end && col_idx < tab_.cols.size()) {
        const ColMeta& col = tab_.cols[col_idx];
        char* dest = record_buf + col.offset;
        
        // 查找下一个逗号或行尾
        const char* field_end = ptr;
        while (field_end < end && *field_end != ',' && *field_end != '\n' && *field_end != '\r') {
            field_end++;
        }
        
        // 解析字段值
        if (!parse_field_value(ptr, field_end - ptr, dest, col)) {
            return false;
        }
        
        // 移动到下一个字段
        ptr = field_end;
        if (ptr < end && *ptr == ',') {
            ptr++;  // 跳过逗号
        }
        col_idx++;
    }
    
    return col_idx == tab_.cols.size();  // 确保所有列都被解析
}
```

### 5.2 字段值解析
```cpp
bool parse_field_value(const char* field_start, size_t field_len, char* dest, const ColMeta& col) {
    switch (col.type) {
        case TYPE_INT: {
            // 快速整数解析
            int value = 0;
            bool negative = false;
            const char* ptr = field_start;
            
            if (field_len > 0 && *ptr == '-') {
                negative = true;
                ptr++;
                field_len--;
            }
            
            for (size_t i = 0; i < field_len; ++i) {
                if (ptr[i] < '0' || ptr[i] > '9') {
                    return false;
                }
                value = value * 10 + (ptr[i] - '0');
            }
            
            if (negative) value = -value;
            *(int*)dest = value;
            break;
        }
        
        case TYPE_FLOAT: {
            // 浮点数解析
            std::string field_str(field_start, field_len);
            float value = std::stof(field_str);
            *(float*)dest = value;
            break;
        }
        
        case TYPE_STRING: {
            // 字符串解析（处理引号）
            const char* start = field_start;
            size_t len = field_len;
            
            // 移除首尾引号
            if (len >= 2 && start[0] == '\'' && start[len-1] == '\'') {
                start++;
                len -= 2;
            }
            
            // 确保不超过列长度
            if (len > col.len) {
                len = col.len;
            }
            
            memcpy(dest, start, len);
            // 填充剩余空间
            if (len < col.len) {
                memset(dest + len, 0, col.len - len);
            }
            break;
        }
        
        default:
            return false;
    }
    
    return true;
}
```

**解析优化**:
- **避免字符串拷贝**: 直接在原始缓冲区上操作
- **快速整数解析**: 手工实现避免标准库开销
- **内存对齐**: 直接写入目标内存位置

## 6. 内存管理优化

### 6.1 预分配策略
```cpp
// 构造函数中的预分配
batch_records_.reserve(BATCH_SIZE);
for (size_t i = 0; i < BATCH_SIZE; ++i) {
    batch_records_.emplace_back(std::make_unique<char[]>(tab_.get_col_tot_len()));
}

batch_rids_.reserve(BATCH_SIZE);
```

**优化效果**:
- **避免动态分配**: 预分配所有需要的内存
- **减少内存碎片**: 连续分配大块内存
- **提高缓存局部性**: 数据在内存中连续存储

### 6.2 缓冲区重用
```cpp
void reset_batch() {
    batch_count_ = 0;
    // 不释放内存，直接重用缓冲区
    // batch_records_保持不变，下次直接使用
}
```

## 7. 性能监控与统计

### 7.1 性能计时
```cpp
// 开始计时
start_time_ = std::chrono::steady_clock::now();

// 结束计时并输出
auto end_time = std::chrono::steady_clock::now();
auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time_);
std::cout << "Load completed in " << duration.count() << " ms" << std::endl;
```

### 7.2 吞吐量统计
```cpp
void print_performance_stats() {
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::steady_clock::now() - start_time_);
    
    if (duration.count() > 0) {
        double records_per_second = (total_records_ * 1000.0) / duration.count();
        std::cout << "Loaded " << total_records_ << " records in " 
                  << duration.count() << " ms" << std::endl;
        std::cout << "Throughput: " << records_per_second << " records/second" << std::endl;
    }
}
```

## 8. 错误处理与恢复

### 8.1 文件错误处理
```cpp
// 文件打开检查
csv_file_.open(file_name_);
if (!csv_file_.is_open()) {
    throw RMDBError("Cannot open file: " + file_name_);
}

// 读取错误检查
if (csv_file_.bad()) {
    throw RMDBError("Error reading file: " + file_name_);
}
```

### 8.2 数据格式错误处理
```cpp
bool parse_csv_line_safe(const char* line, size_t line_len, char* record_buf) {
    try {
        return parse_csv_line_fast(line, line_len, record_buf);
    } catch (const std::exception& e) {
        std::cerr << "Error parsing line: " << std::string(line, line_len) << std::endl;
        std::cerr << "Error: " << e.what() << std::endl;
        return false;  // 跳过错误行
    }
}
```

### 8.3 事务回滚支持
```cpp
void rollback_batch() {
    // 如果批量插入失败，回滚已插入的记录
    for (const auto& rid : batch_rids_) {
        fh_->delete_record(rid, context_);
    }
    batch_rids_.clear();
}
```

## 9. 性能优化技巧

### 9.1 I/O优化
- **批量读取**: 减少系统调用次数
- **缓冲区大小**: 优化文件读取缓冲区
- **顺序访问**: 利用磁盘顺序读取优势

### 9.2 CPU优化
- **避免字符串操作**: 直接操作字符数组
- **减少函数调用**: 内联关键函数
- **分支预测**: 优化条件判断顺序

### 9.3 内存优化
- **预分配**: 避免运行时内存分配
- **对齐访问**: 确保内存对齐提高访问速度
- **缓存友好**: 优化数据结构布局

## 10. 调试与测试

### 10.1 性能测试
```bash
# 测试大文件加载
time ./bin/rmdb testdb < load_commands.sql

# 监控内存使用
valgrind --tool=massif ./bin/rmdb testdb < load_commands.sql
```

### 10.2 正确性验证
```sql
-- 加载后验证记录数
LOAD data.csv INTO test_table;
SELECT COUNT(*) FROM test_table;

-- 验证数据完整性
SELECT * FROM test_table LIMIT 10;
```

### 10.3 性能基准
- **TPC-C标准**: 符合TPC-C基准测试要求
- **吞吐量目标**: 达到每秒数万条记录的加载速度
- **内存效率**: 控制内存使用在合理范围内

这个实现提供了高性能的数据加载功能，通过批量处理、内存优化和快速解析等技术，能够高效处理大规模数据导入任务。
