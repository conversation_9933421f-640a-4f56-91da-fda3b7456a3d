# 数据库管理系统设计赛 决赛答辩

参赛学校：西北工业大学

队伍名称：啊对对队

指导老师：李宁 潘巍

队伍成员：沈阳 丁榕 张凯伦

时间：2024.8.19

汇报人：沈阳

## 目录 CONTENTS

01 比赛安排

02 功能创新点

03 优化实现

04 难点及解决方案

05 总结

## 01 比赛安排



*   **初赛**：完成情况为第十题两个测试点未通过，成绩 95.95，排名 14/114

*   **决赛**：完成功能题目，通过决赛测试，成绩 15.1086，排名 15/16

*   **训练赛**：流程包括学习、讨论、分工、开发、合并分支、提交



| 文件类型    | 源代码行数     | 增加代码行数    |
| ------- | --------- | --------- |
| .c      | 3931      | 3886      |
| .cpp    | 6004      | 3608      |
| .h      | 16520     | 4428      |
| .l      | 125       | 20        |
| .y      | 346       | 141       |
| .sh     | 109       | 739       |
| **总行数** | **27035** | **12822** |

## 02 功能创新点

### 2.1 聚合函数与分组统计



1.  减少扫描次数

2.  减少内存占用

### 2.2 不相关子查询



1.  设计递归执行子查询并存储中间结果

2.  IN 谓词后的集合支持不同类型

### 2.3 归并连接算法



1.  利用磁盘实现记录的外部排序

2.  尽可能大地增加初始归并段长以减少访问磁盘的次数，同时尽可能减少文件头的定位次数

### 2.4 间隙锁



1.  利用索引查找的上下界构造锁数据

2.  构造锁相容矩阵快速判断锁是否相容

3.  FIFO 策略

## 03 优化实现

### 决赛代码性能火焰图

（火焰图内容：包含 kernel.kallsyms、pthread\_mutex\_unlock、BufferPoolManager 相关操作、各类 Executor 执行过程等）

### 优化思路

#### 3.1 缓冲区优化



*   **缓冲区表分区**：将缓冲区表分为 128 个分区，每个 PageTag 通过自定义散列函数映射到一个分区；每个分区维护从 PageTag 到 PageID 的哈希表，且有独占的分区锁（支持独占和共享模式），极大提高访问并发度。

*   **缓存替换算法优化为 2Q 算法**：


    *   原理：2Q算法维护两个缓存队列，一个是FIFO队列，一个是LRU队列

        1.  新访问的数据插入到 FIFO 队列

        2.  若数据在 FIFO 队列中未被再次访问，按 FIFO 规则淘汰

        3.  若数据在 FIFO 队列中被再次访问，移到 LRU 队列头部

        4.  若数据在 LRU 队列再次被访问，移到 LRU 队列头部

        5.  LRU 队列淘汰末尾的数据

#### 3.2 SELECT 逻辑优化（谓词下推）



*   原理：将查询语句中的过滤表达式计算尽可能下推到距离数据源最近的地方，尽早过滤数据，减少数据传输或计算开销。



| 执行步骤                   | 修改前                  | 修改后              |
| ---------------------- | -------------------- | ---------------- |
| SeqScan Executor       | 选择所有行传递至上层算子         | 仅需传递满足 t.id=1 的行 |
| SortMergeJoin Executor | 归并排序                 | 归并排序             |
| Projection Executor    | t.id=d.id 的行做笛卡尔积和投影 | 做笛卡尔积和投影         |

#### 3.3 LOAD 优化

将读取的数据整页插入数据文件而非调用 insert\_record 逐条插入

#### 3.4 COUNT (\*) 优化



*   原理：当 COUNT (\*) 语句后无 WHERE 子句时，无需进入算子，直接通过数据文件的页头读取每个页中存放的记录数累加求和。

*   流程：

1.  执行聚合函数 COUNT

2.  判断是否有条件：

    *   无：调用数据文件接口读取每页记录数 → 返回结果

    *   有：进入底层算子遍历记录 → 返回结果

## 04 难点及解决方案

### 4.1 初赛难点



*   难点：知识储备不足；黑盒测试难以定位问题

*   解决方案：学习理论知识；构造经典场景测例；Code Review 讨论解决代码逻辑问题

### 4.2 内存占用



*   难点：有时内存占用过大导致系统崩溃

*   解决方案：减少中间数据的存储；使用 valgrind 工具监测内存泄露；尽可能使用智能指针

### 4.3 系统并发度



*   难点：并发度过低

*   解决方案：使用细粒度锁，减少共享资源范围

### 4.4 模拟 TPCC 场景



*   难点：决赛采用黑盒测试，测评机器上获得的报错信息很少

*   解决方案：实现多线程脚本模拟运行 TPCC，在本地定位代码中的问题并调试修改

## 05 总结

### 最终结果



*   决赛 tpc-x：15.1086

*   排名：15/16

### 比赛收获



*   理论知识深化

*   编程能力提升

*   工具使用熟练

*   学习团队合作

### 未来展望



*   实现完整的功能要求

*   实现 order\_by 算子同时支持外部排序和内部排序

*   下推投影算子，减小数据传递的规模

敬请批评指正！

