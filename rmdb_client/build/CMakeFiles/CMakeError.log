Performing C SOURCE FILE Test CMAKE_HAVE_LIBC_PTHREAD failed with the following output:
Change Dir: /home/<USER>/db2025/db2025-x1/rmdb_client/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_372ce/fast && /usr/bin/make -f CMakeFiles/cmTC_372ce.dir/build.make CMakeFiles/cmTC_372ce.dir/build
make[1]: Entering directory '/home/<USER>/db2025/db2025-x1/rmdb_client/build/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_372ce.dir/src.c.o
/usr/bin/cc   -DCMAKE_HAVE_LIBC_PTHREAD   -o CMakeFiles/cmTC_372ce.dir/src.c.o   -c /home/<USER>/db2025/db2025-x1/rmdb_client/build/CMakeFiles/CMakeTmp/src.c
Linking C executable cmTC_372ce
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_372ce.dir/link.txt --verbose=1
/usr/bin/cc  -DCMAKE_HAVE_LIBC_PTHREAD    CMakeFiles/cmTC_372ce.dir/src.c.o  -o cmTC_372ce 
/usr/bin/ld: CMakeFiles/cmTC_372ce.dir/src.c.o: in function `main':
src.c:(.text+0x46): undefined reference to `pthread_create'
/usr/bin/ld: src.c:(.text+0x52): undefined reference to `pthread_detach'
/usr/bin/ld: src.c:(.text+0x63): undefined reference to `pthread_join'
collect2: error: ld returned 1 exit status
make[1]: *** [CMakeFiles/cmTC_372ce.dir/build.make:87: cmTC_372ce] Error 1
make[1]: Leaving directory '/home/<USER>/db2025/db2025-x1/rmdb_client/build/CMakeFiles/CMakeTmp'
make: *** [Makefile:121: cmTC_372ce/fast] Error 2


Source file was:
#include <pthread.h>

void* test_func(void* data)
{
  return data;
}

int main(void)
{
  pthread_t thread;
  pthread_create(&thread, NULL, test_func, NULL);
  pthread_detach(thread);
  pthread_join(thread, NULL);
  pthread_atfork(NULL, NULL, NULL);
  pthread_exit(NULL);

  return 0;
}

Determining if the function pthread_create exists in the pthreads failed with the following output:
Change Dir: /home/<USER>/db2025/db2025-x1/rmdb_client/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_460cf/fast && /usr/bin/make -f CMakeFiles/cmTC_460cf.dir/build.make CMakeFiles/cmTC_460cf.dir/build
make[1]: Entering directory '/home/<USER>/db2025/db2025-x1/rmdb_client/build/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_460cf.dir/CheckFunctionExists.c.o
/usr/bin/cc   -DCHECK_FUNCTION_EXISTS=pthread_create   -o CMakeFiles/cmTC_460cf.dir/CheckFunctionExists.c.o   -c /usr/share/cmake-3.16/Modules/CheckFunctionExists.c
Linking C executable cmTC_460cf
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_460cf.dir/link.txt --verbose=1
/usr/bin/cc  -DCHECK_FUNCTION_EXISTS=pthread_create    CMakeFiles/cmTC_460cf.dir/CheckFunctionExists.c.o  -o cmTC_460cf  -lpthreads 
/usr/bin/ld: cannot find -lpthreads
collect2: error: ld returned 1 exit status
make[1]: *** [CMakeFiles/cmTC_460cf.dir/build.make:87: cmTC_460cf] Error 1
make[1]: Leaving directory '/home/<USER>/db2025/db2025-x1/rmdb_client/build/CMakeFiles/CMakeTmp'
make: *** [Makefile:121: cmTC_460cf/fast] Error 2



